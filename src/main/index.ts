import { app, <PERSON><PERSON>erWindow, ipc<PERSON><PERSON>, <PERSON>u } from 'electron';
import { join } from 'path';
import { isDev } from './utils/environment';
import { DatabaseConnection } from './database/connection';
import { CryptographyService } from './auth/crypto.service';
import { MCPService } from './mcp/service';
import { authService } from './auth/auth.service';
import { todoDAO } from './dao/todo.dao';
import { categoryDAO } from './dao/category.dao';
import { createAPIResponse } from './api';
import { TodoValidationError, TodoDatabaseError, TodoBusinessError } from '@shared/types';
import { syncManager } from './services/sync-manager.service';
import log from 'electron-log';

// Configure logging
log.transports.file.level = 'info';
log.transports.console.level = 'debug';

/**
 * Enhanced error handling for IPC operations
 */
function handleIpcError(error: unknown, operation: string): any {
  log.error(`${operation} error:`, error);

  if (error instanceof TodoValidationError) {
    // Include validation details in the error message for better user feedback
    const errorMessage = error.getUserMessage();
    const detailedMessage = error.validationErrors.length > 0
      ? `${errorMessage} (${error.code})`
      : errorMessage;
    return createAPIResponse(false, null, detailedMessage);
  }

  if (error instanceof TodoDatabaseError) {
    const errorMessage = error.getUserMessage();
    const detailedMessage = `${errorMessage} (${error.code})`;
    return createAPIResponse(false, null, detailedMessage);
  }

  if (error instanceof TodoBusinessError) {
    const detailedMessage = `${error.message} (${error.code})`;
    return createAPIResponse(false, null, detailedMessage);
  }

  // Generic error handling
  const message = error instanceof Error ? error.message : 'Unknown error occurred';
  return createAPIResponse(false, null, message);
}

class ModernTodoApp {
  private mainWindow: BrowserWindow | null = null;
  private databaseService: DatabaseConnection | null = null;
  private securityService: CryptographyService | null = null;
  private mcpService: MCPService | null = null;

  constructor() {
    this.setupApp();
    this.setupServices();
    this.setupIpcHandlers();
  }

  private setupApp(): void {
    // Set app user model id for Windows
    app.setAppUserModelId('com.moderntodo.app');

    // Handle app events
    app.whenReady().then(() => {
      this.createMainWindow();
      this.setupMenu();
      
      app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
          this.createMainWindow();
        }
      });
    });

    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    app.on('before-quit', async () => {
      await this.cleanup();
    });
  }

  private async setupServices(): Promise<void> {
    try {
      // Initialize core services
      this.securityService = new CryptographyService();
      this.databaseService = DatabaseConnection.getInstance();
      this.mcpService = MCPService.getInstance();

      // Initialize services in order
      await this.databaseService.initialize();
      log.info('Database service initialized');

      await this.mcpService.initialize();
      log.info('MCP service initialized');

      // Initialize sync manager (only if MCP is connected)
      if (this.mcpService.isConnected()) {
        await syncManager.initialize();
        log.info('Sync manager initialized');
      }

    } catch (error) {
      log.error('Failed to initialize services:', error);
      app.quit();
    }
  }

  private createMainWindow(): void {
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      show: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: join(__dirname, '../preload/index.js'),
        webSecurity: true,
        allowRunningInsecureContent: false,
      },
      titleBarStyle: 'hiddenInset',
      vibrancy: 'under-window', // macOS glassmorphism effect
      backgroundMaterial: 'acrylic', // Windows 11 glassmorphism
      transparent: true,
      frame: false,
    });

    // Load the renderer
    if (isDev()) {
      this.mainWindow.loadURL('http://localhost:5173');
      this.mainWindow.webContents.openDevTools();
    } else {
      this.mainWindow.loadFile(join(__dirname, '../renderer/index.html'));
    }

    // Show window when ready
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow?.show();
      
      if (isDev()) {
        this.mainWindow?.webContents.openDevTools();
      }
    });

    // Handle window closed
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });
  }

  private setupMenu(): void {
    const template: Electron.MenuItemConstructorOptions[] = [
      {
        label: 'File',
        submenu: [
          {
            label: 'New Todo',
            accelerator: 'CmdOrCtrl+N',
            click: () => {
              this.mainWindow?.webContents.send('menu-new-todo');
            },
          },
          { type: 'separator' },
          {
            label: 'Import',
            accelerator: 'CmdOrCtrl+I',
            click: () => {
              this.mainWindow?.webContents.send('menu-import');
            },
          },
          {
            label: 'Export',
            accelerator: 'CmdOrCtrl+E',
            click: () => {
              this.mainWindow?.webContents.send('menu-export');
            },
          },
          { type: 'separator' },
          {
            label: 'Quit',
            accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
            click: () => {
              app.quit();
            },
          },
        ],
      },
      {
        label: 'Edit',
        submenu: [
          { role: 'undo' },
          { role: 'redo' },
          { type: 'separator' },
          { role: 'cut' },
          { role: 'copy' },
          { role: 'paste' },
          { role: 'selectAll' },
        ],
      },
      {
        label: 'View',
        submenu: [
          { role: 'reload' },
          { role: 'forceReload' },
          { role: 'toggleDevTools' },
          { type: 'separator' },
          { role: 'resetZoom' },
          { role: 'zoomIn' },
          { role: 'zoomOut' },
          { type: 'separator' },
          { role: 'togglefullscreen' },
        ],
      },
      {
        label: 'Window',
        submenu: [
          { role: 'minimize' },
          { role: 'close' },
        ],
      },
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
  }

  private setupIpcHandlers(): void {
    // Database operations
    ipcMain.handle('db:query', async (event: any, sql: string, params?: any[]) => {
      try {
        return await this.databaseService?.executeQuery(sql, params);
      } catch (error) {
        log.error('Database query error:', error);
        throw error;
      }
    });

    ipcMain.handle('db:transaction', async (event: any, operations: any[]) => {
      try {
        return await this.databaseService?.executeTransaction(async (execute) => {
          const results = [];
          for (const op of operations) {
            results.push(await execute(op.query, op.params));
          }
          return results;
        });
      } catch (error) {
        log.error('Database transaction error:', error);
        throw error;
      }
    });

    // Security operations
    ipcMain.handle('security:encrypt', async (event: any, data: string, password: string) => {
      try {
        return await this.securityService?.encryptData(data, password);
      } catch (error) {
        log.error('Encryption error:', error);
        throw error;
      }
    });

    ipcMain.handle('security:decrypt', async (event: any, encryptedData: any, password: string) => {
      try {
        return await this.securityService?.decryptData(encryptedData, password);
      } catch (error) {
        log.error('Decryption error:', error);
        throw error;
      }
    });

    // MCP operations
    ipcMain.handle('mcp:sync', async (event: any, data: any) => {
      try {
        return await this.mcpService?.executeQuery(data.sql, data.params);
      } catch (error) {
        log.error('MCP sync error:', error);
        throw error;
      }
    });

    // System operations
    ipcMain.handle('system:getInfo', async () => {
      return {
        platform: process.platform,
        arch: process.arch,
        version: app.getVersion(),
        electronVersion: process.versions.electron,
        nodeVersion: process.versions.node,
      };
    });

    ipcMain.handle('app:quit', () => {
      app.quit();
    });

    ipcMain.handle('app:minimize', () => {
      this.mainWindow?.minimize();
    });

    ipcMain.handle('app:maximize', () => {
      if (this.mainWindow?.isMaximized()) {
        this.mainWindow.unmaximize();
      } else {
        this.mainWindow?.maximize();
      }
    });

    ipcMain.handle('app:close', () => {
      this.mainWindow?.close();
    });

    // Todo operations
    ipcMain.handle('todos:getAll', async (event: any, sessionId: string, filters?: any, pagination?: any) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const result = await todoDAO.findByUserId(session.userId, pagination, filters);
        return createAPIResponse(true, result);
      } catch (error) {
        log.error('Get todos error:', error);
        return createAPIResponse(false, null, error instanceof Error ? error.message : 'Unknown error');
      }
    });

    ipcMain.handle('todos:create', async (event: any, sessionId: string, todoData: any) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const todo = await todoDAO.createTodo(todoData, session.userId);
        return createAPIResponse(true, todo);
      } catch (error) {
        return handleIpcError(error, 'Create todo');
      }
    });

    ipcMain.handle('todos:update', async (event: any, sessionId: string, todoId: string, updates: any) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const todo = await todoDAO.updateTodo(todoId, updates, session.userId);
        return createAPIResponse(true, todo);
      } catch (error) {
        return handleIpcError(error, 'Update todo');
      }
    });

    ipcMain.handle('todos:delete', async (event: any, sessionId: string, todoId: string) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const todo = await todoDAO.softDeleteTodo(todoId, session.userId);
        return createAPIResponse(true, todo);
      } catch (error) {
        return handleIpcError(error, 'Delete todo');
      }
    });

    ipcMain.handle('todos:updateStatus', async (event: any, sessionId: string, todoId: string, status: string) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const todo = await todoDAO.updateStatus(todoId, status as any, session.userId);
        return createAPIResponse(true, todo);
      } catch (error) {
        return handleIpcError(error, 'Update todo status');
      }
    });

    // Category operations
    ipcMain.handle('categories:getAll', async (event: any, sessionId: string) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const categories = await categoryDAO.findByUserId(session.userId);
        return createAPIResponse(true, categories);
      } catch (error) {
        return handleIpcError(error, 'Get categories');
      }
    });

    ipcMain.handle('categories:create', async (event: any, sessionId: string, categoryData: any) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const category = await categoryDAO.createCategory(
          session.userId,
          categoryData.name,
          categoryData.color,
          categoryData.icon,
          categoryData.is_default
        );
        return createAPIResponse(true, category);
      } catch (error) {
        return handleIpcError(error, 'Create category');
      }
    });

    ipcMain.handle('categories:update', async (event: any, sessionId: string, categoryId: string, updates: any) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const category = await categoryDAO.updateCategory(categoryId, session.userId, updates);
        return createAPIResponse(true, category);
      } catch (error) {
        return handleIpcError(error, 'Update category');
      }
    });

    ipcMain.handle('categories:delete', async (event: any, sessionId: string, categoryId: string) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const success = await categoryDAO.deleteCategory(categoryId, session.userId);
        return createAPIResponse(true, { success });
      } catch (error) {
        return handleIpcError(error, 'Delete category');
      }
    });

    ipcMain.handle('categories:reorder', async (event: any, sessionId: string, categoryOrders: any[]) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const categories = await categoryDAO.reorderCategories(session.userId, categoryOrders);
        return createAPIResponse(true, categories);
      } catch (error) {
        return handleIpcError(error, 'Reorder categories');
      }
    });

    // Tag operations
    ipcMain.handle('tags:getAll', async (event: any, sessionId: string) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const tags = await todoDAO.getAllTags(session.userId);
        return createAPIResponse(true, tags);
      } catch (error) {
        return handleIpcError(error, 'Get all tags');
      }
    });

    ipcMain.handle('tags:getSuggestions', async (event: any, sessionId: string, query: string, limit?: number) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const suggestions = await todoDAO.getTagSuggestions(session.userId, query, limit);
        return createAPIResponse(true, suggestions);
      } catch (error) {
        return handleIpcError(error, 'Get tag suggestions');
      }
    });

    ipcMain.handle('tags:getPopular', async (event: any, sessionId: string, limit?: number) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const popularTags = await todoDAO.getPopularTags(session.userId, limit);
        return createAPIResponse(true, popularTags);
      } catch (error) {
        return handleIpcError(error, 'Get popular tags');
      }
    });

    ipcMain.handle('tags:getRecent', async (event: any, sessionId: string, limit?: number) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const recentTags = await todoDAO.getRecentTags(session.userId, limit);
        return createAPIResponse(true, recentTags);
      } catch (error) {
        return handleIpcError(error, 'Get recent tags');
      }
    });

    ipcMain.handle('tags:getStats', async (event: any, sessionId: string, tag: string) => {
      try {
        const session = await authService.validateSession(sessionId);
        if (!session) {
          throw new Error('Invalid session');
        }

        const stats = await todoDAO.getTagStats(session.userId, tag);
        return createAPIResponse(true, stats);
      } catch (error) {
        return handleIpcError(error, 'Get tag stats');
      }
    });

    // Sync operations
    ipcMain.handle('sync:getStatus', async () => {
      try {
        const status = syncManager.getSyncStatus();
        return createAPIResponse(true, status);
      } catch (error) {
        return handleIpcError(error, 'Get sync status');
      }
    });

    ipcMain.handle('sync:getQueueStatus', async () => {
      try {
        const queueStatus = await syncManager.getQueueStatus();
        return createAPIResponse(true, queueStatus);
      } catch (error) {
        return handleIpcError(error, 'Get sync queue status');
      }
    });

    ipcMain.handle('sync:performSync', async () => {
      try {
        const result = await syncManager.performSync();
        return createAPIResponse(true, result);
      } catch (error) {
        return handleIpcError(error, 'Perform sync');
      }
    });

    ipcMain.handle('sync:forcePush', async () => {
      try {
        const result = await syncManager.forcePushChanges();
        return createAPIResponse(true, result);
      } catch (error) {
        return handleIpcError(error, 'Force push changes');
      }
    });

    ipcMain.handle('sync:forcePull', async () => {
      try {
        const result = await syncManager.forcePullChanges();
        return createAPIResponse(true, result);
      } catch (error) {
        return handleIpcError(error, 'Force pull changes');
      }
    });

    ipcMain.handle('sync:getConflicts', async () => {
      try {
        const conflicts = await syncManager.getConflicts();
        return createAPIResponse(true, conflicts);
      } catch (error) {
        return handleIpcError(error, 'Get sync conflicts');
      }
    });

    ipcMain.handle('sync:resolveConflict', async (event: any, conflictId: string, resolution: string, mergedData?: any) => {
      try {
        await syncManager.resolveConflict(conflictId, resolution as any, mergedData);
        return createAPIResponse(true, { success: true });
      } catch (error) {
        return handleIpcError(error, 'Resolve conflict');
      }
    });

    ipcMain.handle('sync:clearQueue', async () => {
      try {
        await syncManager.clearQueue();
        return createAPIResponse(true, { success: true });
      } catch (error) {
        return handleIpcError(error, 'Clear sync queue');
      }
    });

    ipcMain.handle('sync:getNetworkStatus', async () => {
      try {
        const networkStatus = syncManager.getNetworkStatus();
        return createAPIResponse(true, networkStatus);
      } catch (error) {
        return handleIpcError(error, 'Get network status');
      }
    });

    // Set up real-time sync event forwarding
    this.setupSyncEventForwarding();
  }

  private setupSyncEventForwarding(): void {
    // Forward sync status changes to renderer
    syncManager.on('statusChanged', (status: any) => {
      this.mainWindow?.webContents.send('sync-status-changed', status);
    });

    // Forward sync events
    syncManager.on('syncStarted', () => {
      this.mainWindow?.webContents.send('sync-event', { type: 'syncStarted' });
    });

    syncManager.on('syncCompleted', (result: any) => {
      this.mainWindow?.webContents.send('sync-event', { type: 'syncCompleted', data: result });
    });

    syncManager.on('syncError', (error: any) => {
      this.mainWindow?.webContents.send('sync-event', { type: 'syncError', data: { message: error.message } });
    });

    syncManager.on('conflictDetected', (conflict: any) => {
      this.mainWindow?.webContents.send('sync-event', { type: 'conflictDetected', data: conflict });
    });

    syncManager.on('operationQueued', (operation: any) => {
      this.mainWindow?.webContents.send('sync-event', { type: 'operationQueued', data: operation });
    });
  }

  private async cleanup(): Promise<void> {
    try {
      // Cleanup sync manager first
      await syncManager.destroy();

      await this.databaseService?.close();
      await this.mcpService?.disconnect();
      log.info('Application cleanup completed');
    } catch (error) {
      log.error('Cleanup error:', error);
    }
  }
}

// Create and initialize the application
new ModernTodoApp();
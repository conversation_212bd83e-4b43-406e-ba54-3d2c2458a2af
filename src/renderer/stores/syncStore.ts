import React from 'react';
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

export interface SyncStatus {
  status: 'idle' | 'syncing' | 'error' | 'offline';
  lastSyncAt: Date | null;
  pendingChanges: number;
  errorMessage?: string;
  syncProgress?: {
    current: number;
    total: number;
    operation: string;
  };
}

export interface SyncQueueStatus {
  totalOperations: number;
  pendingOperations: number;
  failedOperations: number;
  conflictOperations: number;
  isOnline: boolean;
  isSyncing: boolean;
}

export interface NetworkStatus {
  isOnline: boolean;
  connectionQuality: 'excellent' | 'good' | 'poor' | 'offline';
  latency: number;
  lastChecked: Date;
  consecutiveFailures: number;
}

export interface ConflictData {
  id: string;
  tableName: string;
  recordId: string;
  localData: any;
  remoteData: any;
  conflictType: 'update-update' | 'update-delete' | 'create-create' | 'no-conflict';
  recommendations: string[];
  createdAt: Date;
  resolved: boolean;
}

export interface SyncResult {
  success: boolean;
  processedOperations: number;
  conflicts: any[];
  errors: string[];
}

export interface SyncState {
  // Status
  syncStatus: SyncStatus;
  queueStatus: SyncQueueStatus;
  networkStatus: NetworkStatus | null;
  
  // Conflicts
  conflicts: ConflictData[];
  activeConflict: ConflictData | null;
  
  // UI State
  showSyncPanel: boolean;
  showConflictDialog: boolean;
  autoSyncEnabled: boolean;
  syncInterval: number;
  
  // Loading states
  isLoadingStatus: boolean;
  isLoadingConflicts: boolean;
  isResolvingConflict: boolean;
  
  // Actions
  loadSyncStatus: () => Promise<void>;
  loadQueueStatus: () => Promise<void>;
  loadNetworkStatus: () => Promise<void>;
  loadConflicts: () => Promise<void>;
  
  performSync: () => Promise<SyncResult>;
  forcePushChanges: () => Promise<SyncResult>;
  forcePullChanges: () => Promise<SyncResult>;
  
  resolveConflict: (conflictId: string, resolution: 'local' | 'remote' | 'merge', mergedData?: any) => Promise<void>;
  resolveConflictManually: (conflictId: string, resolvedData: any) => Promise<void>;
  
  clearSyncQueue: () => Promise<void>;
  
  // UI Actions
  toggleSyncPanel: () => void;
  showConflictResolution: (conflict: ConflictData) => void;
  hideConflictResolution: () => void;
  setAutoSync: (enabled: boolean) => void;
  setSyncInterval: (interval: number) => void;
  
  // Utility actions
  refreshAll: () => Promise<void>;
  clearErrors: () => void;
}

export const useSyncStore = create<SyncState>()(
  subscribeWithSelector(
    immer((set, get) => ({
      // Initial state
      syncStatus: {
        status: 'idle',
        lastSyncAt: null,
        pendingChanges: 0,
      },
      queueStatus: {
        totalOperations: 0,
        pendingOperations: 0,
        failedOperations: 0,
        conflictOperations: 0,
        isOnline: false,
        isSyncing: false,
      },
      networkStatus: null,
      conflicts: [],
      activeConflict: null,
      showSyncPanel: false,
      showConflictDialog: false,
      autoSyncEnabled: true,
      syncInterval: 30000,
      isLoadingStatus: false,
      isLoadingConflicts: false,
      isResolvingConflict: false,

      // Status loading actions
      loadSyncStatus: async () => {
        set((state) => {
          state.isLoadingStatus = true;
        });

        try {
          if (window.electronAPI?.sync) {
            const result = await window.electronAPI.sync.getStatus();
            if (result.success) {
              set((state) => {
                state.syncStatus = {
                  ...result.data,
                  lastSyncAt: result.data.lastSyncAt ? new Date(result.data.lastSyncAt) : null,
                };
              });
            }
          }
        } catch (error) {
          console.error('Failed to load sync status:', error);
        } finally {
          set((state) => {
            state.isLoadingStatus = false;
          });
        }
      },

      loadQueueStatus: async () => {
        try {
          if (window.electronAPI?.sync) {
            const result = await window.electronAPI.sync.getQueueStatus();
            if (result.success) {
              set((state) => {
                state.queueStatus = result.data;
              });
            }
          }
        } catch (error) {
          console.error('Failed to load queue status:', error);
        }
      },

      loadNetworkStatus: async () => {
        try {
          if (window.electronAPI?.sync) {
            const result = await window.electronAPI.sync.getNetworkStatus();
            if (result.success) {
              set((state) => {
                state.networkStatus = {
                  ...result.data,
                  lastChecked: new Date(result.data.lastChecked),
                };
              });
            }
          }
        } catch (error) {
          console.error('Failed to load network status:', error);
        }
      },

      loadConflicts: async () => {
        set((state) => {
          state.isLoadingConflicts = true;
        });

        try {
          if (window.electronAPI?.sync) {
            const result = await window.electronAPI.sync.getConflicts();
            if (result.success) {
              set((state) => {
                state.conflicts = result.data.map((conflict: any) => ({
                  ...conflict,
                  createdAt: new Date(conflict.created_at),
                  resolved: false,
                }));
              });
            }
          }
        } catch (error) {
          console.error('Failed to load conflicts:', error);
        } finally {
          set((state) => {
            state.isLoadingConflicts = false;
          });
        }
      },

      // Sync actions
      performSync: async () => {
        try {
          if (window.electronAPI?.sync) {
            const result = await window.electronAPI.sync.performSync();
            if (result.success) {
              // Refresh status after sync
              await get().loadSyncStatus();
              await get().loadQueueStatus();
              await get().loadConflicts();
              return result.data;
            }
            throw new Error(result.error || 'Sync failed');
          }
          throw new Error('Sync API not available');
        } catch (error) {
          console.error('Sync failed:', error);
          throw error;
        }
      },

      forcePushChanges: async () => {
        try {
          if (window.electronAPI?.sync) {
            const result = await window.electronAPI.sync.forcePush();
            if (result.success) {
              await get().refreshAll();
              return result.data;
            }
            throw new Error(result.error || 'Force push failed');
          }
          throw new Error('Sync API not available');
        } catch (error) {
          console.error('Force push failed:', error);
          throw error;
        }
      },

      forcePullChanges: async () => {
        try {
          if (window.electronAPI?.sync) {
            const result = await window.electronAPI.sync.forcePull();
            if (result.success) {
              await get().refreshAll();
              return result.data;
            }
            throw new Error(result.error || 'Force pull failed');
          }
          throw new Error('Sync API not available');
        } catch (error) {
          console.error('Force pull failed:', error);
          throw error;
        }
      },

      // Conflict resolution actions
      resolveConflict: async (conflictId: string, resolution: 'local' | 'remote' | 'merge', mergedData?: any) => {
        set((state) => {
          state.isResolvingConflict = true;
        });

        try {
          if (window.electronAPI?.sync) {
            const result = await window.electronAPI.sync.resolveConflict(conflictId, resolution, mergedData);
            if (result.success) {
              // Remove resolved conflict from state
              set((state) => {
                state.conflicts = state.conflicts.filter(c => c.id !== conflictId);
                if (state.activeConflict?.id === conflictId) {
                  state.activeConflict = null;
                  state.showConflictDialog = false;
                }
              });
              
              // Refresh status
              await get().loadSyncStatus();
              await get().loadQueueStatus();
            } else {
              throw new Error(result.error || 'Failed to resolve conflict');
            }
          }
        } catch (error) {
          console.error('Failed to resolve conflict:', error);
          throw error;
        } finally {
          set((state) => {
            state.isResolvingConflict = false;
          });
        }
      },

      resolveConflictManually: async (conflictId: string, resolvedData: any) => {
        await get().resolveConflict(conflictId, 'merge', resolvedData);
      },

      clearSyncQueue: async () => {
        try {
          if (window.electronAPI?.sync) {
            const result = await window.electronAPI.sync.clearQueue();
            if (result.success) {
              await get().loadQueueStatus();
            }
          }
        } catch (error) {
          console.error('Failed to clear sync queue:', error);
          throw error;
        }
      },

      // UI Actions
      toggleSyncPanel: () => {
        set((state) => {
          state.showSyncPanel = !state.showSyncPanel;
        });
      },

      showConflictResolution: (conflict: ConflictData) => {
        set((state) => {
          state.activeConflict = conflict;
          state.showConflictDialog = true;
        });
      },

      hideConflictResolution: () => {
        set((state) => {
          state.activeConflict = null;
          state.showConflictDialog = false;
        });
      },

      setAutoSync: (enabled: boolean) => {
        set((state) => {
          state.autoSyncEnabled = enabled;
        });
      },

      setSyncInterval: (interval: number) => {
        set((state) => {
          state.syncInterval = interval;
        });
      },

      // Utility actions
      refreshAll: async () => {
        await Promise.all([
          get().loadSyncStatus(),
          get().loadQueueStatus(),
          get().loadNetworkStatus(),
          get().loadConflicts(),
        ]);
      },

      clearErrors: () => {
        set((state) => {
          state.syncStatus.errorMessage = undefined;
        });
      },
    }))
  )
);

// Hook for sync status with auto-refresh
export const useSyncStatus = (autoRefresh = true) => {
  const store = useSyncStore();
  
  React.useEffect(() => {
    if (autoRefresh) {
      store.refreshAll();
      
      const interval = setInterval(() => {
        store.loadSyncStatus();
        store.loadQueueStatus();
        store.loadNetworkStatus();
      }, 5000); // Refresh every 5 seconds
      
      return () => clearInterval(interval);
    }
  }, [autoRefresh, store]);
  
  return store;
};

// Hook for conflict management
export const useConflictResolution = () => {
  const store = useSyncStore();
  
  return {
    conflicts: store.conflicts,
    activeConflict: store.activeConflict,
    showConflictDialog: store.showConflictDialog,
    isResolvingConflict: store.isResolvingConflict,
    showConflictResolution: store.showConflictResolution,
    hideConflictResolution: store.hideConflictResolution,
    resolveConflict: store.resolveConflict,
    resolveConflictManually: store.resolveConflictManually,
    loadConflicts: store.loadConflicts,
  };
};

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Grid, List, BarChart3, RefreshCw } from 'lucide-react';
import { Todo } from '@shared/types';
import { useTodoStore, useUIStore } from '@renderer/stores';
import { TodoSearchBar } from './TodoSearchBar';
import { TodoFilterPanel } from './TodoFilterPanel';
import { TodoSortControls } from './TodoSortControls';
import { VirtualizedTodoList } from './VirtualizedTodoList';
import { BulkActionToolbar } from './BulkActionToolbar';
import { useDebounce } from '@renderer/hooks/useDebounce';
import { useTodoSorting } from '@renderer/hooks/useTodoSorting';
import { useTodoFilters } from '@renderer/hooks/useTodoFilters';
import { useBulkOperations } from '@renderer/hooks/useBulkOperations';

import type { ViewMode } from '@renderer/stores';

export interface TodoListProps {
  containerHeight?: number;
  className?: string;
}

export const TodoList: React.FC<TodoListProps> = ({
  containerHeight = 600,
  className = ''
}) => {
  // Zustand stores
  const {
    todos,
    categories,
    isLoading,
    error,
    filters,
    selectedTodos,
    loadTodos,
    loadCategories,
    updateTodo,
    deleteTodo,
    setSearchQuery,
    setFilters,
    selectTodo,
    deselectTodo,
    clearSelection
  } = useTodoStore();

  const {
    todoViewMode: viewMode,
    setTodoViewMode: setViewMode
  } = useUIStore();

  // Local UI state
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  // Debounced search
  const debouncedSearchQuery = useDebounce(filters.searchQuery, 300);

  // Callback functions (defined early to avoid hoisting issues)
  const handleTodoUpdate = useCallback(async (updatedTodo: Todo) => {
    try {
      await updateTodo(updatedTodo.id, updatedTodo);
    } catch (error) {
      console.error('Failed to update todo:', error);
    }
  }, [updateTodo]);

  const handleTodoDelete = useCallback(async (todoId: string) => {
    try {
      await deleteTodo(todoId);
    } catch (error) {
      console.error('Failed to delete todo:', error);
    }
  }, [deleteTodo]);

  const handleBulkTodosUpdate = useCallback(async (updatedTodos: Todo[]) => {
    try {
      // Update each todo individually
      for (const todo of updatedTodos) {
        await updateTodo(todo.id, todo);
      }
    } catch (error) {
      console.error('Failed to bulk update todos:', error);
    }
  }, [updateTodo]);

  // Custom hooks for advanced functionality
  const filtering = useTodoFilters(todos, categories);
  const sorting = useTodoSorting(filtering.filteredTodos);
  const bulkOps = useBulkOperations(todos, handleBulkTodosUpdate, handleTodoDelete);

  // Note: Using filtering.filteredTodos and sorting.sortedTodos from hooks instead of store

  // Load todos and categories on mount
  useEffect(() => {
    loadTodos();
    loadCategories();
  }, [loadTodos, loadCategories]);

  // Update search filter when debounced query changes
  useEffect(() => {
    setSearchQuery(debouncedSearchQuery);
  }, [debouncedSearchQuery, setSearchQuery]);

  // Handle refresh
  const handleRefresh = useCallback(async () => {
    try {
      await loadTodos();
      setLastRefresh(new Date());
    } catch (error) {
      console.error('Failed to refresh todos:', error);
    }
  }, [loadTodos]);

  // Get search suggestions from existing todos
  const searchSuggestions = useMemo(() => {
    const suggestions = new Set<string>();
    todos.forEach(todo => {
      // Add title words
      todo.title.split(' ').forEach(word => {
        if (word.length > 2) suggestions.add(word);
      });
      // Add tags
      todo.tags.forEach(tag => suggestions.add(tag));
    });
    return Array.from(suggestions).slice(0, 10);
  }, [todos]);

  // Handle view mode changes
  const handleViewModeChange = useCallback((mode: ViewMode) => {
    setViewMode(mode);
  }, [setViewMode]);

  // Render loading state
  if (isLoading) {
    return (
      <div className={`w-full flex items-center justify-center ${className}`} style={{ height: containerHeight }}>
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-fa-blue-200 border-t-fa-blue-500 rounded-full animate-spin mx-auto mb-4" />
          <p className="fa-body text-fa-gray-500">Loading todos...</p>
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className={`w-full flex items-center justify-center ${className}`} style={{ height: containerHeight }}>
        <div className="text-center">
          <div className="text-5xl mb-4">⚠️</div>
          <h3 className="fa-heading-3 mb-2 text-fa-error">Error Loading Todos</h3>
          <p className="fa-body text-fa-gray-500 mb-4">{error}</p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={loadTodos}
            className="fa-button-primary px-4 py-2"
          >
            Try Again
          </motion.button>
        </div>
      </div>
    );
  }

  return (
    <div className={`w-full h-full flex flex-col ${className}`}>
      {/* Header Controls */}
      <div className="flex-shrink-0 space-y-4 mb-6">
        {/* Search and Filter Row */}
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <TodoSearchBar
              value={filters.searchQuery}
              onChange={setSearchQuery}
              onFilterToggle={() => setIsFilterPanelOpen(!isFilterPanelOpen)}
              suggestions={searchSuggestions}
              isFilterActive={Object.values(filters).some(f =>
                Array.isArray(f) ? f.length > 0 :
                typeof f === 'string' ? f !== '' :
                typeof f === 'object' && f !== null ? Object.keys(f).length > 0 : false
              )}
            />
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center space-x-1 fa-glass-panel px-2 py-1 rounded-lg">
            {(['list', 'grid', 'kanban'] as ViewMode[]).map((mode) => (
              <motion.button
                key={mode}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => handleViewModeChange(mode)}
                className={`p-2 rounded-lg transition-all duration-200 ${
                  viewMode === mode
                    ? 'bg-fa-blue-500 text-white'
                    : 'text-fa-gray-400 hover:text-fa-gray-600 hover:bg-fa-white-glass'
                }`}
                title={`${mode.charAt(0).toUpperCase() + mode.slice(1)} view`}
              >
                {mode === 'list' && <List className="w-4 h-4" />}
                {mode === 'grid' && <Grid className="w-4 h-4" />}
                {mode === 'kanban' && <BarChart3 className="w-4 h-4" />}
              </motion.button>
            ))}
          </div>

          {/* Refresh Button */}
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleRefresh}
            disabled={isLoading}
            className="fa-button-glass p-2 disabled:opacity-50"
            title="Refresh todos"
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          </motion.button>
        </div>

        {/* Sort Controls and Stats */}
        <div className="flex items-center justify-between">
          <TodoSortControls
            sortConfig={sorting.sortConfig}
            onUpdateSort={sorting.updateSort}
            onApplyPreset={sorting.applyPreset}
            onToggleSortOrder={sorting.toggleSortOrder}
            presets={sorting.presets}
            activePreset={sorting.activePreset}
            sortDescription={sorting.sortDescription}
          />

          {/* Stats */}
          <div className="text-sm text-fa-gray-500">
            {filtering.filterSummary.hasActiveFilters ? (
              <span>
                {filtering.filterSummary.totalFiltered} of {filtering.filterSummary.totalOriginal} tasks
              </span>
            ) : (
              <span>{todos.length} tasks</span>
            )}
          </div>
        </div>
      </div>

      {/* Bulk Action Toolbar */}
      <AnimatePresence>
        {bulkOps.selectionState.hasSelection && (
          <div className="flex-shrink-0 mb-4">
            <BulkActionToolbar
              isVisible={bulkOps.selectionState.hasSelection}
              selectedCount={bulkOps.selectionState.selectedCount}
              totalCount={bulkOps.selectionState.totalCount}
              isAllSelected={bulkOps.selectionState.isAllSelected}
              isPartiallySelected={bulkOps.selectionState.isPartiallySelected}
              onSelectAll={bulkOps.selectAll}
              onSelectNone={bulkOps.selectNone}
              operations={bulkOps.availableOperations}
              isProcessing={bulkOps.isProcessing}
              onClose={bulkOps.selectNone}
            />
          </div>
        )}
      </AnimatePresence>

      {/* Todo List */}
      <div className="flex-1 min-h-0">
        <VirtualizedTodoList
          todos={sorting.sortedTodos}
          onTodoUpdate={handleTodoUpdate}
          onTodoDelete={handleTodoDelete}
          containerHeight={containerHeight - 200} // Account for header space
          isLoading={isLoading}
          emptyState={
            filtering.filterSummary.hasActiveFilters ? (
              <div className="text-center">
                <div className="text-5xl mb-4">🔍</div>
                <h3 className="fa-heading-3 mb-2">No matching tasks</h3>
                <p className="fa-body text-fa-gray-500 mb-4">
                  Try adjusting your filters or search terms
                </p>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={filtering.clearFilters}
                  className="fa-button-glass px-4 py-2"
                >
                  Clear Filters
                </motion.button>
              </div>
            ) : (
              <div className="text-center">
                <div className="text-5xl mb-4">🍃</div>
                <h3 className="fa-heading-3 mb-2">No tasks yet</h3>
                <p className="fa-body text-fa-gray-500">
                  Add your first task to get started!
                </p>
              </div>
            )
          }
        />
      </div>

      {/* Filter Panel */}
      <TodoFilterPanel
        isOpen={isFilterPanelOpen}
        onClose={() => setIsFilterPanelOpen(false)}
        filters={filtering.filters}
        onUpdateFilter={filtering.updateFilter}
        onApplyPreset={filtering.applyPreset}
        onClearFilters={filtering.clearFilters}
        presets={filtering.presets}
        activePreset={filtering.activePreset}
        filterOptions={filtering.filterOptions}
        filterSummary={filtering.filterSummary}
      />
    </div>
  );
};
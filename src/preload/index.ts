import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

// Define the API that will be exposed to the renderer process
const electronAPI = {
  // Database operations
  database: {
    query: (sql: string, params?: any[]) => ipcRenderer.invoke('db:query', sql, params),
    transaction: (operations: any[]) => ipcRenderer.invoke('db:transaction', operations),
  },

  // Security operations
  security: {
    encrypt: (data: string) => ipcRenderer.invoke('security:encrypt', data),
    decrypt: (encryptedData: string) => ipcRenderer.invoke('security:decrypt', encryptedData),
  },

  // MCP operations
  mcp: {
    sync: (data: any) => ipcRenderer.invoke('mcp:sync', data),
  },

  // Sync operations
  sync: {
    getStatus: () => ipcRenderer.invoke('sync:getStatus'),
    getQueueStatus: () => ipcRenderer.invoke('sync:getQueueStatus'),
    getNetworkStatus: () => ipcRenderer.invoke('sync:getNetworkStatus'),
    getConflicts: () => ipcRenderer.invoke('sync:getConflicts'),
    performSync: () => ipcRenderer.invoke('sync:performSync'),
    forcePush: () => ipcRenderer.invoke('sync:forcePush'),
    forcePull: () => ipcRenderer.invoke('sync:forcePull'),
    resolveConflict: (conflictId: string, resolution: string, mergedData?: any) =>
      ipcRenderer.invoke('sync:resolveConflict', conflictId, resolution, mergedData),
    clearQueue: () => ipcRenderer.invoke('sync:clearQueue'),
  },

  // Todo operations
  todos: {
    getAll: (sessionId: string, filters?: any, pagination?: any) =>
      ipcRenderer.invoke('todos:getAll', sessionId, filters, pagination),
    create: (sessionId: string, todoData: any) =>
      ipcRenderer.invoke('todos:create', sessionId, todoData),
    update: (sessionId: string, todoId: string, updates: any) =>
      ipcRenderer.invoke('todos:update', sessionId, todoId, updates),
    delete: (sessionId: string, todoId: string) =>
      ipcRenderer.invoke('todos:delete', sessionId, todoId),
    updateStatus: (sessionId: string, todoId: string, status: string) =>
      ipcRenderer.invoke('todos:updateStatus', sessionId, todoId, status),
  },

  // Category operations
  categories: {
    getAll: (sessionId: string) =>
      ipcRenderer.invoke('categories:getAll', sessionId),
    create: (sessionId: string, categoryData: any) =>
      ipcRenderer.invoke('categories:create', sessionId, categoryData),
    update: (sessionId: string, categoryId: string, updates: any) =>
      ipcRenderer.invoke('categories:update', sessionId, categoryId, updates),
    delete: (sessionId: string, categoryId: string) =>
      ipcRenderer.invoke('categories:delete', sessionId, categoryId),
    reorder: (sessionId: string, categoryOrders: any[]) =>
      ipcRenderer.invoke('categories:reorder', sessionId, categoryOrders),
  },

  // Tag operations
  tags: {
    getAll: (sessionId: string) =>
      ipcRenderer.invoke('tags:getAll', sessionId),
    getSuggestions: (sessionId: string, query: string, limit?: number) =>
      ipcRenderer.invoke('tags:getSuggestions', sessionId, query, limit),
    getPopular: (sessionId: string, limit?: number) =>
      ipcRenderer.invoke('tags:getPopular', sessionId, limit),
    getRecent: (sessionId: string, limit?: number) =>
      ipcRenderer.invoke('tags:getRecent', sessionId, limit),
    getStats: (sessionId: string, tag: string) =>
      ipcRenderer.invoke('tags:getStats', sessionId, tag),
  },

  // System operations
  system: {
    getInfo: () => ipcRenderer.invoke('system:getInfo'),
    quit: () => ipcRenderer.invoke('app:quit'),
    minimize: () => ipcRenderer.invoke('app:minimize'),
    maximize: () => ipcRenderer.invoke('app:maximize'),
    close: () => ipcRenderer.invoke('app:close'),
  },

  // Event listeners
  on: (channel: string, callback: (event: any, ...args: any[]) => void) => {
    const validChannels = [
      'menu-new-todo',
      'menu-import',
      'menu-export',
      'sync-status-changed',
      'theme-changed',
    ];
    
    if (validChannels.includes(channel)) {
      ipcRenderer.on(channel, callback);
    }
  },

  // Remove event listeners
  removeAllListeners: (channel: string) => {
    ipcRenderer.removeAllListeners(channel);
  },
};

// Expose the API to the renderer process
contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// Export the type for the exposed API
export type ElectronAPIType = typeof electronAPI;

// Declare global interface for TypeScript
declare global {
  interface Window {
    electronAPI: typeof electronAPI;
  }
}
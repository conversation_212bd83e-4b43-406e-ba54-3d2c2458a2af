var h=Object.defineProperty;var y=(r,t,i)=>t in r?h(r,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):r[t]=i;var s=(r,t,i)=>y(r,typeof t!="symbol"?t+"":t,i);import{g as d,s as u,a as v,b as g}from"./index-B6kepP5v.js";const n=class n{constructor(){s(this,"intervalId",null);s(this,"notificationHistory",new Map);s(this,"isRunning",!1);s(this,"checkIntervalMs",15*60*1e3);s(this,"getTodos",null);s(this,"addNotification",null);this.loadNotificationHistory()}static getInstance(){return n.instance||(n.instance=new n),n.instance}initialize(t,i){this.getTodos=t,this.addNotification=i}start(){this.isRunning||!this.getTodos||!this.addNotification||(this.isRunning=!0,this.checkOverdueTodos(),this.intervalId=setInterval(()=>{this.checkOverdueTodos()},this.checkIntervalMs),console.log("OverdueNotificationService started"))}stop(){this.intervalId&&(clearInterval(this.intervalId),this.intervalId=null),this.isRunning=!1,console.log("OverdueNotificationService stopped")}async checkOverdueTodos(){if(!(!this.getTodos||!this.addNotification))try{const t=await this.getTodos(),i=t.filter(e=>d(e).isOverdue);for(const e of i)await this.processOverdueTodo(e);this.cleanupNotificationHistory(t)}catch(t){console.error("Error checking overdue todos:",t)}}async processOverdueTodo(t){const i=d(t),e=this.notificationHistory.get(t.id),o=e==null?void 0:e.lastNotificationTime;u(i,o)&&(await this.sendOverdueNotification(t,i),this.updateNotificationHistory(t.id))}async sendOverdueNotification(t,i){if(!this.addNotification)return;const{title:e,message:o}=v(t,i);g(i.severity);let f="warning",a=5e3;i.severity==="severe"?(f="error",a=1e4):i.severity==="moderate"&&(a=7e3);try{this.addNotification({type:f,title:e,message:o,duration:a,action:{label:"View Task",onClick:()=>{console.log("Navigate to todo:",t.id)}}}),console.log(`Sent ${i.severity} overdue notification for todo: ${t.title}`)}catch(l){console.error("Error sending overdue notification:",l)}}updateNotificationHistory(t){const i=this.notificationHistory.get(t),e={todoId:t,lastNotificationTime:new Date,notificationCount:((i==null?void 0:i.notificationCount)||0)+1};this.notificationHistory.set(t,e),this.saveNotificationHistory()}cleanupNotificationHistory(t){const i=new Set(t.map(o=>o.id)),e=new Set(t.filter(o=>o.status==="completed"||o.status==="cancelled").map(o=>o.id));for(const[o]of this.notificationHistory)(!i.has(o)||e.has(o))&&this.notificationHistory.delete(o);this.saveNotificationHistory()}async triggerCheck(){this.isRunning&&await this.checkOverdueTodos()}getNotificationHistory(){return Array.from(this.notificationHistory.values())}clearNotificationHistory(){this.notificationHistory.clear(),this.saveNotificationHistory()}saveNotificationHistory(){try{const t=Array.from(this.notificationHistory.entries());localStorage.setItem("overdueNotificationHistory",JSON.stringify(t))}catch(t){console.error("Error saving notification history:",t)}}loadNotificationHistory(){try{const t=localStorage.getItem("overdueNotificationHistory");if(t){const i=JSON.parse(t);this.notificationHistory=new Map(i.map(([e,o])=>[e,{...o,lastNotificationTime:new Date(o.lastNotificationTime)}]))}}catch(t){console.error("Error loading notification history:",t),this.notificationHistory=new Map}}setCheckInterval(t){this.checkIntervalMs=t*60*1e3,this.isRunning&&(this.stop(),this.start())}};s(n,"instance");let c=n;const p=c.getInstance();export{c as OverdueNotificationService,p as overdueNotificationService};

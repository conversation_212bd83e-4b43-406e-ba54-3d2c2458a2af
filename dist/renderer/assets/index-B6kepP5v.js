var Xx=Object.defineProperty;var Yx=(e,t,n)=>t in e?Xx(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Ht=(e,t,n)=>Yx(e,typeof t!="symbol"?t+"":t,n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const a of i.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&r(a)}).observe(document,{childList:!0,subtree:!0});function n(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(s){if(s.ep)return;s.ep=!0;const i=n(s);fetch(s.href,i)}})();function op(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var lp={exports:{}},ro={},cp={exports:{}},J={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xi=Symbol.for("react.element"),Zx=Symbol.for("react.portal"),Jx=Symbol.for("react.fragment"),ev=Symbol.for("react.strict_mode"),tv=Symbol.for("react.profiler"),nv=Symbol.for("react.provider"),rv=Symbol.for("react.context"),sv=Symbol.for("react.forward_ref"),iv=Symbol.for("react.suspense"),av=Symbol.for("react.memo"),ov=Symbol.for("react.lazy"),ef=Symbol.iterator;function lv(e){return e===null||typeof e!="object"?null:(e=ef&&e[ef]||e["@@iterator"],typeof e=="function"?e:null)}var up={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},dp=Object.assign,fp={};function rs(e,t,n){this.props=e,this.context=t,this.refs=fp,this.updater=n||up}rs.prototype.isReactComponent={};rs.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};rs.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function hp(){}hp.prototype=rs.prototype;function nu(e,t,n){this.props=e,this.context=t,this.refs=fp,this.updater=n||up}var ru=nu.prototype=new hp;ru.constructor=nu;dp(ru,rs.prototype);ru.isPureReactComponent=!0;var tf=Array.isArray,mp=Object.prototype.hasOwnProperty,su={current:null},pp={key:!0,ref:!0,__self:!0,__source:!0};function gp(e,t,n){var r,s={},i=null,a=null;if(t!=null)for(r in t.ref!==void 0&&(a=t.ref),t.key!==void 0&&(i=""+t.key),t)mp.call(t,r)&&!pp.hasOwnProperty(r)&&(s[r]=t[r]);var l=arguments.length-2;if(l===1)s.children=n;else if(1<l){for(var c=Array(l),u=0;u<l;u++)c[u]=arguments[u+2];s.children=c}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)s[r]===void 0&&(s[r]=l[r]);return{$$typeof:xi,type:e,key:i,ref:a,props:s,_owner:su.current}}function cv(e,t){return{$$typeof:xi,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function iu(e){return typeof e=="object"&&e!==null&&e.$$typeof===xi}function uv(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var nf=/\/+/g;function Uo(e,t){return typeof e=="object"&&e!==null&&e.key!=null?uv(""+e.key):t.toString(36)}function ea(e,t,n,r,s){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var a=!1;if(e===null)a=!0;else switch(i){case"string":case"number":a=!0;break;case"object":switch(e.$$typeof){case xi:case Zx:a=!0}}if(a)return a=e,s=s(a),e=r===""?"."+Uo(a,0):r,tf(s)?(n="",e!=null&&(n=e.replace(nf,"$&/")+"/"),ea(s,t,n,"",function(u){return u})):s!=null&&(iu(s)&&(s=cv(s,n+(!s.key||a&&a.key===s.key?"":(""+s.key).replace(nf,"$&/")+"/")+e)),t.push(s)),1;if(a=0,r=r===""?".":r+":",tf(e))for(var l=0;l<e.length;l++){i=e[l];var c=r+Uo(i,l);a+=ea(i,t,n,c,s)}else if(c=lv(e),typeof c=="function")for(e=c.call(e),l=0;!(i=e.next()).done;)i=i.value,c=r+Uo(i,l++),a+=ea(i,t,n,c,s);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return a}function Ei(e,t,n){if(e==null)return e;var r=[],s=0;return ea(e,r,"","",function(i){return t.call(n,i,s++)}),r}function dv(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ke={current:null},ta={transition:null},fv={ReactCurrentDispatcher:Ke,ReactCurrentBatchConfig:ta,ReactCurrentOwner:su};function yp(){throw Error("act(...) is not supported in production builds of React.")}J.Children={map:Ei,forEach:function(e,t,n){Ei(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Ei(e,function(){t++}),t},toArray:function(e){return Ei(e,function(t){return t})||[]},only:function(e){if(!iu(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};J.Component=rs;J.Fragment=Jx;J.Profiler=tv;J.PureComponent=nu;J.StrictMode=ev;J.Suspense=iv;J.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=fv;J.act=yp;J.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=dp({},e.props),s=e.key,i=e.ref,a=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,a=su.current),t.key!==void 0&&(s=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(c in t)mp.call(t,c)&&!pp.hasOwnProperty(c)&&(r[c]=t[c]===void 0&&l!==void 0?l[c]:t[c])}var c=arguments.length-2;if(c===1)r.children=n;else if(1<c){l=Array(c);for(var u=0;u<c;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:xi,type:e.type,key:s,ref:i,props:r,_owner:a}};J.createContext=function(e){return e={$$typeof:rv,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:nv,_context:e},e.Consumer=e};J.createElement=gp;J.createFactory=function(e){var t=gp.bind(null,e);return t.type=e,t};J.createRef=function(){return{current:null}};J.forwardRef=function(e){return{$$typeof:sv,render:e}};J.isValidElement=iu;J.lazy=function(e){return{$$typeof:ov,_payload:{_status:-1,_result:e},_init:dv}};J.memo=function(e,t){return{$$typeof:av,type:e,compare:t===void 0?null:t}};J.startTransition=function(e){var t=ta.transition;ta.transition={};try{e()}finally{ta.transition=t}};J.unstable_act=yp;J.useCallback=function(e,t){return Ke.current.useCallback(e,t)};J.useContext=function(e){return Ke.current.useContext(e)};J.useDebugValue=function(){};J.useDeferredValue=function(e){return Ke.current.useDeferredValue(e)};J.useEffect=function(e,t){return Ke.current.useEffect(e,t)};J.useId=function(){return Ke.current.useId()};J.useImperativeHandle=function(e,t,n){return Ke.current.useImperativeHandle(e,t,n)};J.useInsertionEffect=function(e,t){return Ke.current.useInsertionEffect(e,t)};J.useLayoutEffect=function(e,t){return Ke.current.useLayoutEffect(e,t)};J.useMemo=function(e,t){return Ke.current.useMemo(e,t)};J.useReducer=function(e,t,n){return Ke.current.useReducer(e,t,n)};J.useRef=function(e){return Ke.current.useRef(e)};J.useState=function(e){return Ke.current.useState(e)};J.useSyncExternalStore=function(e,t,n){return Ke.current.useSyncExternalStore(e,t,n)};J.useTransition=function(){return Ke.current.useTransition()};J.version="18.3.1";cp.exports=J;var w=cp.exports;const Z=op(w);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var hv=w,mv=Symbol.for("react.element"),pv=Symbol.for("react.fragment"),gv=Object.prototype.hasOwnProperty,yv=hv.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,xv={key:!0,ref:!0,__self:!0,__source:!0};function xp(e,t,n){var r,s={},i=null,a=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(a=t.ref);for(r in t)gv.call(t,r)&&!xv.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)s[r]===void 0&&(s[r]=t[r]);return{$$typeof:mv,type:e,key:i,ref:a,props:s,_owner:yv.current}}ro.Fragment=pv;ro.jsx=xp;ro.jsxs=xp;lp.exports=ro;var o=lp.exports,Fl={},vp={exports:{}},ut={},wp={exports:{}},Sp={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(L,K){var Y=L.length;L.push(K);e:for(;0<Y;){var ue=Y-1>>>1,je=L[ue];if(0<s(je,K))L[ue]=K,L[Y]=je,Y=ue;else break e}}function n(L){return L.length===0?null:L[0]}function r(L){if(L.length===0)return null;var K=L[0],Y=L.pop();if(Y!==K){L[0]=Y;e:for(var ue=0,je=L.length,In=je>>>1;ue<In;){var Ut=2*(ue+1)-1,gr=L[Ut],Bt=Ut+1,cn=L[Bt];if(0>s(gr,Y))Bt<je&&0>s(cn,gr)?(L[ue]=cn,L[Bt]=Y,ue=Bt):(L[ue]=gr,L[Ut]=Y,ue=Ut);else if(Bt<je&&0>s(cn,Y))L[ue]=cn,L[Bt]=Y,ue=Bt;else break e}}return K}function s(L,K){var Y=L.sortIndex-K.sortIndex;return Y!==0?Y:L.id-K.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var a=Date,l=a.now();e.unstable_now=function(){return a.now()-l}}var c=[],u=[],d=1,h=null,f=3,g=!1,x=!1,v=!1,S=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,m=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function y(L){for(var K=n(u);K!==null;){if(K.callback===null)r(u);else if(K.startTime<=L)r(u),K.sortIndex=K.expirationTime,t(c,K);else break;K=n(u)}}function b(L){if(v=!1,y(L),!x)if(n(c)!==null)x=!0,W(j);else{var K=n(u);K!==null&&U(b,K.startTime-L)}}function j(L,K){x=!1,v&&(v=!1,p(C),C=-1),g=!0;var Y=f;try{for(y(K),h=n(c);h!==null&&(!(h.expirationTime>K)||L&&!V());){var ue=h.callback;if(typeof ue=="function"){h.callback=null,f=h.priorityLevel;var je=ue(h.expirationTime<=K);K=e.unstable_now(),typeof je=="function"?h.callback=je:h===n(c)&&r(c),y(K)}else r(c);h=n(c)}if(h!==null)var In=!0;else{var Ut=n(u);Ut!==null&&U(b,Ut.startTime-K),In=!1}return In}finally{h=null,f=Y,g=!1}}var k=!1,_=null,C=-1,P=5,D=-1;function V(){return!(e.unstable_now()-D<P)}function A(){if(_!==null){var L=e.unstable_now();D=L;var K=!0;try{K=_(!0,L)}finally{K?R():(k=!1,_=null)}}else k=!1}var R;if(typeof m=="function")R=function(){m(A)};else if(typeof MessageChannel<"u"){var X=new MessageChannel,G=X.port2;X.port1.onmessage=A,R=function(){G.postMessage(null)}}else R=function(){S(A,0)};function W(L){_=L,k||(k=!0,R())}function U(L,K){C=S(function(){L(e.unstable_now())},K)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(L){L.callback=null},e.unstable_continueExecution=function(){x||g||(x=!0,W(j))},e.unstable_forceFrameRate=function(L){0>L||125<L?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):P=0<L?Math.floor(1e3/L):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(c)},e.unstable_next=function(L){switch(f){case 1:case 2:case 3:var K=3;break;default:K=f}var Y=f;f=K;try{return L()}finally{f=Y}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(L,K){switch(L){case 1:case 2:case 3:case 4:case 5:break;default:L=3}var Y=f;f=L;try{return K()}finally{f=Y}},e.unstable_scheduleCallback=function(L,K,Y){var ue=e.unstable_now();switch(typeof Y=="object"&&Y!==null?(Y=Y.delay,Y=typeof Y=="number"&&0<Y?ue+Y:ue):Y=ue,L){case 1:var je=-1;break;case 2:je=250;break;case 5:je=**********;break;case 4:je=1e4;break;default:je=5e3}return je=Y+je,L={id:d++,callback:K,priorityLevel:L,startTime:Y,expirationTime:je,sortIndex:-1},Y>ue?(L.sortIndex=Y,t(u,L),n(c)===null&&L===n(u)&&(v?(p(C),C=-1):v=!0,U(b,Y-ue))):(L.sortIndex=je,t(c,L),x||g||(x=!0,W(j))),L},e.unstable_shouldYield=V,e.unstable_wrapCallback=function(L){var K=f;return function(){var Y=f;f=K;try{return L.apply(this,arguments)}finally{f=Y}}}})(Sp);wp.exports=Sp;var vv=wp.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var wv=w,at=vv;function F(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var bp=new Set,Hs={};function fr(e,t){Ur(e,t),Ur(e+"Capture",t)}function Ur(e,t){for(Hs[e]=t,e=0;e<t.length;e++)bp.add(t[e])}var nn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Vl=Object.prototype.hasOwnProperty,Sv=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,rf={},sf={};function bv(e){return Vl.call(sf,e)?!0:Vl.call(rf,e)?!1:Sv.test(e)?sf[e]=!0:(rf[e]=!0,!1)}function jv(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Nv(e,t,n,r){if(t===null||typeof t>"u"||jv(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Ge(e,t,n,r,s,i,a){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=s,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=a}var Re={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Re[e]=new Ge(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Re[t]=new Ge(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Re[e]=new Ge(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Re[e]=new Ge(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Re[e]=new Ge(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Re[e]=new Ge(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Re[e]=new Ge(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Re[e]=new Ge(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Re[e]=new Ge(e,5,!1,e.toLowerCase(),null,!1,!1)});var au=/[\-:]([a-z])/g;function ou(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(au,ou);Re[t]=new Ge(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(au,ou);Re[t]=new Ge(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(au,ou);Re[t]=new Ge(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Re[e]=new Ge(e,1,!1,e.toLowerCase(),null,!1,!1)});Re.xlinkHref=new Ge("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Re[e]=new Ge(e,1,!1,e.toLowerCase(),null,!0,!0)});function lu(e,t,n,r){var s=Re.hasOwnProperty(t)?Re[t]:null;(s!==null?s.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Nv(t,n,s,r)&&(n=null),r||s===null?bv(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):s.mustUseProperty?e[s.propertyName]=n===null?s.type===3?!1:"":n:(t=s.attributeName,r=s.attributeNamespace,n===null?e.removeAttribute(t):(s=s.type,n=s===3||s===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var ln=wv.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Pi=Symbol.for("react.element"),vr=Symbol.for("react.portal"),wr=Symbol.for("react.fragment"),cu=Symbol.for("react.strict_mode"),Il=Symbol.for("react.profiler"),jp=Symbol.for("react.provider"),Np=Symbol.for("react.context"),uu=Symbol.for("react.forward_ref"),Ol=Symbol.for("react.suspense"),zl=Symbol.for("react.suspense_list"),du=Symbol.for("react.memo"),mn=Symbol.for("react.lazy"),Cp=Symbol.for("react.offscreen"),af=Symbol.iterator;function cs(e){return e===null||typeof e!="object"?null:(e=af&&e[af]||e["@@iterator"],typeof e=="function"?e:null)}var ge=Object.assign,Bo;function Ss(e){if(Bo===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Bo=t&&t[1]||""}return`
`+Bo+e}var Ho=!1;function Wo(e,t){if(!e||Ho)return"";Ho=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var s=u.stack.split(`
`),i=r.stack.split(`
`),a=s.length-1,l=i.length-1;1<=a&&0<=l&&s[a]!==i[l];)l--;for(;1<=a&&0<=l;a--,l--)if(s[a]!==i[l]){if(a!==1||l!==1)do if(a--,l--,0>l||s[a]!==i[l]){var c=`
`+s[a].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}while(1<=a&&0<=l);break}}}finally{Ho=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Ss(e):""}function Cv(e){switch(e.tag){case 5:return Ss(e.type);case 16:return Ss("Lazy");case 13:return Ss("Suspense");case 19:return Ss("SuspenseList");case 0:case 2:case 15:return e=Wo(e.type,!1),e;case 11:return e=Wo(e.type.render,!1),e;case 1:return e=Wo(e.type,!0),e;default:return""}}function $l(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case wr:return"Fragment";case vr:return"Portal";case Il:return"Profiler";case cu:return"StrictMode";case Ol:return"Suspense";case zl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Np:return(e.displayName||"Context")+".Consumer";case jp:return(e._context.displayName||"Context")+".Provider";case uu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case du:return t=e.displayName||null,t!==null?t:$l(e.type)||"Memo";case mn:t=e._payload,e=e._init;try{return $l(e(t))}catch{}}return null}function kv(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return $l(t);case 8:return t===cu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function _n(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function kp(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Tv(e){var t=kp(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(a){r=""+a,i.call(this,a)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(a){r=""+a},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ai(e){e._valueTracker||(e._valueTracker=Tv(e))}function Tp(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=kp(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function ya(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Ul(e,t){var n=t.checked;return ge({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function of(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=_n(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function _p(e,t){t=t.checked,t!=null&&lu(e,"checked",t,!1)}function Bl(e,t){_p(e,t);var n=_n(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Hl(e,t.type,n):t.hasOwnProperty("defaultValue")&&Hl(e,t.type,_n(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function lf(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Hl(e,t,n){(t!=="number"||ya(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var bs=Array.isArray;function Fr(e,t,n,r){if(e=e.options,t){t={};for(var s=0;s<n.length;s++)t["$"+n[s]]=!0;for(n=0;n<e.length;n++)s=t.hasOwnProperty("$"+e[n].value),e[n].selected!==s&&(e[n].selected=s),s&&r&&(e[n].defaultSelected=!0)}else{for(n=""+_n(n),t=null,s=0;s<e.length;s++){if(e[s].value===n){e[s].selected=!0,r&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function Wl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(F(91));return ge({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function cf(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(F(92));if(bs(n)){if(1<n.length)throw Error(F(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:_n(n)}}function Ep(e,t){var n=_n(t.value),r=_n(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function uf(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Pp(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Kl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Pp(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Di,Ap=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,s){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,s)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Di=Di||document.createElement("div"),Di.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Di.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Ws(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Es={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},_v=["Webkit","ms","Moz","O"];Object.keys(Es).forEach(function(e){_v.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Es[t]=Es[e]})});function Dp(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Es.hasOwnProperty(e)&&Es[e]?(""+t).trim():t+"px"}function Mp(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,s=Dp(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,s):e[n]=s}}var Ev=ge({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Gl(e,t){if(t){if(Ev[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(F(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(F(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(F(61))}if(t.style!=null&&typeof t.style!="object")throw Error(F(62))}}function Ql(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ql=null;function fu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Xl=null,Vr=null,Ir=null;function df(e){if(e=Si(e)){if(typeof Xl!="function")throw Error(F(280));var t=e.stateNode;t&&(t=lo(t),Xl(e.stateNode,e.type,t))}}function Rp(e){Vr?Ir?Ir.push(e):Ir=[e]:Vr=e}function Lp(){if(Vr){var e=Vr,t=Ir;if(Ir=Vr=null,df(e),t)for(e=0;e<t.length;e++)df(t[e])}}function Fp(e,t){return e(t)}function Vp(){}var Ko=!1;function Ip(e,t,n){if(Ko)return e(t,n);Ko=!0;try{return Fp(e,t,n)}finally{Ko=!1,(Vr!==null||Ir!==null)&&(Vp(),Lp())}}function Ks(e,t){var n=e.stateNode;if(n===null)return null;var r=lo(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(F(231,t,typeof n));return n}var Yl=!1;if(nn)try{var us={};Object.defineProperty(us,"passive",{get:function(){Yl=!0}}),window.addEventListener("test",us,us),window.removeEventListener("test",us,us)}catch{Yl=!1}function Pv(e,t,n,r,s,i,a,l,c){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(d){this.onError(d)}}var Ps=!1,xa=null,va=!1,Zl=null,Av={onError:function(e){Ps=!0,xa=e}};function Dv(e,t,n,r,s,i,a,l,c){Ps=!1,xa=null,Pv.apply(Av,arguments)}function Mv(e,t,n,r,s,i,a,l,c){if(Dv.apply(this,arguments),Ps){if(Ps){var u=xa;Ps=!1,xa=null}else throw Error(F(198));va||(va=!0,Zl=u)}}function hr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Op(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function ff(e){if(hr(e)!==e)throw Error(F(188))}function Rv(e){var t=e.alternate;if(!t){if(t=hr(e),t===null)throw Error(F(188));return t!==e?null:e}for(var n=e,r=t;;){var s=n.return;if(s===null)break;var i=s.alternate;if(i===null){if(r=s.return,r!==null){n=r;continue}break}if(s.child===i.child){for(i=s.child;i;){if(i===n)return ff(s),e;if(i===r)return ff(s),t;i=i.sibling}throw Error(F(188))}if(n.return!==r.return)n=s,r=i;else{for(var a=!1,l=s.child;l;){if(l===n){a=!0,n=s,r=i;break}if(l===r){a=!0,r=s,n=i;break}l=l.sibling}if(!a){for(l=i.child;l;){if(l===n){a=!0,n=i,r=s;break}if(l===r){a=!0,r=i,n=s;break}l=l.sibling}if(!a)throw Error(F(189))}}if(n.alternate!==r)throw Error(F(190))}if(n.tag!==3)throw Error(F(188));return n.stateNode.current===n?e:t}function zp(e){return e=Rv(e),e!==null?$p(e):null}function $p(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=$p(e);if(t!==null)return t;e=e.sibling}return null}var Up=at.unstable_scheduleCallback,hf=at.unstable_cancelCallback,Lv=at.unstable_shouldYield,Fv=at.unstable_requestPaint,Se=at.unstable_now,Vv=at.unstable_getCurrentPriorityLevel,hu=at.unstable_ImmediatePriority,Bp=at.unstable_UserBlockingPriority,wa=at.unstable_NormalPriority,Iv=at.unstable_LowPriority,Hp=at.unstable_IdlePriority,so=null,Vt=null;function Ov(e){if(Vt&&typeof Vt.onCommitFiberRoot=="function")try{Vt.onCommitFiberRoot(so,e,void 0,(e.current.flags&128)===128)}catch{}}var Et=Math.clz32?Math.clz32:Uv,zv=Math.log,$v=Math.LN2;function Uv(e){return e>>>=0,e===0?32:31-(zv(e)/$v|0)|0}var Mi=64,Ri=4194304;function js(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Sa(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,s=e.suspendedLanes,i=e.pingedLanes,a=n&268435455;if(a!==0){var l=a&~s;l!==0?r=js(l):(i&=a,i!==0&&(r=js(i)))}else a=n&~s,a!==0?r=js(a):i!==0&&(r=js(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&s)&&(s=r&-r,i=t&-t,s>=i||s===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Et(t),s=1<<n,r|=e[n],t&=~s;return r}function Bv(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Hv(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,s=e.expirationTimes,i=e.pendingLanes;0<i;){var a=31-Et(i),l=1<<a,c=s[a];c===-1?(!(l&n)||l&r)&&(s[a]=Bv(l,t)):c<=t&&(e.expiredLanes|=l),i&=~l}}function Jl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Wp(){var e=Mi;return Mi<<=1,!(Mi&4194240)&&(Mi=64),e}function Go(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function vi(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Et(t),e[t]=n}function Wv(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var s=31-Et(n),i=1<<s;t[s]=0,r[s]=-1,e[s]=-1,n&=~i}}function mu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Et(n),s=1<<r;s&t|e[r]&t&&(e[r]|=t),n&=~s}}var ne=0;function Kp(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Gp,pu,Qp,qp,Xp,ec=!1,Li=[],wn=null,Sn=null,bn=null,Gs=new Map,Qs=new Map,gn=[],Kv="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function mf(e,t){switch(e){case"focusin":case"focusout":wn=null;break;case"dragenter":case"dragleave":Sn=null;break;case"mouseover":case"mouseout":bn=null;break;case"pointerover":case"pointerout":Gs.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Qs.delete(t.pointerId)}}function ds(e,t,n,r,s,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[s]},t!==null&&(t=Si(t),t!==null&&pu(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function Gv(e,t,n,r,s){switch(t){case"focusin":return wn=ds(wn,e,t,n,r,s),!0;case"dragenter":return Sn=ds(Sn,e,t,n,r,s),!0;case"mouseover":return bn=ds(bn,e,t,n,r,s),!0;case"pointerover":var i=s.pointerId;return Gs.set(i,ds(Gs.get(i)||null,e,t,n,r,s)),!0;case"gotpointercapture":return i=s.pointerId,Qs.set(i,ds(Qs.get(i)||null,e,t,n,r,s)),!0}return!1}function Yp(e){var t=Gn(e.target);if(t!==null){var n=hr(t);if(n!==null){if(t=n.tag,t===13){if(t=Op(n),t!==null){e.blockedOn=t,Xp(e.priority,function(){Qp(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function na(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=tc(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);ql=r,n.target.dispatchEvent(r),ql=null}else return t=Si(n),t!==null&&pu(t),e.blockedOn=n,!1;t.shift()}return!0}function pf(e,t,n){na(e)&&n.delete(t)}function Qv(){ec=!1,wn!==null&&na(wn)&&(wn=null),Sn!==null&&na(Sn)&&(Sn=null),bn!==null&&na(bn)&&(bn=null),Gs.forEach(pf),Qs.forEach(pf)}function fs(e,t){e.blockedOn===t&&(e.blockedOn=null,ec||(ec=!0,at.unstable_scheduleCallback(at.unstable_NormalPriority,Qv)))}function qs(e){function t(s){return fs(s,e)}if(0<Li.length){fs(Li[0],e);for(var n=1;n<Li.length;n++){var r=Li[n];r.blockedOn===e&&(r.blockedOn=null)}}for(wn!==null&&fs(wn,e),Sn!==null&&fs(Sn,e),bn!==null&&fs(bn,e),Gs.forEach(t),Qs.forEach(t),n=0;n<gn.length;n++)r=gn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<gn.length&&(n=gn[0],n.blockedOn===null);)Yp(n),n.blockedOn===null&&gn.shift()}var Or=ln.ReactCurrentBatchConfig,ba=!0;function qv(e,t,n,r){var s=ne,i=Or.transition;Or.transition=null;try{ne=1,gu(e,t,n,r)}finally{ne=s,Or.transition=i}}function Xv(e,t,n,r){var s=ne,i=Or.transition;Or.transition=null;try{ne=4,gu(e,t,n,r)}finally{ne=s,Or.transition=i}}function gu(e,t,n,r){if(ba){var s=tc(e,t,n,r);if(s===null)rl(e,t,r,ja,n),mf(e,r);else if(Gv(s,e,t,n,r))r.stopPropagation();else if(mf(e,r),t&4&&-1<Kv.indexOf(e)){for(;s!==null;){var i=Si(s);if(i!==null&&Gp(i),i=tc(e,t,n,r),i===null&&rl(e,t,r,ja,n),i===s)break;s=i}s!==null&&r.stopPropagation()}else rl(e,t,r,null,n)}}var ja=null;function tc(e,t,n,r){if(ja=null,e=fu(r),e=Gn(e),e!==null)if(t=hr(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Op(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ja=e,null}function Zp(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Vv()){case hu:return 1;case Bp:return 4;case wa:case Iv:return 16;case Hp:return 536870912;default:return 16}default:return 16}}var xn=null,yu=null,ra=null;function Jp(){if(ra)return ra;var e,t=yu,n=t.length,r,s="value"in xn?xn.value:xn.textContent,i=s.length;for(e=0;e<n&&t[e]===s[e];e++);var a=n-e;for(r=1;r<=a&&t[n-r]===s[i-r];r++);return ra=s.slice(e,1<r?1-r:void 0)}function sa(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Fi(){return!0}function gf(){return!1}function dt(e){function t(n,r,s,i,a){this._reactName=n,this._targetInst=s,this.type=r,this.nativeEvent=i,this.target=a,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(i):i[l]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Fi:gf,this.isPropagationStopped=gf,this}return ge(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Fi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Fi)},persist:function(){},isPersistent:Fi}),t}var ss={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},xu=dt(ss),wi=ge({},ss,{view:0,detail:0}),Yv=dt(wi),Qo,qo,hs,io=ge({},wi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:vu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==hs&&(hs&&e.type==="mousemove"?(Qo=e.screenX-hs.screenX,qo=e.screenY-hs.screenY):qo=Qo=0,hs=e),Qo)},movementY:function(e){return"movementY"in e?e.movementY:qo}}),yf=dt(io),Zv=ge({},io,{dataTransfer:0}),Jv=dt(Zv),ew=ge({},wi,{relatedTarget:0}),Xo=dt(ew),tw=ge({},ss,{animationName:0,elapsedTime:0,pseudoElement:0}),nw=dt(tw),rw=ge({},ss,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),sw=dt(rw),iw=ge({},ss,{data:0}),xf=dt(iw),aw={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},ow={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},lw={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function cw(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=lw[e])?!!t[e]:!1}function vu(){return cw}var uw=ge({},wi,{key:function(e){if(e.key){var t=aw[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=sa(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?ow[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:vu,charCode:function(e){return e.type==="keypress"?sa(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?sa(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),dw=dt(uw),fw=ge({},io,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),vf=dt(fw),hw=ge({},wi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:vu}),mw=dt(hw),pw=ge({},ss,{propertyName:0,elapsedTime:0,pseudoElement:0}),gw=dt(pw),yw=ge({},io,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),xw=dt(yw),vw=[9,13,27,32],wu=nn&&"CompositionEvent"in window,As=null;nn&&"documentMode"in document&&(As=document.documentMode);var ww=nn&&"TextEvent"in window&&!As,eg=nn&&(!wu||As&&8<As&&11>=As),wf=" ",Sf=!1;function tg(e,t){switch(e){case"keyup":return vw.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function ng(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Sr=!1;function Sw(e,t){switch(e){case"compositionend":return ng(t);case"keypress":return t.which!==32?null:(Sf=!0,wf);case"textInput":return e=t.data,e===wf&&Sf?null:e;default:return null}}function bw(e,t){if(Sr)return e==="compositionend"||!wu&&tg(e,t)?(e=Jp(),ra=yu=xn=null,Sr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return eg&&t.locale!=="ko"?null:t.data;default:return null}}var jw={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function bf(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!jw[e.type]:t==="textarea"}function rg(e,t,n,r){Rp(r),t=Na(t,"onChange"),0<t.length&&(n=new xu("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Ds=null,Xs=null;function Nw(e){mg(e,0)}function ao(e){var t=Nr(e);if(Tp(t))return e}function Cw(e,t){if(e==="change")return t}var sg=!1;if(nn){var Yo;if(nn){var Zo="oninput"in document;if(!Zo){var jf=document.createElement("div");jf.setAttribute("oninput","return;"),Zo=typeof jf.oninput=="function"}Yo=Zo}else Yo=!1;sg=Yo&&(!document.documentMode||9<document.documentMode)}function Nf(){Ds&&(Ds.detachEvent("onpropertychange",ig),Xs=Ds=null)}function ig(e){if(e.propertyName==="value"&&ao(Xs)){var t=[];rg(t,Xs,e,fu(e)),Ip(Nw,t)}}function kw(e,t,n){e==="focusin"?(Nf(),Ds=t,Xs=n,Ds.attachEvent("onpropertychange",ig)):e==="focusout"&&Nf()}function Tw(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return ao(Xs)}function _w(e,t){if(e==="click")return ao(t)}function Ew(e,t){if(e==="input"||e==="change")return ao(t)}function Pw(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var At=typeof Object.is=="function"?Object.is:Pw;function Ys(e,t){if(At(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var s=n[r];if(!Vl.call(t,s)||!At(e[s],t[s]))return!1}return!0}function Cf(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function kf(e,t){var n=Cf(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Cf(n)}}function ag(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?ag(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function og(){for(var e=window,t=ya();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=ya(e.document)}return t}function Su(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Aw(e){var t=og(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&ag(n.ownerDocument.documentElement,n)){if(r!==null&&Su(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var s=n.textContent.length,i=Math.min(r.start,s);r=r.end===void 0?i:Math.min(r.end,s),!e.extend&&i>r&&(s=r,r=i,i=s),s=kf(n,i);var a=kf(n,r);s&&a&&(e.rangeCount!==1||e.anchorNode!==s.node||e.anchorOffset!==s.offset||e.focusNode!==a.node||e.focusOffset!==a.offset)&&(t=t.createRange(),t.setStart(s.node,s.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(a.node,a.offset)):(t.setEnd(a.node,a.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Dw=nn&&"documentMode"in document&&11>=document.documentMode,br=null,nc=null,Ms=null,rc=!1;function Tf(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;rc||br==null||br!==ya(r)||(r=br,"selectionStart"in r&&Su(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Ms&&Ys(Ms,r)||(Ms=r,r=Na(nc,"onSelect"),0<r.length&&(t=new xu("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=br)))}function Vi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var jr={animationend:Vi("Animation","AnimationEnd"),animationiteration:Vi("Animation","AnimationIteration"),animationstart:Vi("Animation","AnimationStart"),transitionend:Vi("Transition","TransitionEnd")},Jo={},lg={};nn&&(lg=document.createElement("div").style,"AnimationEvent"in window||(delete jr.animationend.animation,delete jr.animationiteration.animation,delete jr.animationstart.animation),"TransitionEvent"in window||delete jr.transitionend.transition);function oo(e){if(Jo[e])return Jo[e];if(!jr[e])return e;var t=jr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in lg)return Jo[e]=t[n];return e}var cg=oo("animationend"),ug=oo("animationiteration"),dg=oo("animationstart"),fg=oo("transitionend"),hg=new Map,_f="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Dn(e,t){hg.set(e,t),fr(t,[e])}for(var el=0;el<_f.length;el++){var tl=_f[el],Mw=tl.toLowerCase(),Rw=tl[0].toUpperCase()+tl.slice(1);Dn(Mw,"on"+Rw)}Dn(cg,"onAnimationEnd");Dn(ug,"onAnimationIteration");Dn(dg,"onAnimationStart");Dn("dblclick","onDoubleClick");Dn("focusin","onFocus");Dn("focusout","onBlur");Dn(fg,"onTransitionEnd");Ur("onMouseEnter",["mouseout","mouseover"]);Ur("onMouseLeave",["mouseout","mouseover"]);Ur("onPointerEnter",["pointerout","pointerover"]);Ur("onPointerLeave",["pointerout","pointerover"]);fr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));fr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));fr("onBeforeInput",["compositionend","keypress","textInput","paste"]);fr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));fr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));fr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ns="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Lw=new Set("cancel close invalid load scroll toggle".split(" ").concat(Ns));function Ef(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Mv(r,t,void 0,e),e.currentTarget=null}function mg(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],s=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var a=r.length-1;0<=a;a--){var l=r[a],c=l.instance,u=l.currentTarget;if(l=l.listener,c!==i&&s.isPropagationStopped())break e;Ef(s,l,u),i=c}else for(a=0;a<r.length;a++){if(l=r[a],c=l.instance,u=l.currentTarget,l=l.listener,c!==i&&s.isPropagationStopped())break e;Ef(s,l,u),i=c}}}if(va)throw e=Zl,va=!1,Zl=null,e}function oe(e,t){var n=t[lc];n===void 0&&(n=t[lc]=new Set);var r=e+"__bubble";n.has(r)||(pg(t,e,2,!1),n.add(r))}function nl(e,t,n){var r=0;t&&(r|=4),pg(n,e,r,t)}var Ii="_reactListening"+Math.random().toString(36).slice(2);function Zs(e){if(!e[Ii]){e[Ii]=!0,bp.forEach(function(n){n!=="selectionchange"&&(Lw.has(n)||nl(n,!1,e),nl(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ii]||(t[Ii]=!0,nl("selectionchange",!1,t))}}function pg(e,t,n,r){switch(Zp(t)){case 1:var s=qv;break;case 4:s=Xv;break;default:s=gu}n=s.bind(null,t,n,e),s=void 0,!Yl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),r?s!==void 0?e.addEventListener(t,n,{capture:!0,passive:s}):e.addEventListener(t,n,!0):s!==void 0?e.addEventListener(t,n,{passive:s}):e.addEventListener(t,n,!1)}function rl(e,t,n,r,s){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var a=r.tag;if(a===3||a===4){var l=r.stateNode.containerInfo;if(l===s||l.nodeType===8&&l.parentNode===s)break;if(a===4)for(a=r.return;a!==null;){var c=a.tag;if((c===3||c===4)&&(c=a.stateNode.containerInfo,c===s||c.nodeType===8&&c.parentNode===s))return;a=a.return}for(;l!==null;){if(a=Gn(l),a===null)return;if(c=a.tag,c===5||c===6){r=i=a;continue e}l=l.parentNode}}r=r.return}Ip(function(){var u=i,d=fu(n),h=[];e:{var f=hg.get(e);if(f!==void 0){var g=xu,x=e;switch(e){case"keypress":if(sa(n)===0)break e;case"keydown":case"keyup":g=dw;break;case"focusin":x="focus",g=Xo;break;case"focusout":x="blur",g=Xo;break;case"beforeblur":case"afterblur":g=Xo;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=yf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=Jv;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=mw;break;case cg:case ug:case dg:g=nw;break;case fg:g=gw;break;case"scroll":g=Yv;break;case"wheel":g=xw;break;case"copy":case"cut":case"paste":g=sw;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=vf}var v=(t&4)!==0,S=!v&&e==="scroll",p=v?f!==null?f+"Capture":null:f;v=[];for(var m=u,y;m!==null;){y=m;var b=y.stateNode;if(y.tag===5&&b!==null&&(y=b,p!==null&&(b=Ks(m,p),b!=null&&v.push(Js(m,b,y)))),S)break;m=m.return}0<v.length&&(f=new g(f,x,null,n,d),h.push({event:f,listeners:v}))}}if(!(t&7)){e:{if(f=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",f&&n!==ql&&(x=n.relatedTarget||n.fromElement)&&(Gn(x)||x[rn]))break e;if((g||f)&&(f=d.window===d?d:(f=d.ownerDocument)?f.defaultView||f.parentWindow:window,g?(x=n.relatedTarget||n.toElement,g=u,x=x?Gn(x):null,x!==null&&(S=hr(x),x!==S||x.tag!==5&&x.tag!==6)&&(x=null)):(g=null,x=u),g!==x)){if(v=yf,b="onMouseLeave",p="onMouseEnter",m="mouse",(e==="pointerout"||e==="pointerover")&&(v=vf,b="onPointerLeave",p="onPointerEnter",m="pointer"),S=g==null?f:Nr(g),y=x==null?f:Nr(x),f=new v(b,m+"leave",g,n,d),f.target=S,f.relatedTarget=y,b=null,Gn(d)===u&&(v=new v(p,m+"enter",x,n,d),v.target=y,v.relatedTarget=S,b=v),S=b,g&&x)t:{for(v=g,p=x,m=0,y=v;y;y=yr(y))m++;for(y=0,b=p;b;b=yr(b))y++;for(;0<m-y;)v=yr(v),m--;for(;0<y-m;)p=yr(p),y--;for(;m--;){if(v===p||p!==null&&v===p.alternate)break t;v=yr(v),p=yr(p)}v=null}else v=null;g!==null&&Pf(h,f,g,v,!1),x!==null&&S!==null&&Pf(h,S,x,v,!0)}}e:{if(f=u?Nr(u):window,g=f.nodeName&&f.nodeName.toLowerCase(),g==="select"||g==="input"&&f.type==="file")var j=Cw;else if(bf(f))if(sg)j=Ew;else{j=Tw;var k=kw}else(g=f.nodeName)&&g.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(j=_w);if(j&&(j=j(e,u))){rg(h,j,n,d);break e}k&&k(e,f,u),e==="focusout"&&(k=f._wrapperState)&&k.controlled&&f.type==="number"&&Hl(f,"number",f.value)}switch(k=u?Nr(u):window,e){case"focusin":(bf(k)||k.contentEditable==="true")&&(br=k,nc=u,Ms=null);break;case"focusout":Ms=nc=br=null;break;case"mousedown":rc=!0;break;case"contextmenu":case"mouseup":case"dragend":rc=!1,Tf(h,n,d);break;case"selectionchange":if(Dw)break;case"keydown":case"keyup":Tf(h,n,d)}var _;if(wu)e:{switch(e){case"compositionstart":var C="onCompositionStart";break e;case"compositionend":C="onCompositionEnd";break e;case"compositionupdate":C="onCompositionUpdate";break e}C=void 0}else Sr?tg(e,n)&&(C="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(C="onCompositionStart");C&&(eg&&n.locale!=="ko"&&(Sr||C!=="onCompositionStart"?C==="onCompositionEnd"&&Sr&&(_=Jp()):(xn=d,yu="value"in xn?xn.value:xn.textContent,Sr=!0)),k=Na(u,C),0<k.length&&(C=new xf(C,e,null,n,d),h.push({event:C,listeners:k}),_?C.data=_:(_=ng(n),_!==null&&(C.data=_)))),(_=ww?Sw(e,n):bw(e,n))&&(u=Na(u,"onBeforeInput"),0<u.length&&(d=new xf("onBeforeInput","beforeinput",null,n,d),h.push({event:d,listeners:u}),d.data=_))}mg(h,t)})}function Js(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Na(e,t){for(var n=t+"Capture",r=[];e!==null;){var s=e,i=s.stateNode;s.tag===5&&i!==null&&(s=i,i=Ks(e,n),i!=null&&r.unshift(Js(e,i,s)),i=Ks(e,t),i!=null&&r.push(Js(e,i,s))),e=e.return}return r}function yr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Pf(e,t,n,r,s){for(var i=t._reactName,a=[];n!==null&&n!==r;){var l=n,c=l.alternate,u=l.stateNode;if(c!==null&&c===r)break;l.tag===5&&u!==null&&(l=u,s?(c=Ks(n,i),c!=null&&a.unshift(Js(n,c,l))):s||(c=Ks(n,i),c!=null&&a.push(Js(n,c,l)))),n=n.return}a.length!==0&&e.push({event:t,listeners:a})}var Fw=/\r\n?/g,Vw=/\u0000|\uFFFD/g;function Af(e){return(typeof e=="string"?e:""+e).replace(Fw,`
`).replace(Vw,"")}function Oi(e,t,n){if(t=Af(t),Af(e)!==t&&n)throw Error(F(425))}function Ca(){}var sc=null,ic=null;function ac(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var oc=typeof setTimeout=="function"?setTimeout:void 0,Iw=typeof clearTimeout=="function"?clearTimeout:void 0,Df=typeof Promise=="function"?Promise:void 0,Ow=typeof queueMicrotask=="function"?queueMicrotask:typeof Df<"u"?function(e){return Df.resolve(null).then(e).catch(zw)}:oc;function zw(e){setTimeout(function(){throw e})}function sl(e,t){var n=t,r=0;do{var s=n.nextSibling;if(e.removeChild(n),s&&s.nodeType===8)if(n=s.data,n==="/$"){if(r===0){e.removeChild(s),qs(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=s}while(n);qs(t)}function jn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Mf(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var is=Math.random().toString(36).slice(2),Lt="__reactFiber$"+is,ei="__reactProps$"+is,rn="__reactContainer$"+is,lc="__reactEvents$"+is,$w="__reactListeners$"+is,Uw="__reactHandles$"+is;function Gn(e){var t=e[Lt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[rn]||n[Lt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Mf(e);e!==null;){if(n=e[Lt])return n;e=Mf(e)}return t}e=n,n=e.parentNode}return null}function Si(e){return e=e[Lt]||e[rn],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Nr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(F(33))}function lo(e){return e[ei]||null}var cc=[],Cr=-1;function Mn(e){return{current:e}}function le(e){0>Cr||(e.current=cc[Cr],cc[Cr]=null,Cr--)}function ae(e,t){Cr++,cc[Cr]=e.current,e.current=t}var En={},ze=Mn(En),Ye=Mn(!1),rr=En;function Br(e,t){var n=e.type.contextTypes;if(!n)return En;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var s={},i;for(i in n)s[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=s),s}function Ze(e){return e=e.childContextTypes,e!=null}function ka(){le(Ye),le(ze)}function Rf(e,t,n){if(ze.current!==En)throw Error(F(168));ae(ze,t),ae(Ye,n)}function gg(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var s in r)if(!(s in t))throw Error(F(108,kv(e)||"Unknown",s));return ge({},n,r)}function Ta(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||En,rr=ze.current,ae(ze,e),ae(Ye,Ye.current),!0}function Lf(e,t,n){var r=e.stateNode;if(!r)throw Error(F(169));n?(e=gg(e,t,rr),r.__reactInternalMemoizedMergedChildContext=e,le(Ye),le(ze),ae(ze,e)):le(Ye),ae(Ye,n)}var Gt=null,co=!1,il=!1;function yg(e){Gt===null?Gt=[e]:Gt.push(e)}function Bw(e){co=!0,yg(e)}function Rn(){if(!il&&Gt!==null){il=!0;var e=0,t=ne;try{var n=Gt;for(ne=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Gt=null,co=!1}catch(s){throw Gt!==null&&(Gt=Gt.slice(e+1)),Up(hu,Rn),s}finally{ne=t,il=!1}}return null}var kr=[],Tr=0,_a=null,Ea=0,pt=[],gt=0,sr=null,Xt=1,Yt="";function $n(e,t){kr[Tr++]=Ea,kr[Tr++]=_a,_a=e,Ea=t}function xg(e,t,n){pt[gt++]=Xt,pt[gt++]=Yt,pt[gt++]=sr,sr=e;var r=Xt;e=Yt;var s=32-Et(r)-1;r&=~(1<<s),n+=1;var i=32-Et(t)+s;if(30<i){var a=s-s%5;i=(r&(1<<a)-1).toString(32),r>>=a,s-=a,Xt=1<<32-Et(t)+s|n<<s|r,Yt=i+e}else Xt=1<<i|n<<s|r,Yt=e}function bu(e){e.return!==null&&($n(e,1),xg(e,1,0))}function ju(e){for(;e===_a;)_a=kr[--Tr],kr[Tr]=null,Ea=kr[--Tr],kr[Tr]=null;for(;e===sr;)sr=pt[--gt],pt[gt]=null,Yt=pt[--gt],pt[gt]=null,Xt=pt[--gt],pt[gt]=null}var st=null,rt=null,de=!1,Ct=null;function vg(e,t){var n=yt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Ff(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,st=e,rt=jn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,st=e,rt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=sr!==null?{id:Xt,overflow:Yt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=yt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,st=e,rt=null,!0):!1;default:return!1}}function uc(e){return(e.mode&1)!==0&&(e.flags&128)===0}function dc(e){if(de){var t=rt;if(t){var n=t;if(!Ff(e,t)){if(uc(e))throw Error(F(418));t=jn(n.nextSibling);var r=st;t&&Ff(e,t)?vg(r,n):(e.flags=e.flags&-4097|2,de=!1,st=e)}}else{if(uc(e))throw Error(F(418));e.flags=e.flags&-4097|2,de=!1,st=e}}}function Vf(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;st=e}function zi(e){if(e!==st)return!1;if(!de)return Vf(e),de=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!ac(e.type,e.memoizedProps)),t&&(t=rt)){if(uc(e))throw wg(),Error(F(418));for(;t;)vg(e,t),t=jn(t.nextSibling)}if(Vf(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(F(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){rt=jn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}rt=null}}else rt=st?jn(e.stateNode.nextSibling):null;return!0}function wg(){for(var e=rt;e;)e=jn(e.nextSibling)}function Hr(){rt=st=null,de=!1}function Nu(e){Ct===null?Ct=[e]:Ct.push(e)}var Hw=ln.ReactCurrentBatchConfig;function ms(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(F(309));var r=n.stateNode}if(!r)throw Error(F(147,e));var s=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(a){var l=s.refs;a===null?delete l[i]:l[i]=a},t._stringRef=i,t)}if(typeof e!="string")throw Error(F(284));if(!n._owner)throw Error(F(290,e))}return e}function $i(e,t){throw e=Object.prototype.toString.call(t),Error(F(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function If(e){var t=e._init;return t(e._payload)}function Sg(e){function t(p,m){if(e){var y=p.deletions;y===null?(p.deletions=[m],p.flags|=16):y.push(m)}}function n(p,m){if(!e)return null;for(;m!==null;)t(p,m),m=m.sibling;return null}function r(p,m){for(p=new Map;m!==null;)m.key!==null?p.set(m.key,m):p.set(m.index,m),m=m.sibling;return p}function s(p,m){return p=Tn(p,m),p.index=0,p.sibling=null,p}function i(p,m,y){return p.index=y,e?(y=p.alternate,y!==null?(y=y.index,y<m?(p.flags|=2,m):y):(p.flags|=2,m)):(p.flags|=1048576,m)}function a(p){return e&&p.alternate===null&&(p.flags|=2),p}function l(p,m,y,b){return m===null||m.tag!==6?(m=fl(y,p.mode,b),m.return=p,m):(m=s(m,y),m.return=p,m)}function c(p,m,y,b){var j=y.type;return j===wr?d(p,m,y.props.children,b,y.key):m!==null&&(m.elementType===j||typeof j=="object"&&j!==null&&j.$$typeof===mn&&If(j)===m.type)?(b=s(m,y.props),b.ref=ms(p,m,y),b.return=p,b):(b=da(y.type,y.key,y.props,null,p.mode,b),b.ref=ms(p,m,y),b.return=p,b)}function u(p,m,y,b){return m===null||m.tag!==4||m.stateNode.containerInfo!==y.containerInfo||m.stateNode.implementation!==y.implementation?(m=hl(y,p.mode,b),m.return=p,m):(m=s(m,y.children||[]),m.return=p,m)}function d(p,m,y,b,j){return m===null||m.tag!==7?(m=Jn(y,p.mode,b,j),m.return=p,m):(m=s(m,y),m.return=p,m)}function h(p,m,y){if(typeof m=="string"&&m!==""||typeof m=="number")return m=fl(""+m,p.mode,y),m.return=p,m;if(typeof m=="object"&&m!==null){switch(m.$$typeof){case Pi:return y=da(m.type,m.key,m.props,null,p.mode,y),y.ref=ms(p,null,m),y.return=p,y;case vr:return m=hl(m,p.mode,y),m.return=p,m;case mn:var b=m._init;return h(p,b(m._payload),y)}if(bs(m)||cs(m))return m=Jn(m,p.mode,y,null),m.return=p,m;$i(p,m)}return null}function f(p,m,y,b){var j=m!==null?m.key:null;if(typeof y=="string"&&y!==""||typeof y=="number")return j!==null?null:l(p,m,""+y,b);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case Pi:return y.key===j?c(p,m,y,b):null;case vr:return y.key===j?u(p,m,y,b):null;case mn:return j=y._init,f(p,m,j(y._payload),b)}if(bs(y)||cs(y))return j!==null?null:d(p,m,y,b,null);$i(p,y)}return null}function g(p,m,y,b,j){if(typeof b=="string"&&b!==""||typeof b=="number")return p=p.get(y)||null,l(m,p,""+b,j);if(typeof b=="object"&&b!==null){switch(b.$$typeof){case Pi:return p=p.get(b.key===null?y:b.key)||null,c(m,p,b,j);case vr:return p=p.get(b.key===null?y:b.key)||null,u(m,p,b,j);case mn:var k=b._init;return g(p,m,y,k(b._payload),j)}if(bs(b)||cs(b))return p=p.get(y)||null,d(m,p,b,j,null);$i(m,b)}return null}function x(p,m,y,b){for(var j=null,k=null,_=m,C=m=0,P=null;_!==null&&C<y.length;C++){_.index>C?(P=_,_=null):P=_.sibling;var D=f(p,_,y[C],b);if(D===null){_===null&&(_=P);break}e&&_&&D.alternate===null&&t(p,_),m=i(D,m,C),k===null?j=D:k.sibling=D,k=D,_=P}if(C===y.length)return n(p,_),de&&$n(p,C),j;if(_===null){for(;C<y.length;C++)_=h(p,y[C],b),_!==null&&(m=i(_,m,C),k===null?j=_:k.sibling=_,k=_);return de&&$n(p,C),j}for(_=r(p,_);C<y.length;C++)P=g(_,p,C,y[C],b),P!==null&&(e&&P.alternate!==null&&_.delete(P.key===null?C:P.key),m=i(P,m,C),k===null?j=P:k.sibling=P,k=P);return e&&_.forEach(function(V){return t(p,V)}),de&&$n(p,C),j}function v(p,m,y,b){var j=cs(y);if(typeof j!="function")throw Error(F(150));if(y=j.call(y),y==null)throw Error(F(151));for(var k=j=null,_=m,C=m=0,P=null,D=y.next();_!==null&&!D.done;C++,D=y.next()){_.index>C?(P=_,_=null):P=_.sibling;var V=f(p,_,D.value,b);if(V===null){_===null&&(_=P);break}e&&_&&V.alternate===null&&t(p,_),m=i(V,m,C),k===null?j=V:k.sibling=V,k=V,_=P}if(D.done)return n(p,_),de&&$n(p,C),j;if(_===null){for(;!D.done;C++,D=y.next())D=h(p,D.value,b),D!==null&&(m=i(D,m,C),k===null?j=D:k.sibling=D,k=D);return de&&$n(p,C),j}for(_=r(p,_);!D.done;C++,D=y.next())D=g(_,p,C,D.value,b),D!==null&&(e&&D.alternate!==null&&_.delete(D.key===null?C:D.key),m=i(D,m,C),k===null?j=D:k.sibling=D,k=D);return e&&_.forEach(function(A){return t(p,A)}),de&&$n(p,C),j}function S(p,m,y,b){if(typeof y=="object"&&y!==null&&y.type===wr&&y.key===null&&(y=y.props.children),typeof y=="object"&&y!==null){switch(y.$$typeof){case Pi:e:{for(var j=y.key,k=m;k!==null;){if(k.key===j){if(j=y.type,j===wr){if(k.tag===7){n(p,k.sibling),m=s(k,y.props.children),m.return=p,p=m;break e}}else if(k.elementType===j||typeof j=="object"&&j!==null&&j.$$typeof===mn&&If(j)===k.type){n(p,k.sibling),m=s(k,y.props),m.ref=ms(p,k,y),m.return=p,p=m;break e}n(p,k);break}else t(p,k);k=k.sibling}y.type===wr?(m=Jn(y.props.children,p.mode,b,y.key),m.return=p,p=m):(b=da(y.type,y.key,y.props,null,p.mode,b),b.ref=ms(p,m,y),b.return=p,p=b)}return a(p);case vr:e:{for(k=y.key;m!==null;){if(m.key===k)if(m.tag===4&&m.stateNode.containerInfo===y.containerInfo&&m.stateNode.implementation===y.implementation){n(p,m.sibling),m=s(m,y.children||[]),m.return=p,p=m;break e}else{n(p,m);break}else t(p,m);m=m.sibling}m=hl(y,p.mode,b),m.return=p,p=m}return a(p);case mn:return k=y._init,S(p,m,k(y._payload),b)}if(bs(y))return x(p,m,y,b);if(cs(y))return v(p,m,y,b);$i(p,y)}return typeof y=="string"&&y!==""||typeof y=="number"?(y=""+y,m!==null&&m.tag===6?(n(p,m.sibling),m=s(m,y),m.return=p,p=m):(n(p,m),m=fl(y,p.mode,b),m.return=p,p=m),a(p)):n(p,m)}return S}var Wr=Sg(!0),bg=Sg(!1),Pa=Mn(null),Aa=null,_r=null,Cu=null;function ku(){Cu=_r=Aa=null}function Tu(e){var t=Pa.current;le(Pa),e._currentValue=t}function fc(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function zr(e,t){Aa=e,Cu=_r=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Xe=!0),e.firstContext=null)}function vt(e){var t=e._currentValue;if(Cu!==e)if(e={context:e,memoizedValue:t,next:null},_r===null){if(Aa===null)throw Error(F(308));_r=e,Aa.dependencies={lanes:0,firstContext:e}}else _r=_r.next=e;return t}var Qn=null;function _u(e){Qn===null?Qn=[e]:Qn.push(e)}function jg(e,t,n,r){var s=t.interleaved;return s===null?(n.next=n,_u(t)):(n.next=s.next,s.next=n),t.interleaved=n,sn(e,r)}function sn(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var pn=!1;function Eu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Ng(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Zt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Nn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,ee&2){var s=r.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),r.pending=t,sn(e,n)}return s=r.interleaved,s===null?(t.next=t,_u(r)):(t.next=s.next,s.next=t),r.interleaved=t,sn(e,n)}function ia(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,mu(e,n)}}function Of(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var s=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?s=i=a:i=i.next=a,n=n.next}while(n!==null);i===null?s=i=t:i=i.next=t}else s=i=t;n={baseState:r.baseState,firstBaseUpdate:s,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Da(e,t,n,r){var s=e.updateQueue;pn=!1;var i=s.firstBaseUpdate,a=s.lastBaseUpdate,l=s.shared.pending;if(l!==null){s.shared.pending=null;var c=l,u=c.next;c.next=null,a===null?i=u:a.next=u,a=c;var d=e.alternate;d!==null&&(d=d.updateQueue,l=d.lastBaseUpdate,l!==a&&(l===null?d.firstBaseUpdate=u:l.next=u,d.lastBaseUpdate=c))}if(i!==null){var h=s.baseState;a=0,d=u=c=null,l=i;do{var f=l.lane,g=l.eventTime;if((r&f)===f){d!==null&&(d=d.next={eventTime:g,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var x=e,v=l;switch(f=t,g=n,v.tag){case 1:if(x=v.payload,typeof x=="function"){h=x.call(g,h,f);break e}h=x;break e;case 3:x.flags=x.flags&-65537|128;case 0:if(x=v.payload,f=typeof x=="function"?x.call(g,h,f):x,f==null)break e;h=ge({},h,f);break e;case 2:pn=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,f=s.effects,f===null?s.effects=[l]:f.push(l))}else g={eventTime:g,lane:f,tag:l.tag,payload:l.payload,callback:l.callback,next:null},d===null?(u=d=g,c=h):d=d.next=g,a|=f;if(l=l.next,l===null){if(l=s.shared.pending,l===null)break;f=l,l=f.next,f.next=null,s.lastBaseUpdate=f,s.shared.pending=null}}while(!0);if(d===null&&(c=h),s.baseState=c,s.firstBaseUpdate=u,s.lastBaseUpdate=d,t=s.shared.interleaved,t!==null){s=t;do a|=s.lane,s=s.next;while(s!==t)}else i===null&&(s.shared.lanes=0);ar|=a,e.lanes=a,e.memoizedState=h}}function zf(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],s=r.callback;if(s!==null){if(r.callback=null,r=n,typeof s!="function")throw Error(F(191,s));s.call(r)}}}var bi={},It=Mn(bi),ti=Mn(bi),ni=Mn(bi);function qn(e){if(e===bi)throw Error(F(174));return e}function Pu(e,t){switch(ae(ni,t),ae(ti,e),ae(It,bi),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Kl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Kl(t,e)}le(It),ae(It,t)}function Kr(){le(It),le(ti),le(ni)}function Cg(e){qn(ni.current);var t=qn(It.current),n=Kl(t,e.type);t!==n&&(ae(ti,e),ae(It,n))}function Au(e){ti.current===e&&(le(It),le(ti))}var he=Mn(0);function Ma(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var al=[];function Du(){for(var e=0;e<al.length;e++)al[e]._workInProgressVersionPrimary=null;al.length=0}var aa=ln.ReactCurrentDispatcher,ol=ln.ReactCurrentBatchConfig,ir=0,pe=null,Te=null,Ee=null,Ra=!1,Rs=!1,ri=0,Ww=0;function Le(){throw Error(F(321))}function Mu(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!At(e[n],t[n]))return!1;return!0}function Ru(e,t,n,r,s,i){if(ir=i,pe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,aa.current=e===null||e.memoizedState===null?qw:Xw,e=n(r,s),Rs){i=0;do{if(Rs=!1,ri=0,25<=i)throw Error(F(301));i+=1,Ee=Te=null,t.updateQueue=null,aa.current=Yw,e=n(r,s)}while(Rs)}if(aa.current=La,t=Te!==null&&Te.next!==null,ir=0,Ee=Te=pe=null,Ra=!1,t)throw Error(F(300));return e}function Lu(){var e=ri!==0;return ri=0,e}function Mt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ee===null?pe.memoizedState=Ee=e:Ee=Ee.next=e,Ee}function wt(){if(Te===null){var e=pe.alternate;e=e!==null?e.memoizedState:null}else e=Te.next;var t=Ee===null?pe.memoizedState:Ee.next;if(t!==null)Ee=t,Te=e;else{if(e===null)throw Error(F(310));Te=e,e={memoizedState:Te.memoizedState,baseState:Te.baseState,baseQueue:Te.baseQueue,queue:Te.queue,next:null},Ee===null?pe.memoizedState=Ee=e:Ee=Ee.next=e}return Ee}function si(e,t){return typeof t=="function"?t(e):t}function ll(e){var t=wt(),n=t.queue;if(n===null)throw Error(F(311));n.lastRenderedReducer=e;var r=Te,s=r.baseQueue,i=n.pending;if(i!==null){if(s!==null){var a=s.next;s.next=i.next,i.next=a}r.baseQueue=s=i,n.pending=null}if(s!==null){i=s.next,r=r.baseState;var l=a=null,c=null,u=i;do{var d=u.lane;if((ir&d)===d)c!==null&&(c=c.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var h={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};c===null?(l=c=h,a=r):c=c.next=h,pe.lanes|=d,ar|=d}u=u.next}while(u!==null&&u!==i);c===null?a=r:c.next=l,At(r,t.memoizedState)||(Xe=!0),t.memoizedState=r,t.baseState=a,t.baseQueue=c,n.lastRenderedState=r}if(e=n.interleaved,e!==null){s=e;do i=s.lane,pe.lanes|=i,ar|=i,s=s.next;while(s!==e)}else s===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function cl(e){var t=wt(),n=t.queue;if(n===null)throw Error(F(311));n.lastRenderedReducer=e;var r=n.dispatch,s=n.pending,i=t.memoizedState;if(s!==null){n.pending=null;var a=s=s.next;do i=e(i,a.action),a=a.next;while(a!==s);At(i,t.memoizedState)||(Xe=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function kg(){}function Tg(e,t){var n=pe,r=wt(),s=t(),i=!At(r.memoizedState,s);if(i&&(r.memoizedState=s,Xe=!0),r=r.queue,Fu(Pg.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||Ee!==null&&Ee.memoizedState.tag&1){if(n.flags|=2048,ii(9,Eg.bind(null,n,r,s,t),void 0,null),Pe===null)throw Error(F(349));ir&30||_g(n,t,s)}return s}function _g(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=pe.updateQueue,t===null?(t={lastEffect:null,stores:null},pe.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Eg(e,t,n,r){t.value=n,t.getSnapshot=r,Ag(t)&&Dg(e)}function Pg(e,t,n){return n(function(){Ag(t)&&Dg(e)})}function Ag(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!At(e,n)}catch{return!0}}function Dg(e){var t=sn(e,1);t!==null&&Pt(t,e,1,-1)}function $f(e){var t=Mt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:si,lastRenderedState:e},t.queue=e,e=e.dispatch=Qw.bind(null,pe,e),[t.memoizedState,e]}function ii(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=pe.updateQueue,t===null?(t={lastEffect:null,stores:null},pe.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Mg(){return wt().memoizedState}function oa(e,t,n,r){var s=Mt();pe.flags|=e,s.memoizedState=ii(1|t,n,void 0,r===void 0?null:r)}function uo(e,t,n,r){var s=wt();r=r===void 0?null:r;var i=void 0;if(Te!==null){var a=Te.memoizedState;if(i=a.destroy,r!==null&&Mu(r,a.deps)){s.memoizedState=ii(t,n,i,r);return}}pe.flags|=e,s.memoizedState=ii(1|t,n,i,r)}function Uf(e,t){return oa(8390656,8,e,t)}function Fu(e,t){return uo(2048,8,e,t)}function Rg(e,t){return uo(4,2,e,t)}function Lg(e,t){return uo(4,4,e,t)}function Fg(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Vg(e,t,n){return n=n!=null?n.concat([e]):null,uo(4,4,Fg.bind(null,t,e),n)}function Vu(){}function Ig(e,t){var n=wt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Mu(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Og(e,t){var n=wt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Mu(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function zg(e,t,n){return ir&21?(At(n,t)||(n=Wp(),pe.lanes|=n,ar|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Xe=!0),e.memoizedState=n)}function Kw(e,t){var n=ne;ne=n!==0&&4>n?n:4,e(!0);var r=ol.transition;ol.transition={};try{e(!1),t()}finally{ne=n,ol.transition=r}}function $g(){return wt().memoizedState}function Gw(e,t,n){var r=kn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Ug(e))Bg(t,n);else if(n=jg(e,t,n,r),n!==null){var s=We();Pt(n,e,r,s),Hg(n,t,r)}}function Qw(e,t,n){var r=kn(e),s={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ug(e))Bg(t,s);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var a=t.lastRenderedState,l=i(a,n);if(s.hasEagerState=!0,s.eagerState=l,At(l,a)){var c=t.interleaved;c===null?(s.next=s,_u(t)):(s.next=c.next,c.next=s),t.interleaved=s;return}}catch{}finally{}n=jg(e,t,s,r),n!==null&&(s=We(),Pt(n,e,r,s),Hg(n,t,r))}}function Ug(e){var t=e.alternate;return e===pe||t!==null&&t===pe}function Bg(e,t){Rs=Ra=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Hg(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,mu(e,n)}}var La={readContext:vt,useCallback:Le,useContext:Le,useEffect:Le,useImperativeHandle:Le,useInsertionEffect:Le,useLayoutEffect:Le,useMemo:Le,useReducer:Le,useRef:Le,useState:Le,useDebugValue:Le,useDeferredValue:Le,useTransition:Le,useMutableSource:Le,useSyncExternalStore:Le,useId:Le,unstable_isNewReconciler:!1},qw={readContext:vt,useCallback:function(e,t){return Mt().memoizedState=[e,t===void 0?null:t],e},useContext:vt,useEffect:Uf,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,oa(4194308,4,Fg.bind(null,t,e),n)},useLayoutEffect:function(e,t){return oa(4194308,4,e,t)},useInsertionEffect:function(e,t){return oa(4,2,e,t)},useMemo:function(e,t){var n=Mt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Mt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Gw.bind(null,pe,e),[r.memoizedState,e]},useRef:function(e){var t=Mt();return e={current:e},t.memoizedState=e},useState:$f,useDebugValue:Vu,useDeferredValue:function(e){return Mt().memoizedState=e},useTransition:function(){var e=$f(!1),t=e[0];return e=Kw.bind(null,e[1]),Mt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=pe,s=Mt();if(de){if(n===void 0)throw Error(F(407));n=n()}else{if(n=t(),Pe===null)throw Error(F(349));ir&30||_g(r,t,n)}s.memoizedState=n;var i={value:n,getSnapshot:t};return s.queue=i,Uf(Pg.bind(null,r,i,e),[e]),r.flags|=2048,ii(9,Eg.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=Mt(),t=Pe.identifierPrefix;if(de){var n=Yt,r=Xt;n=(r&~(1<<32-Et(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=ri++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Ww++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Xw={readContext:vt,useCallback:Ig,useContext:vt,useEffect:Fu,useImperativeHandle:Vg,useInsertionEffect:Rg,useLayoutEffect:Lg,useMemo:Og,useReducer:ll,useRef:Mg,useState:function(){return ll(si)},useDebugValue:Vu,useDeferredValue:function(e){var t=wt();return zg(t,Te.memoizedState,e)},useTransition:function(){var e=ll(si)[0],t=wt().memoizedState;return[e,t]},useMutableSource:kg,useSyncExternalStore:Tg,useId:$g,unstable_isNewReconciler:!1},Yw={readContext:vt,useCallback:Ig,useContext:vt,useEffect:Fu,useImperativeHandle:Vg,useInsertionEffect:Rg,useLayoutEffect:Lg,useMemo:Og,useReducer:cl,useRef:Mg,useState:function(){return cl(si)},useDebugValue:Vu,useDeferredValue:function(e){var t=wt();return Te===null?t.memoizedState=e:zg(t,Te.memoizedState,e)},useTransition:function(){var e=cl(si)[0],t=wt().memoizedState;return[e,t]},useMutableSource:kg,useSyncExternalStore:Tg,useId:$g,unstable_isNewReconciler:!1};function jt(e,t){if(e&&e.defaultProps){t=ge({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function hc(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:ge({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var fo={isMounted:function(e){return(e=e._reactInternals)?hr(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=We(),s=kn(e),i=Zt(r,s);i.payload=t,n!=null&&(i.callback=n),t=Nn(e,i,s),t!==null&&(Pt(t,e,s,r),ia(t,e,s))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=We(),s=kn(e),i=Zt(r,s);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Nn(e,i,s),t!==null&&(Pt(t,e,s,r),ia(t,e,s))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=We(),r=kn(e),s=Zt(n,r);s.tag=2,t!=null&&(s.callback=t),t=Nn(e,s,r),t!==null&&(Pt(t,e,r,n),ia(t,e,r))}};function Bf(e,t,n,r,s,i,a){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,a):t.prototype&&t.prototype.isPureReactComponent?!Ys(n,r)||!Ys(s,i):!0}function Wg(e,t,n){var r=!1,s=En,i=t.contextType;return typeof i=="object"&&i!==null?i=vt(i):(s=Ze(t)?rr:ze.current,r=t.contextTypes,i=(r=r!=null)?Br(e,s):En),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=fo,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=s,e.__reactInternalMemoizedMaskedChildContext=i),t}function Hf(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&fo.enqueueReplaceState(t,t.state,null)}function mc(e,t,n,r){var s=e.stateNode;s.props=n,s.state=e.memoizedState,s.refs={},Eu(e);var i=t.contextType;typeof i=="object"&&i!==null?s.context=vt(i):(i=Ze(t)?rr:ze.current,s.context=Br(e,i)),s.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(hc(e,t,i,n),s.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(t=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),t!==s.state&&fo.enqueueReplaceState(s,s.state,null),Da(e,n,s,r),s.state=e.memoizedState),typeof s.componentDidMount=="function"&&(e.flags|=4194308)}function Gr(e,t){try{var n="",r=t;do n+=Cv(r),r=r.return;while(r);var s=n}catch(i){s=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:s,digest:null}}function ul(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function pc(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Zw=typeof WeakMap=="function"?WeakMap:Map;function Kg(e,t,n){n=Zt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Va||(Va=!0,Cc=r),pc(e,t)},n}function Gg(e,t,n){n=Zt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var s=t.value;n.payload=function(){return r(s)},n.callback=function(){pc(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){pc(e,t),typeof r!="function"&&(Cn===null?Cn=new Set([this]):Cn.add(this));var a=t.stack;this.componentDidCatch(t.value,{componentStack:a!==null?a:""})}),n}function Wf(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Zw;var s=new Set;r.set(t,s)}else s=r.get(t),s===void 0&&(s=new Set,r.set(t,s));s.has(n)||(s.add(n),e=f1.bind(null,e,t,n),t.then(e,e))}function Kf(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Gf(e,t,n,r,s){return e.mode&1?(e.flags|=65536,e.lanes=s,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Zt(-1,1),t.tag=2,Nn(n,t,1))),n.lanes|=1),e)}var Jw=ln.ReactCurrentOwner,Xe=!1;function $e(e,t,n,r){t.child=e===null?bg(t,null,n,r):Wr(t,e.child,n,r)}function Qf(e,t,n,r,s){n=n.render;var i=t.ref;return zr(t,s),r=Ru(e,t,n,r,i,s),n=Lu(),e!==null&&!Xe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,an(e,t,s)):(de&&n&&bu(t),t.flags|=1,$e(e,t,r,s),t.child)}function qf(e,t,n,r,s){if(e===null){var i=n.type;return typeof i=="function"&&!Wu(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,Qg(e,t,i,r,s)):(e=da(n.type,null,r,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&s)){var a=i.memoizedProps;if(n=n.compare,n=n!==null?n:Ys,n(a,r)&&e.ref===t.ref)return an(e,t,s)}return t.flags|=1,e=Tn(i,r),e.ref=t.ref,e.return=t,t.child=e}function Qg(e,t,n,r,s){if(e!==null){var i=e.memoizedProps;if(Ys(i,r)&&e.ref===t.ref)if(Xe=!1,t.pendingProps=r=i,(e.lanes&s)!==0)e.flags&131072&&(Xe=!0);else return t.lanes=e.lanes,an(e,t,s)}return gc(e,t,n,r,s)}function qg(e,t,n){var r=t.pendingProps,s=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ae(Pr,tt),tt|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ae(Pr,tt),tt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,ae(Pr,tt),tt|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,ae(Pr,tt),tt|=r;return $e(e,t,s,n),t.child}function Xg(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function gc(e,t,n,r,s){var i=Ze(n)?rr:ze.current;return i=Br(t,i),zr(t,s),n=Ru(e,t,n,r,i,s),r=Lu(),e!==null&&!Xe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,an(e,t,s)):(de&&r&&bu(t),t.flags|=1,$e(e,t,n,s),t.child)}function Xf(e,t,n,r,s){if(Ze(n)){var i=!0;Ta(t)}else i=!1;if(zr(t,s),t.stateNode===null)la(e,t),Wg(t,n,r),mc(t,n,r,s),r=!0;else if(e===null){var a=t.stateNode,l=t.memoizedProps;a.props=l;var c=a.context,u=n.contextType;typeof u=="object"&&u!==null?u=vt(u):(u=Ze(n)?rr:ze.current,u=Br(t,u));var d=n.getDerivedStateFromProps,h=typeof d=="function"||typeof a.getSnapshotBeforeUpdate=="function";h||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(l!==r||c!==u)&&Hf(t,a,r,u),pn=!1;var f=t.memoizedState;a.state=f,Da(t,r,a,s),c=t.memoizedState,l!==r||f!==c||Ye.current||pn?(typeof d=="function"&&(hc(t,n,d,r),c=t.memoizedState),(l=pn||Bf(t,n,l,r,f,c,u))?(h||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount()),typeof a.componentDidMount=="function"&&(t.flags|=4194308)):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=c),a.props=r,a.state=c,a.context=u,r=l):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,Ng(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:jt(t.type,l),a.props=u,h=t.pendingProps,f=a.context,c=n.contextType,typeof c=="object"&&c!==null?c=vt(c):(c=Ze(n)?rr:ze.current,c=Br(t,c));var g=n.getDerivedStateFromProps;(d=typeof g=="function"||typeof a.getSnapshotBeforeUpdate=="function")||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(l!==h||f!==c)&&Hf(t,a,r,c),pn=!1,f=t.memoizedState,a.state=f,Da(t,r,a,s);var x=t.memoizedState;l!==h||f!==x||Ye.current||pn?(typeof g=="function"&&(hc(t,n,g,r),x=t.memoizedState),(u=pn||Bf(t,n,u,r,f,x,c)||!1)?(d||typeof a.UNSAFE_componentWillUpdate!="function"&&typeof a.componentWillUpdate!="function"||(typeof a.componentWillUpdate=="function"&&a.componentWillUpdate(r,x,c),typeof a.UNSAFE_componentWillUpdate=="function"&&a.UNSAFE_componentWillUpdate(r,x,c)),typeof a.componentDidUpdate=="function"&&(t.flags|=4),typeof a.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof a.componentDidUpdate!="function"||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=x),a.props=r,a.state=x,a.context=c,r=u):(typeof a.componentDidUpdate!="function"||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return yc(e,t,n,r,i,s)}function yc(e,t,n,r,s,i){Xg(e,t);var a=(t.flags&128)!==0;if(!r&&!a)return s&&Lf(t,n,!1),an(e,t,i);r=t.stateNode,Jw.current=t;var l=a&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&a?(t.child=Wr(t,e.child,null,i),t.child=Wr(t,null,l,i)):$e(e,t,l,i),t.memoizedState=r.state,s&&Lf(t,n,!0),t.child}function Yg(e){var t=e.stateNode;t.pendingContext?Rf(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Rf(e,t.context,!1),Pu(e,t.containerInfo)}function Yf(e,t,n,r,s){return Hr(),Nu(s),t.flags|=256,$e(e,t,n,r),t.child}var xc={dehydrated:null,treeContext:null,retryLane:0};function vc(e){return{baseLanes:e,cachePool:null,transitions:null}}function Zg(e,t,n){var r=t.pendingProps,s=he.current,i=!1,a=(t.flags&128)!==0,l;if((l=a)||(l=e!==null&&e.memoizedState===null?!1:(s&2)!==0),l?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(s|=1),ae(he,s&1),e===null)return dc(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(a=r.children,e=r.fallback,i?(r=t.mode,i=t.child,a={mode:"hidden",children:a},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=a):i=po(a,r,0,null),e=Jn(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=vc(n),t.memoizedState=xc,e):Iu(t,a));if(s=e.memoizedState,s!==null&&(l=s.dehydrated,l!==null))return e1(e,t,a,r,l,s,n);if(i){i=r.fallback,a=t.mode,s=e.child,l=s.sibling;var c={mode:"hidden",children:r.children};return!(a&1)&&t.child!==s?(r=t.child,r.childLanes=0,r.pendingProps=c,t.deletions=null):(r=Tn(s,c),r.subtreeFlags=s.subtreeFlags&14680064),l!==null?i=Tn(l,i):(i=Jn(i,a,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,a=e.child.memoizedState,a=a===null?vc(n):{baseLanes:a.baseLanes|n,cachePool:null,transitions:a.transitions},i.memoizedState=a,i.childLanes=e.childLanes&~n,t.memoizedState=xc,r}return i=e.child,e=i.sibling,r=Tn(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Iu(e,t){return t=po({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Ui(e,t,n,r){return r!==null&&Nu(r),Wr(t,e.child,null,n),e=Iu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function e1(e,t,n,r,s,i,a){if(n)return t.flags&256?(t.flags&=-257,r=ul(Error(F(422))),Ui(e,t,a,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,s=t.mode,r=po({mode:"visible",children:r.children},s,0,null),i=Jn(i,s,a,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&Wr(t,e.child,null,a),t.child.memoizedState=vc(a),t.memoizedState=xc,i);if(!(t.mode&1))return Ui(e,t,a,null);if(s.data==="$!"){if(r=s.nextSibling&&s.nextSibling.dataset,r)var l=r.dgst;return r=l,i=Error(F(419)),r=ul(i,r,void 0),Ui(e,t,a,r)}if(l=(a&e.childLanes)!==0,Xe||l){if(r=Pe,r!==null){switch(a&-a){case 4:s=2;break;case 16:s=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:s=32;break;case 536870912:s=268435456;break;default:s=0}s=s&(r.suspendedLanes|a)?0:s,s!==0&&s!==i.retryLane&&(i.retryLane=s,sn(e,s),Pt(r,e,s,-1))}return Hu(),r=ul(Error(F(421))),Ui(e,t,a,r)}return s.data==="$?"?(t.flags|=128,t.child=e.child,t=h1.bind(null,e),s._reactRetry=t,null):(e=i.treeContext,rt=jn(s.nextSibling),st=t,de=!0,Ct=null,e!==null&&(pt[gt++]=Xt,pt[gt++]=Yt,pt[gt++]=sr,Xt=e.id,Yt=e.overflow,sr=t),t=Iu(t,r.children),t.flags|=4096,t)}function Zf(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),fc(e.return,t,n)}function dl(e,t,n,r,s){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:s}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=s)}function Jg(e,t,n){var r=t.pendingProps,s=r.revealOrder,i=r.tail;if($e(e,t,r.children,n),r=he.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Zf(e,n,t);else if(e.tag===19)Zf(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(ae(he,r),!(t.mode&1))t.memoizedState=null;else switch(s){case"forwards":for(n=t.child,s=null;n!==null;)e=n.alternate,e!==null&&Ma(e)===null&&(s=n),n=n.sibling;n=s,n===null?(s=t.child,t.child=null):(s=n.sibling,n.sibling=null),dl(t,!1,s,n,i);break;case"backwards":for(n=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&Ma(e)===null){t.child=s;break}e=s.sibling,s.sibling=n,n=s,s=e}dl(t,!0,n,null,i);break;case"together":dl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function la(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function an(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),ar|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(F(153));if(t.child!==null){for(e=t.child,n=Tn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Tn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function t1(e,t,n){switch(t.tag){case 3:Yg(t),Hr();break;case 5:Cg(t);break;case 1:Ze(t.type)&&Ta(t);break;case 4:Pu(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,s=t.memoizedProps.value;ae(Pa,r._currentValue),r._currentValue=s;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(ae(he,he.current&1),t.flags|=128,null):n&t.child.childLanes?Zg(e,t,n):(ae(he,he.current&1),e=an(e,t,n),e!==null?e.sibling:null);ae(he,he.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Jg(e,t,n);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),ae(he,he.current),r)break;return null;case 22:case 23:return t.lanes=0,qg(e,t,n)}return an(e,t,n)}var e0,wc,t0,n0;e0=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};wc=function(){};t0=function(e,t,n,r){var s=e.memoizedProps;if(s!==r){e=t.stateNode,qn(It.current);var i=null;switch(n){case"input":s=Ul(e,s),r=Ul(e,r),i=[];break;case"select":s=ge({},s,{value:void 0}),r=ge({},r,{value:void 0}),i=[];break;case"textarea":s=Wl(e,s),r=Wl(e,r),i=[];break;default:typeof s.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Ca)}Gl(n,r);var a;n=null;for(u in s)if(!r.hasOwnProperty(u)&&s.hasOwnProperty(u)&&s[u]!=null)if(u==="style"){var l=s[u];for(a in l)l.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Hs.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var c=r[u];if(l=s!=null?s[u]:void 0,r.hasOwnProperty(u)&&c!==l&&(c!=null||l!=null))if(u==="style")if(l){for(a in l)!l.hasOwnProperty(a)||c&&c.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in c)c.hasOwnProperty(a)&&l[a]!==c[a]&&(n||(n={}),n[a]=c[a])}else n||(i||(i=[]),i.push(u,n)),n=c;else u==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,l=l?l.__html:void 0,c!=null&&l!==c&&(i=i||[]).push(u,c)):u==="children"?typeof c!="string"&&typeof c!="number"||(i=i||[]).push(u,""+c):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Hs.hasOwnProperty(u)?(c!=null&&u==="onScroll"&&oe("scroll",e),i||l===c||(i=[])):(i=i||[]).push(u,c))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};n0=function(e,t,n,r){n!==r&&(t.flags|=4)};function ps(e,t){if(!de)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Fe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags&14680064,r|=s.flags&14680064,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags,r|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function n1(e,t,n){var r=t.pendingProps;switch(ju(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Fe(t),null;case 1:return Ze(t.type)&&ka(),Fe(t),null;case 3:return r=t.stateNode,Kr(),le(Ye),le(ze),Du(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(zi(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ct!==null&&(_c(Ct),Ct=null))),wc(e,t),Fe(t),null;case 5:Au(t);var s=qn(ni.current);if(n=t.type,e!==null&&t.stateNode!=null)t0(e,t,n,r,s),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(F(166));return Fe(t),null}if(e=qn(It.current),zi(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[Lt]=t,r[ei]=i,e=(t.mode&1)!==0,n){case"dialog":oe("cancel",r),oe("close",r);break;case"iframe":case"object":case"embed":oe("load",r);break;case"video":case"audio":for(s=0;s<Ns.length;s++)oe(Ns[s],r);break;case"source":oe("error",r);break;case"img":case"image":case"link":oe("error",r),oe("load",r);break;case"details":oe("toggle",r);break;case"input":of(r,i),oe("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},oe("invalid",r);break;case"textarea":cf(r,i),oe("invalid",r)}Gl(n,i),s=null;for(var a in i)if(i.hasOwnProperty(a)){var l=i[a];a==="children"?typeof l=="string"?r.textContent!==l&&(i.suppressHydrationWarning!==!0&&Oi(r.textContent,l,e),s=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(i.suppressHydrationWarning!==!0&&Oi(r.textContent,l,e),s=["children",""+l]):Hs.hasOwnProperty(a)&&l!=null&&a==="onScroll"&&oe("scroll",r)}switch(n){case"input":Ai(r),lf(r,i,!0);break;case"textarea":Ai(r),uf(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=Ca)}r=s,t.updateQueue=r,r!==null&&(t.flags|=4)}else{a=s.nodeType===9?s:s.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Pp(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=a.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=a.createElement(n,{is:r.is}):(e=a.createElement(n),n==="select"&&(a=e,r.multiple?a.multiple=!0:r.size&&(a.size=r.size))):e=a.createElementNS(e,n),e[Lt]=t,e[ei]=r,e0(e,t,!1,!1),t.stateNode=e;e:{switch(a=Ql(n,r),n){case"dialog":oe("cancel",e),oe("close",e),s=r;break;case"iframe":case"object":case"embed":oe("load",e),s=r;break;case"video":case"audio":for(s=0;s<Ns.length;s++)oe(Ns[s],e);s=r;break;case"source":oe("error",e),s=r;break;case"img":case"image":case"link":oe("error",e),oe("load",e),s=r;break;case"details":oe("toggle",e),s=r;break;case"input":of(e,r),s=Ul(e,r),oe("invalid",e);break;case"option":s=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},s=ge({},r,{value:void 0}),oe("invalid",e);break;case"textarea":cf(e,r),s=Wl(e,r),oe("invalid",e);break;default:s=r}Gl(n,s),l=s;for(i in l)if(l.hasOwnProperty(i)){var c=l[i];i==="style"?Mp(e,c):i==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,c!=null&&Ap(e,c)):i==="children"?typeof c=="string"?(n!=="textarea"||c!=="")&&Ws(e,c):typeof c=="number"&&Ws(e,""+c):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Hs.hasOwnProperty(i)?c!=null&&i==="onScroll"&&oe("scroll",e):c!=null&&lu(e,i,c,a))}switch(n){case"input":Ai(e),lf(e,r,!1);break;case"textarea":Ai(e),uf(e);break;case"option":r.value!=null&&e.setAttribute("value",""+_n(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Fr(e,!!r.multiple,i,!1):r.defaultValue!=null&&Fr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof s.onClick=="function"&&(e.onclick=Ca)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Fe(t),null;case 6:if(e&&t.stateNode!=null)n0(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(F(166));if(n=qn(ni.current),qn(It.current),zi(t)){if(r=t.stateNode,n=t.memoizedProps,r[Lt]=t,(i=r.nodeValue!==n)&&(e=st,e!==null))switch(e.tag){case 3:Oi(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Oi(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Lt]=t,t.stateNode=r}return Fe(t),null;case 13:if(le(he),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(de&&rt!==null&&t.mode&1&&!(t.flags&128))wg(),Hr(),t.flags|=98560,i=!1;else if(i=zi(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(F(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(F(317));i[Lt]=t}else Hr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Fe(t),i=!1}else Ct!==null&&(_c(Ct),Ct=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||he.current&1?_e===0&&(_e=3):Hu())),t.updateQueue!==null&&(t.flags|=4),Fe(t),null);case 4:return Kr(),wc(e,t),e===null&&Zs(t.stateNode.containerInfo),Fe(t),null;case 10:return Tu(t.type._context),Fe(t),null;case 17:return Ze(t.type)&&ka(),Fe(t),null;case 19:if(le(he),i=t.memoizedState,i===null)return Fe(t),null;if(r=(t.flags&128)!==0,a=i.rendering,a===null)if(r)ps(i,!1);else{if(_e!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(a=Ma(e),a!==null){for(t.flags|=128,ps(i,!1),r=a.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,a=i.alternate,a===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=a.childLanes,i.lanes=a.lanes,i.child=a.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=a.memoizedProps,i.memoizedState=a.memoizedState,i.updateQueue=a.updateQueue,i.type=a.type,e=a.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return ae(he,he.current&1|2),t.child}e=e.sibling}i.tail!==null&&Se()>Qr&&(t.flags|=128,r=!0,ps(i,!1),t.lanes=4194304)}else{if(!r)if(e=Ma(a),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),ps(i,!0),i.tail===null&&i.tailMode==="hidden"&&!a.alternate&&!de)return Fe(t),null}else 2*Se()-i.renderingStartTime>Qr&&n!==1073741824&&(t.flags|=128,r=!0,ps(i,!1),t.lanes=4194304);i.isBackwards?(a.sibling=t.child,t.child=a):(n=i.last,n!==null?n.sibling=a:t.child=a,i.last=a)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Se(),t.sibling=null,n=he.current,ae(he,r?n&1|2:n&1),t):(Fe(t),null);case 22:case 23:return Bu(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?tt&1073741824&&(Fe(t),t.subtreeFlags&6&&(t.flags|=8192)):Fe(t),null;case 24:return null;case 25:return null}throw Error(F(156,t.tag))}function r1(e,t){switch(ju(t),t.tag){case 1:return Ze(t.type)&&ka(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Kr(),le(Ye),le(ze),Du(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Au(t),null;case 13:if(le(he),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(F(340));Hr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return le(he),null;case 4:return Kr(),null;case 10:return Tu(t.type._context),null;case 22:case 23:return Bu(),null;case 24:return null;default:return null}}var Bi=!1,Ie=!1,s1=typeof WeakSet=="function"?WeakSet:Set,B=null;function Er(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){xe(e,t,r)}else n.current=null}function Sc(e,t,n){try{n()}catch(r){xe(e,t,r)}}var Jf=!1;function i1(e,t){if(sc=ba,e=og(),Su(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var s=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var a=0,l=-1,c=-1,u=0,d=0,h=e,f=null;t:for(;;){for(var g;h!==n||s!==0&&h.nodeType!==3||(l=a+s),h!==i||r!==0&&h.nodeType!==3||(c=a+r),h.nodeType===3&&(a+=h.nodeValue.length),(g=h.firstChild)!==null;)f=h,h=g;for(;;){if(h===e)break t;if(f===n&&++u===s&&(l=a),f===i&&++d===r&&(c=a),(g=h.nextSibling)!==null)break;h=f,f=h.parentNode}h=g}n=l===-1||c===-1?null:{start:l,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(ic={focusedElem:e,selectionRange:n},ba=!1,B=t;B!==null;)if(t=B,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,B=e;else for(;B!==null;){t=B;try{var x=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(x!==null){var v=x.memoizedProps,S=x.memoizedState,p=t.stateNode,m=p.getSnapshotBeforeUpdate(t.elementType===t.type?v:jt(t.type,v),S);p.__reactInternalSnapshotBeforeUpdate=m}break;case 3:var y=t.stateNode.containerInfo;y.nodeType===1?y.textContent="":y.nodeType===9&&y.documentElement&&y.removeChild(y.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(F(163))}}catch(b){xe(t,t.return,b)}if(e=t.sibling,e!==null){e.return=t.return,B=e;break}B=t.return}return x=Jf,Jf=!1,x}function Ls(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var s=r=r.next;do{if((s.tag&e)===e){var i=s.destroy;s.destroy=void 0,i!==void 0&&Sc(t,n,i)}s=s.next}while(s!==r)}}function ho(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function bc(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function r0(e){var t=e.alternate;t!==null&&(e.alternate=null,r0(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Lt],delete t[ei],delete t[lc],delete t[$w],delete t[Uw])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function s0(e){return e.tag===5||e.tag===3||e.tag===4}function eh(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||s0(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function jc(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Ca));else if(r!==4&&(e=e.child,e!==null))for(jc(e,t,n),e=e.sibling;e!==null;)jc(e,t,n),e=e.sibling}function Nc(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Nc(e,t,n),e=e.sibling;e!==null;)Nc(e,t,n),e=e.sibling}var Ae=null,Nt=!1;function dn(e,t,n){for(n=n.child;n!==null;)i0(e,t,n),n=n.sibling}function i0(e,t,n){if(Vt&&typeof Vt.onCommitFiberUnmount=="function")try{Vt.onCommitFiberUnmount(so,n)}catch{}switch(n.tag){case 5:Ie||Er(n,t);case 6:var r=Ae,s=Nt;Ae=null,dn(e,t,n),Ae=r,Nt=s,Ae!==null&&(Nt?(e=Ae,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Ae.removeChild(n.stateNode));break;case 18:Ae!==null&&(Nt?(e=Ae,n=n.stateNode,e.nodeType===8?sl(e.parentNode,n):e.nodeType===1&&sl(e,n),qs(e)):sl(Ae,n.stateNode));break;case 4:r=Ae,s=Nt,Ae=n.stateNode.containerInfo,Nt=!0,dn(e,t,n),Ae=r,Nt=s;break;case 0:case 11:case 14:case 15:if(!Ie&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){s=r=r.next;do{var i=s,a=i.destroy;i=i.tag,a!==void 0&&(i&2||i&4)&&Sc(n,t,a),s=s.next}while(s!==r)}dn(e,t,n);break;case 1:if(!Ie&&(Er(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){xe(n,t,l)}dn(e,t,n);break;case 21:dn(e,t,n);break;case 22:n.mode&1?(Ie=(r=Ie)||n.memoizedState!==null,dn(e,t,n),Ie=r):dn(e,t,n);break;default:dn(e,t,n)}}function th(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new s1),t.forEach(function(r){var s=m1.bind(null,e,r);n.has(r)||(n.add(r),r.then(s,s))})}}function St(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var s=n[r];try{var i=e,a=t,l=a;e:for(;l!==null;){switch(l.tag){case 5:Ae=l.stateNode,Nt=!1;break e;case 3:Ae=l.stateNode.containerInfo,Nt=!0;break e;case 4:Ae=l.stateNode.containerInfo,Nt=!0;break e}l=l.return}if(Ae===null)throw Error(F(160));i0(i,a,s),Ae=null,Nt=!1;var c=s.alternate;c!==null&&(c.return=null),s.return=null}catch(u){xe(s,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)a0(t,e),t=t.sibling}function a0(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(St(t,e),Dt(e),r&4){try{Ls(3,e,e.return),ho(3,e)}catch(v){xe(e,e.return,v)}try{Ls(5,e,e.return)}catch(v){xe(e,e.return,v)}}break;case 1:St(t,e),Dt(e),r&512&&n!==null&&Er(n,n.return);break;case 5:if(St(t,e),Dt(e),r&512&&n!==null&&Er(n,n.return),e.flags&32){var s=e.stateNode;try{Ws(s,"")}catch(v){xe(e,e.return,v)}}if(r&4&&(s=e.stateNode,s!=null)){var i=e.memoizedProps,a=n!==null?n.memoizedProps:i,l=e.type,c=e.updateQueue;if(e.updateQueue=null,c!==null)try{l==="input"&&i.type==="radio"&&i.name!=null&&_p(s,i),Ql(l,a);var u=Ql(l,i);for(a=0;a<c.length;a+=2){var d=c[a],h=c[a+1];d==="style"?Mp(s,h):d==="dangerouslySetInnerHTML"?Ap(s,h):d==="children"?Ws(s,h):lu(s,d,h,u)}switch(l){case"input":Bl(s,i);break;case"textarea":Ep(s,i);break;case"select":var f=s._wrapperState.wasMultiple;s._wrapperState.wasMultiple=!!i.multiple;var g=i.value;g!=null?Fr(s,!!i.multiple,g,!1):f!==!!i.multiple&&(i.defaultValue!=null?Fr(s,!!i.multiple,i.defaultValue,!0):Fr(s,!!i.multiple,i.multiple?[]:"",!1))}s[ei]=i}catch(v){xe(e,e.return,v)}}break;case 6:if(St(t,e),Dt(e),r&4){if(e.stateNode===null)throw Error(F(162));s=e.stateNode,i=e.memoizedProps;try{s.nodeValue=i}catch(v){xe(e,e.return,v)}}break;case 3:if(St(t,e),Dt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{qs(t.containerInfo)}catch(v){xe(e,e.return,v)}break;case 4:St(t,e),Dt(e);break;case 13:St(t,e),Dt(e),s=e.child,s.flags&8192&&(i=s.memoizedState!==null,s.stateNode.isHidden=i,!i||s.alternate!==null&&s.alternate.memoizedState!==null||($u=Se())),r&4&&th(e);break;case 22:if(d=n!==null&&n.memoizedState!==null,e.mode&1?(Ie=(u=Ie)||d,St(t,e),Ie=u):St(t,e),Dt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!d&&e.mode&1)for(B=e,d=e.child;d!==null;){for(h=B=d;B!==null;){switch(f=B,g=f.child,f.tag){case 0:case 11:case 14:case 15:Ls(4,f,f.return);break;case 1:Er(f,f.return);var x=f.stateNode;if(typeof x.componentWillUnmount=="function"){r=f,n=f.return;try{t=r,x.props=t.memoizedProps,x.state=t.memoizedState,x.componentWillUnmount()}catch(v){xe(r,n,v)}}break;case 5:Er(f,f.return);break;case 22:if(f.memoizedState!==null){rh(h);continue}}g!==null?(g.return=f,B=g):rh(h)}d=d.sibling}e:for(d=null,h=e;;){if(h.tag===5){if(d===null){d=h;try{s=h.stateNode,u?(i=s.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(l=h.stateNode,c=h.memoizedProps.style,a=c!=null&&c.hasOwnProperty("display")?c.display:null,l.style.display=Dp("display",a))}catch(v){xe(e,e.return,v)}}}else if(h.tag===6){if(d===null)try{h.stateNode.nodeValue=u?"":h.memoizedProps}catch(v){xe(e,e.return,v)}}else if((h.tag!==22&&h.tag!==23||h.memoizedState===null||h===e)&&h.child!==null){h.child.return=h,h=h.child;continue}if(h===e)break e;for(;h.sibling===null;){if(h.return===null||h.return===e)break e;d===h&&(d=null),h=h.return}d===h&&(d=null),h.sibling.return=h.return,h=h.sibling}}break;case 19:St(t,e),Dt(e),r&4&&th(e);break;case 21:break;default:St(t,e),Dt(e)}}function Dt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(s0(n)){var r=n;break e}n=n.return}throw Error(F(160))}switch(r.tag){case 5:var s=r.stateNode;r.flags&32&&(Ws(s,""),r.flags&=-33);var i=eh(e);Nc(e,i,s);break;case 3:case 4:var a=r.stateNode.containerInfo,l=eh(e);jc(e,l,a);break;default:throw Error(F(161))}}catch(c){xe(e,e.return,c)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function a1(e,t,n){B=e,o0(e)}function o0(e,t,n){for(var r=(e.mode&1)!==0;B!==null;){var s=B,i=s.child;if(s.tag===22&&r){var a=s.memoizedState!==null||Bi;if(!a){var l=s.alternate,c=l!==null&&l.memoizedState!==null||Ie;l=Bi;var u=Ie;if(Bi=a,(Ie=c)&&!u)for(B=s;B!==null;)a=B,c=a.child,a.tag===22&&a.memoizedState!==null?sh(s):c!==null?(c.return=a,B=c):sh(s);for(;i!==null;)B=i,o0(i),i=i.sibling;B=s,Bi=l,Ie=u}nh(e)}else s.subtreeFlags&8772&&i!==null?(i.return=s,B=i):nh(e)}}function nh(e){for(;B!==null;){var t=B;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Ie||ho(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Ie)if(n===null)r.componentDidMount();else{var s=t.elementType===t.type?n.memoizedProps:jt(t.type,n.memoizedProps);r.componentDidUpdate(s,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&zf(t,i,r);break;case 3:var a=t.updateQueue;if(a!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}zf(t,a,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var d=u.memoizedState;if(d!==null){var h=d.dehydrated;h!==null&&qs(h)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(F(163))}Ie||t.flags&512&&bc(t)}catch(f){xe(t,t.return,f)}}if(t===e){B=null;break}if(n=t.sibling,n!==null){n.return=t.return,B=n;break}B=t.return}}function rh(e){for(;B!==null;){var t=B;if(t===e){B=null;break}var n=t.sibling;if(n!==null){n.return=t.return,B=n;break}B=t.return}}function sh(e){for(;B!==null;){var t=B;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ho(4,t)}catch(c){xe(t,n,c)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var s=t.return;try{r.componentDidMount()}catch(c){xe(t,s,c)}}var i=t.return;try{bc(t)}catch(c){xe(t,i,c)}break;case 5:var a=t.return;try{bc(t)}catch(c){xe(t,a,c)}}}catch(c){xe(t,t.return,c)}if(t===e){B=null;break}var l=t.sibling;if(l!==null){l.return=t.return,B=l;break}B=t.return}}var o1=Math.ceil,Fa=ln.ReactCurrentDispatcher,Ou=ln.ReactCurrentOwner,xt=ln.ReactCurrentBatchConfig,ee=0,Pe=null,Ce=null,Me=0,tt=0,Pr=Mn(0),_e=0,ai=null,ar=0,mo=0,zu=0,Fs=null,qe=null,$u=0,Qr=1/0,Kt=null,Va=!1,Cc=null,Cn=null,Hi=!1,vn=null,Ia=0,Vs=0,kc=null,ca=-1,ua=0;function We(){return ee&6?Se():ca!==-1?ca:ca=Se()}function kn(e){return e.mode&1?ee&2&&Me!==0?Me&-Me:Hw.transition!==null?(ua===0&&(ua=Wp()),ua):(e=ne,e!==0||(e=window.event,e=e===void 0?16:Zp(e.type)),e):1}function Pt(e,t,n,r){if(50<Vs)throw Vs=0,kc=null,Error(F(185));vi(e,n,r),(!(ee&2)||e!==Pe)&&(e===Pe&&(!(ee&2)&&(mo|=n),_e===4&&yn(e,Me)),Je(e,r),n===1&&ee===0&&!(t.mode&1)&&(Qr=Se()+500,co&&Rn()))}function Je(e,t){var n=e.callbackNode;Hv(e,t);var r=Sa(e,e===Pe?Me:0);if(r===0)n!==null&&hf(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&hf(n),t===1)e.tag===0?Bw(ih.bind(null,e)):yg(ih.bind(null,e)),Ow(function(){!(ee&6)&&Rn()}),n=null;else{switch(Kp(r)){case 1:n=hu;break;case 4:n=Bp;break;case 16:n=wa;break;case 536870912:n=Hp;break;default:n=wa}n=p0(n,l0.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function l0(e,t){if(ca=-1,ua=0,ee&6)throw Error(F(327));var n=e.callbackNode;if($r()&&e.callbackNode!==n)return null;var r=Sa(e,e===Pe?Me:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Oa(e,r);else{t=r;var s=ee;ee|=2;var i=u0();(Pe!==e||Me!==t)&&(Kt=null,Qr=Se()+500,Zn(e,t));do try{u1();break}catch(l){c0(e,l)}while(!0);ku(),Fa.current=i,ee=s,Ce!==null?t=0:(Pe=null,Me=0,t=_e)}if(t!==0){if(t===2&&(s=Jl(e),s!==0&&(r=s,t=Tc(e,s))),t===1)throw n=ai,Zn(e,0),yn(e,r),Je(e,Se()),n;if(t===6)yn(e,r);else{if(s=e.current.alternate,!(r&30)&&!l1(s)&&(t=Oa(e,r),t===2&&(i=Jl(e),i!==0&&(r=i,t=Tc(e,i))),t===1))throw n=ai,Zn(e,0),yn(e,r),Je(e,Se()),n;switch(e.finishedWork=s,e.finishedLanes=r,t){case 0:case 1:throw Error(F(345));case 2:Un(e,qe,Kt);break;case 3:if(yn(e,r),(r&130023424)===r&&(t=$u+500-Se(),10<t)){if(Sa(e,0)!==0)break;if(s=e.suspendedLanes,(s&r)!==r){We(),e.pingedLanes|=e.suspendedLanes&s;break}e.timeoutHandle=oc(Un.bind(null,e,qe,Kt),t);break}Un(e,qe,Kt);break;case 4:if(yn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,s=-1;0<r;){var a=31-Et(r);i=1<<a,a=t[a],a>s&&(s=a),r&=~i}if(r=s,r=Se()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*o1(r/1960))-r,10<r){e.timeoutHandle=oc(Un.bind(null,e,qe,Kt),r);break}Un(e,qe,Kt);break;case 5:Un(e,qe,Kt);break;default:throw Error(F(329))}}}return Je(e,Se()),e.callbackNode===n?l0.bind(null,e):null}function Tc(e,t){var n=Fs;return e.current.memoizedState.isDehydrated&&(Zn(e,t).flags|=256),e=Oa(e,t),e!==2&&(t=qe,qe=n,t!==null&&_c(t)),e}function _c(e){qe===null?qe=e:qe.push.apply(qe,e)}function l1(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var s=n[r],i=s.getSnapshot;s=s.value;try{if(!At(i(),s))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function yn(e,t){for(t&=~zu,t&=~mo,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Et(t),r=1<<n;e[n]=-1,t&=~r}}function ih(e){if(ee&6)throw Error(F(327));$r();var t=Sa(e,0);if(!(t&1))return Je(e,Se()),null;var n=Oa(e,t);if(e.tag!==0&&n===2){var r=Jl(e);r!==0&&(t=r,n=Tc(e,r))}if(n===1)throw n=ai,Zn(e,0),yn(e,t),Je(e,Se()),n;if(n===6)throw Error(F(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Un(e,qe,Kt),Je(e,Se()),null}function Uu(e,t){var n=ee;ee|=1;try{return e(t)}finally{ee=n,ee===0&&(Qr=Se()+500,co&&Rn())}}function or(e){vn!==null&&vn.tag===0&&!(ee&6)&&$r();var t=ee;ee|=1;var n=xt.transition,r=ne;try{if(xt.transition=null,ne=1,e)return e()}finally{ne=r,xt.transition=n,ee=t,!(ee&6)&&Rn()}}function Bu(){tt=Pr.current,le(Pr)}function Zn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Iw(n)),Ce!==null)for(n=Ce.return;n!==null;){var r=n;switch(ju(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&ka();break;case 3:Kr(),le(Ye),le(ze),Du();break;case 5:Au(r);break;case 4:Kr();break;case 13:le(he);break;case 19:le(he);break;case 10:Tu(r.type._context);break;case 22:case 23:Bu()}n=n.return}if(Pe=e,Ce=e=Tn(e.current,null),Me=tt=t,_e=0,ai=null,zu=mo=ar=0,qe=Fs=null,Qn!==null){for(t=0;t<Qn.length;t++)if(n=Qn[t],r=n.interleaved,r!==null){n.interleaved=null;var s=r.next,i=n.pending;if(i!==null){var a=i.next;i.next=s,r.next=a}n.pending=r}Qn=null}return e}function c0(e,t){do{var n=Ce;try{if(ku(),aa.current=La,Ra){for(var r=pe.memoizedState;r!==null;){var s=r.queue;s!==null&&(s.pending=null),r=r.next}Ra=!1}if(ir=0,Ee=Te=pe=null,Rs=!1,ri=0,Ou.current=null,n===null||n.return===null){_e=1,ai=t,Ce=null;break}e:{var i=e,a=n.return,l=n,c=t;if(t=Me,l.flags|=32768,c!==null&&typeof c=="object"&&typeof c.then=="function"){var u=c,d=l,h=d.tag;if(!(d.mode&1)&&(h===0||h===11||h===15)){var f=d.alternate;f?(d.updateQueue=f.updateQueue,d.memoizedState=f.memoizedState,d.lanes=f.lanes):(d.updateQueue=null,d.memoizedState=null)}var g=Kf(a);if(g!==null){g.flags&=-257,Gf(g,a,l,i,t),g.mode&1&&Wf(i,u,t),t=g,c=u;var x=t.updateQueue;if(x===null){var v=new Set;v.add(c),t.updateQueue=v}else x.add(c);break e}else{if(!(t&1)){Wf(i,u,t),Hu();break e}c=Error(F(426))}}else if(de&&l.mode&1){var S=Kf(a);if(S!==null){!(S.flags&65536)&&(S.flags|=256),Gf(S,a,l,i,t),Nu(Gr(c,l));break e}}i=c=Gr(c,l),_e!==4&&(_e=2),Fs===null?Fs=[i]:Fs.push(i),i=a;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var p=Kg(i,c,t);Of(i,p);break e;case 1:l=c;var m=i.type,y=i.stateNode;if(!(i.flags&128)&&(typeof m.getDerivedStateFromError=="function"||y!==null&&typeof y.componentDidCatch=="function"&&(Cn===null||!Cn.has(y)))){i.flags|=65536,t&=-t,i.lanes|=t;var b=Gg(i,l,t);Of(i,b);break e}}i=i.return}while(i!==null)}f0(n)}catch(j){t=j,Ce===n&&n!==null&&(Ce=n=n.return);continue}break}while(!0)}function u0(){var e=Fa.current;return Fa.current=La,e===null?La:e}function Hu(){(_e===0||_e===3||_e===2)&&(_e=4),Pe===null||!(ar&268435455)&&!(mo&268435455)||yn(Pe,Me)}function Oa(e,t){var n=ee;ee|=2;var r=u0();(Pe!==e||Me!==t)&&(Kt=null,Zn(e,t));do try{c1();break}catch(s){c0(e,s)}while(!0);if(ku(),ee=n,Fa.current=r,Ce!==null)throw Error(F(261));return Pe=null,Me=0,_e}function c1(){for(;Ce!==null;)d0(Ce)}function u1(){for(;Ce!==null&&!Lv();)d0(Ce)}function d0(e){var t=m0(e.alternate,e,tt);e.memoizedProps=e.pendingProps,t===null?f0(e):Ce=t,Ou.current=null}function f0(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=r1(n,t),n!==null){n.flags&=32767,Ce=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{_e=6,Ce=null;return}}else if(n=n1(n,t,tt),n!==null){Ce=n;return}if(t=t.sibling,t!==null){Ce=t;return}Ce=t=e}while(t!==null);_e===0&&(_e=5)}function Un(e,t,n){var r=ne,s=xt.transition;try{xt.transition=null,ne=1,d1(e,t,n,r)}finally{xt.transition=s,ne=r}return null}function d1(e,t,n,r){do $r();while(vn!==null);if(ee&6)throw Error(F(327));n=e.finishedWork;var s=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(F(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(Wv(e,i),e===Pe&&(Ce=Pe=null,Me=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Hi||(Hi=!0,p0(wa,function(){return $r(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=xt.transition,xt.transition=null;var a=ne;ne=1;var l=ee;ee|=4,Ou.current=null,i1(e,n),a0(n,e),Aw(ic),ba=!!sc,ic=sc=null,e.current=n,a1(n),Fv(),ee=l,ne=a,xt.transition=i}else e.current=n;if(Hi&&(Hi=!1,vn=e,Ia=s),i=e.pendingLanes,i===0&&(Cn=null),Ov(n.stateNode),Je(e,Se()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)s=t[n],r(s.value,{componentStack:s.stack,digest:s.digest});if(Va)throw Va=!1,e=Cc,Cc=null,e;return Ia&1&&e.tag!==0&&$r(),i=e.pendingLanes,i&1?e===kc?Vs++:(Vs=0,kc=e):Vs=0,Rn(),null}function $r(){if(vn!==null){var e=Kp(Ia),t=xt.transition,n=ne;try{if(xt.transition=null,ne=16>e?16:e,vn===null)var r=!1;else{if(e=vn,vn=null,Ia=0,ee&6)throw Error(F(331));var s=ee;for(ee|=4,B=e.current;B!==null;){var i=B,a=i.child;if(B.flags&16){var l=i.deletions;if(l!==null){for(var c=0;c<l.length;c++){var u=l[c];for(B=u;B!==null;){var d=B;switch(d.tag){case 0:case 11:case 15:Ls(8,d,i)}var h=d.child;if(h!==null)h.return=d,B=h;else for(;B!==null;){d=B;var f=d.sibling,g=d.return;if(r0(d),d===u){B=null;break}if(f!==null){f.return=g,B=f;break}B=g}}}var x=i.alternate;if(x!==null){var v=x.child;if(v!==null){x.child=null;do{var S=v.sibling;v.sibling=null,v=S}while(v!==null)}}B=i}}if(i.subtreeFlags&2064&&a!==null)a.return=i,B=a;else e:for(;B!==null;){if(i=B,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Ls(9,i,i.return)}var p=i.sibling;if(p!==null){p.return=i.return,B=p;break e}B=i.return}}var m=e.current;for(B=m;B!==null;){a=B;var y=a.child;if(a.subtreeFlags&2064&&y!==null)y.return=a,B=y;else e:for(a=m;B!==null;){if(l=B,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:ho(9,l)}}catch(j){xe(l,l.return,j)}if(l===a){B=null;break e}var b=l.sibling;if(b!==null){b.return=l.return,B=b;break e}B=l.return}}if(ee=s,Rn(),Vt&&typeof Vt.onPostCommitFiberRoot=="function")try{Vt.onPostCommitFiberRoot(so,e)}catch{}r=!0}return r}finally{ne=n,xt.transition=t}}return!1}function ah(e,t,n){t=Gr(n,t),t=Kg(e,t,1),e=Nn(e,t,1),t=We(),e!==null&&(vi(e,1,t),Je(e,t))}function xe(e,t,n){if(e.tag===3)ah(e,e,n);else for(;t!==null;){if(t.tag===3){ah(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Cn===null||!Cn.has(r))){e=Gr(n,e),e=Gg(t,e,1),t=Nn(t,e,1),e=We(),t!==null&&(vi(t,1,e),Je(t,e));break}}t=t.return}}function f1(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=We(),e.pingedLanes|=e.suspendedLanes&n,Pe===e&&(Me&n)===n&&(_e===4||_e===3&&(Me&130023424)===Me&&500>Se()-$u?Zn(e,0):zu|=n),Je(e,t)}function h0(e,t){t===0&&(e.mode&1?(t=Ri,Ri<<=1,!(Ri&130023424)&&(Ri=4194304)):t=1);var n=We();e=sn(e,t),e!==null&&(vi(e,t,n),Je(e,n))}function h1(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),h0(e,n)}function m1(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,s=e.memoizedState;s!==null&&(n=s.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(F(314))}r!==null&&r.delete(t),h0(e,n)}var m0;m0=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ye.current)Xe=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Xe=!1,t1(e,t,n);Xe=!!(e.flags&131072)}else Xe=!1,de&&t.flags&1048576&&xg(t,Ea,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;la(e,t),e=t.pendingProps;var s=Br(t,ze.current);zr(t,n),s=Ru(null,t,r,e,s,n);var i=Lu();return t.flags|=1,typeof s=="object"&&s!==null&&typeof s.render=="function"&&s.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ze(r)?(i=!0,Ta(t)):i=!1,t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,Eu(t),s.updater=fo,t.stateNode=s,s._reactInternals=t,mc(t,r,e,n),t=yc(null,t,r,!0,i,n)):(t.tag=0,de&&i&&bu(t),$e(null,t,s,n),t=t.child),t;case 16:r=t.elementType;e:{switch(la(e,t),e=t.pendingProps,s=r._init,r=s(r._payload),t.type=r,s=t.tag=g1(r),e=jt(r,e),s){case 0:t=gc(null,t,r,e,n);break e;case 1:t=Xf(null,t,r,e,n);break e;case 11:t=Qf(null,t,r,e,n);break e;case 14:t=qf(null,t,r,jt(r.type,e),n);break e}throw Error(F(306,r,""))}return t;case 0:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:jt(r,s),gc(e,t,r,s,n);case 1:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:jt(r,s),Xf(e,t,r,s,n);case 3:e:{if(Yg(t),e===null)throw Error(F(387));r=t.pendingProps,i=t.memoizedState,s=i.element,Ng(e,t),Da(t,r,null,n);var a=t.memoizedState;if(r=a.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:a.cache,pendingSuspenseBoundaries:a.pendingSuspenseBoundaries,transitions:a.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){s=Gr(Error(F(423)),t),t=Yf(e,t,r,n,s);break e}else if(r!==s){s=Gr(Error(F(424)),t),t=Yf(e,t,r,n,s);break e}else for(rt=jn(t.stateNode.containerInfo.firstChild),st=t,de=!0,Ct=null,n=bg(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Hr(),r===s){t=an(e,t,n);break e}$e(e,t,r,n)}t=t.child}return t;case 5:return Cg(t),e===null&&dc(t),r=t.type,s=t.pendingProps,i=e!==null?e.memoizedProps:null,a=s.children,ac(r,s)?a=null:i!==null&&ac(r,i)&&(t.flags|=32),Xg(e,t),$e(e,t,a,n),t.child;case 6:return e===null&&dc(t),null;case 13:return Zg(e,t,n);case 4:return Pu(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Wr(t,null,r,n):$e(e,t,r,n),t.child;case 11:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:jt(r,s),Qf(e,t,r,s,n);case 7:return $e(e,t,t.pendingProps,n),t.child;case 8:return $e(e,t,t.pendingProps.children,n),t.child;case 12:return $e(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,s=t.pendingProps,i=t.memoizedProps,a=s.value,ae(Pa,r._currentValue),r._currentValue=a,i!==null)if(At(i.value,a)){if(i.children===s.children&&!Ye.current){t=an(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var l=i.dependencies;if(l!==null){a=i.child;for(var c=l.firstContext;c!==null;){if(c.context===r){if(i.tag===1){c=Zt(-1,n&-n),c.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var d=u.pending;d===null?c.next=c:(c.next=d.next,d.next=c),u.pending=c}}i.lanes|=n,c=i.alternate,c!==null&&(c.lanes|=n),fc(i.return,n,t),l.lanes|=n;break}c=c.next}}else if(i.tag===10)a=i.type===t.type?null:i.child;else if(i.tag===18){if(a=i.return,a===null)throw Error(F(341));a.lanes|=n,l=a.alternate,l!==null&&(l.lanes|=n),fc(a,n,t),a=i.sibling}else a=i.child;if(a!==null)a.return=i;else for(a=i;a!==null;){if(a===t){a=null;break}if(i=a.sibling,i!==null){i.return=a.return,a=i;break}a=a.return}i=a}$e(e,t,s.children,n),t=t.child}return t;case 9:return s=t.type,r=t.pendingProps.children,zr(t,n),s=vt(s),r=r(s),t.flags|=1,$e(e,t,r,n),t.child;case 14:return r=t.type,s=jt(r,t.pendingProps),s=jt(r.type,s),qf(e,t,r,s,n);case 15:return Qg(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:jt(r,s),la(e,t),t.tag=1,Ze(r)?(e=!0,Ta(t)):e=!1,zr(t,n),Wg(t,r,s),mc(t,r,s,n),yc(null,t,r,!0,e,n);case 19:return Jg(e,t,n);case 22:return qg(e,t,n)}throw Error(F(156,t.tag))};function p0(e,t){return Up(e,t)}function p1(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function yt(e,t,n,r){return new p1(e,t,n,r)}function Wu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function g1(e){if(typeof e=="function")return Wu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===uu)return 11;if(e===du)return 14}return 2}function Tn(e,t){var n=e.alternate;return n===null?(n=yt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function da(e,t,n,r,s,i){var a=2;if(r=e,typeof e=="function")Wu(e)&&(a=1);else if(typeof e=="string")a=5;else e:switch(e){case wr:return Jn(n.children,s,i,t);case cu:a=8,s|=8;break;case Il:return e=yt(12,n,t,s|2),e.elementType=Il,e.lanes=i,e;case Ol:return e=yt(13,n,t,s),e.elementType=Ol,e.lanes=i,e;case zl:return e=yt(19,n,t,s),e.elementType=zl,e.lanes=i,e;case Cp:return po(n,s,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case jp:a=10;break e;case Np:a=9;break e;case uu:a=11;break e;case du:a=14;break e;case mn:a=16,r=null;break e}throw Error(F(130,e==null?e:typeof e,""))}return t=yt(a,n,t,s),t.elementType=e,t.type=r,t.lanes=i,t}function Jn(e,t,n,r){return e=yt(7,e,r,t),e.lanes=n,e}function po(e,t,n,r){return e=yt(22,e,r,t),e.elementType=Cp,e.lanes=n,e.stateNode={isHidden:!1},e}function fl(e,t,n){return e=yt(6,e,null,t),e.lanes=n,e}function hl(e,t,n){return t=yt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function y1(e,t,n,r,s){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Go(0),this.expirationTimes=Go(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Go(0),this.identifierPrefix=r,this.onRecoverableError=s,this.mutableSourceEagerHydrationData=null}function Ku(e,t,n,r,s,i,a,l,c){return e=new y1(e,t,n,l,c),t===1?(t=1,i===!0&&(t|=8)):t=0,i=yt(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Eu(i),e}function x1(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:vr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function g0(e){if(!e)return En;e=e._reactInternals;e:{if(hr(e)!==e||e.tag!==1)throw Error(F(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ze(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(F(171))}if(e.tag===1){var n=e.type;if(Ze(n))return gg(e,n,t)}return t}function y0(e,t,n,r,s,i,a,l,c){return e=Ku(n,r,!0,e,s,i,a,l,c),e.context=g0(null),n=e.current,r=We(),s=kn(n),i=Zt(r,s),i.callback=t??null,Nn(n,i,s),e.current.lanes=s,vi(e,s,r),Je(e,r),e}function go(e,t,n,r){var s=t.current,i=We(),a=kn(s);return n=g0(n),t.context===null?t.context=n:t.pendingContext=n,t=Zt(i,a),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Nn(s,t,a),e!==null&&(Pt(e,s,a,i),ia(e,s,a)),a}function za(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function oh(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Gu(e,t){oh(e,t),(e=e.alternate)&&oh(e,t)}function v1(){return null}var x0=typeof reportError=="function"?reportError:function(e){console.error(e)};function Qu(e){this._internalRoot=e}yo.prototype.render=Qu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(F(409));go(e,t,null,null)};yo.prototype.unmount=Qu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;or(function(){go(null,e,null,null)}),t[rn]=null}};function yo(e){this._internalRoot=e}yo.prototype.unstable_scheduleHydration=function(e){if(e){var t=qp();e={blockedOn:null,target:e,priority:t};for(var n=0;n<gn.length&&t!==0&&t<gn[n].priority;n++);gn.splice(n,0,e),n===0&&Yp(e)}};function qu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function xo(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function lh(){}function w1(e,t,n,r,s){if(s){if(typeof r=="function"){var i=r;r=function(){var u=za(a);i.call(u)}}var a=y0(t,r,e,0,null,!1,!1,"",lh);return e._reactRootContainer=a,e[rn]=a.current,Zs(e.nodeType===8?e.parentNode:e),or(),a}for(;s=e.lastChild;)e.removeChild(s);if(typeof r=="function"){var l=r;r=function(){var u=za(c);l.call(u)}}var c=Ku(e,0,!1,null,null,!1,!1,"",lh);return e._reactRootContainer=c,e[rn]=c.current,Zs(e.nodeType===8?e.parentNode:e),or(function(){go(t,c,n,r)}),c}function vo(e,t,n,r,s){var i=n._reactRootContainer;if(i){var a=i;if(typeof s=="function"){var l=s;s=function(){var c=za(a);l.call(c)}}go(t,a,e,s)}else a=w1(n,t,e,s,r);return za(a)}Gp=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=js(t.pendingLanes);n!==0&&(mu(t,n|1),Je(t,Se()),!(ee&6)&&(Qr=Se()+500,Rn()))}break;case 13:or(function(){var r=sn(e,1);if(r!==null){var s=We();Pt(r,e,1,s)}}),Gu(e,1)}};pu=function(e){if(e.tag===13){var t=sn(e,134217728);if(t!==null){var n=We();Pt(t,e,134217728,n)}Gu(e,134217728)}};Qp=function(e){if(e.tag===13){var t=kn(e),n=sn(e,t);if(n!==null){var r=We();Pt(n,e,t,r)}Gu(e,t)}};qp=function(){return ne};Xp=function(e,t){var n=ne;try{return ne=e,t()}finally{ne=n}};Xl=function(e,t,n){switch(t){case"input":if(Bl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var s=lo(r);if(!s)throw Error(F(90));Tp(r),Bl(r,s)}}}break;case"textarea":Ep(e,n);break;case"select":t=n.value,t!=null&&Fr(e,!!n.multiple,t,!1)}};Fp=Uu;Vp=or;var S1={usingClientEntryPoint:!1,Events:[Si,Nr,lo,Rp,Lp,Uu]},gs={findFiberByHostInstance:Gn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},b1={bundleType:gs.bundleType,version:gs.version,rendererPackageName:gs.rendererPackageName,rendererConfig:gs.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:ln.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=zp(e),e===null?null:e.stateNode},findFiberByHostInstance:gs.findFiberByHostInstance||v1,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Wi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Wi.isDisabled&&Wi.supportsFiber)try{so=Wi.inject(b1),Vt=Wi}catch{}}ut.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=S1;ut.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!qu(t))throw Error(F(200));return x1(e,t,null,n)};ut.createRoot=function(e,t){if(!qu(e))throw Error(F(299));var n=!1,r="",s=x0;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(s=t.onRecoverableError)),t=Ku(e,1,!1,null,null,n,!1,r,s),e[rn]=t.current,Zs(e.nodeType===8?e.parentNode:e),new Qu(t)};ut.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(F(188)):(e=Object.keys(e).join(","),Error(F(268,e)));return e=zp(t),e=e===null?null:e.stateNode,e};ut.flushSync=function(e){return or(e)};ut.hydrate=function(e,t,n){if(!xo(t))throw Error(F(200));return vo(null,e,t,!0,n)};ut.hydrateRoot=function(e,t,n){if(!qu(e))throw Error(F(405));var r=n!=null&&n.hydratedSources||null,s=!1,i="",a=x0;if(n!=null&&(n.unstable_strictMode===!0&&(s=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(a=n.onRecoverableError)),t=y0(t,null,e,1,n??null,s,!1,i,a),e[rn]=t.current,Zs(e),r)for(e=0;e<r.length;e++)n=r[e],s=n._getVersion,s=s(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,s]:t.mutableSourceEagerHydrationData.push(n,s);return new yo(t)};ut.render=function(e,t,n){if(!xo(t))throw Error(F(200));return vo(null,e,t,!1,n)};ut.unmountComponentAtNode=function(e){if(!xo(e))throw Error(F(40));return e._reactRootContainer?(or(function(){vo(null,null,e,!1,function(){e._reactRootContainer=null,e[rn]=null})}),!0):!1};ut.unstable_batchedUpdates=Uu;ut.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!xo(n))throw Error(F(200));if(e==null||e._reactInternals===void 0)throw Error(F(38));return vo(e,t,n,!1,r)};ut.version="18.3.1-next-f1338f8080-20240426";function v0(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(v0)}catch(e){console.error(e)}}v0(),vp.exports=ut;var j1=vp.exports,ch=j1;Fl.createRoot=ch.createRoot,Fl.hydrateRoot=ch.hydrateRoot;const Xu=w.createContext({});function Yu(e){const t=w.useRef(null);return t.current===null&&(t.current=e()),t.current}const wo=w.createContext(null),Zu=w.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"});class N1 extends w.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function C1({children:e,isPresent:t}){const n=w.useId(),r=w.useRef(null),s=w.useRef({width:0,height:0,top:0,left:0}),{nonce:i}=w.useContext(Zu);return w.useInsertionEffect(()=>{const{width:a,height:l,top:c,left:u}=s.current;if(t||!r.current||!a||!l)return;r.current.dataset.motionPopId=n;const d=document.createElement("style");return i&&(d.nonce=i),document.head.appendChild(d),d.sheet&&d.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${a}px !important;
            height: ${l}px !important;
            top: ${c}px !important;
            left: ${u}px !important;
          }
        `),()=>{document.head.removeChild(d)}},[t]),o.jsx(N1,{isPresent:t,childRef:r,sizeRef:s,children:w.cloneElement(e,{ref:r})})}const k1=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:s,presenceAffectsLayout:i,mode:a})=>{const l=Yu(T1),c=w.useId(),u=w.useCallback(h=>{l.set(h,!0);for(const f of l.values())if(!f)return;r&&r()},[l,r]),d=w.useMemo(()=>({id:c,initial:t,isPresent:n,custom:s,onExitComplete:u,register:h=>(l.set(h,!1),()=>l.delete(h))}),i?[Math.random(),u]:[n,u]);return w.useMemo(()=>{l.forEach((h,f)=>l.set(f,!1))},[n]),w.useEffect(()=>{!n&&!l.size&&r&&r()},[n]),a==="popLayout"&&(e=o.jsx(C1,{isPresent:n,children:e})),o.jsx(wo.Provider,{value:d,children:e})};function T1(){return new Map}function w0(e=!0){const t=w.useContext(wo);if(t===null)return[!0,null];const{isPresent:n,onExitComplete:r,register:s}=t,i=w.useId();w.useEffect(()=>{e&&s(i)},[e]);const a=w.useCallback(()=>e&&r&&r(i),[i,r,e]);return!n&&r?[!1,a]:[!0]}const Ki=e=>e.key||"";function uh(e){const t=[];return w.Children.forEach(e,n=>{w.isValidElement(n)&&t.push(n)}),t}const Ju=typeof window<"u",S0=Ju?w.useLayoutEffect:w.useEffect,re=({children:e,custom:t,initial:n=!0,onExitComplete:r,presenceAffectsLayout:s=!0,mode:i="sync",propagate:a=!1})=>{const[l,c]=w0(a),u=w.useMemo(()=>uh(e),[e]),d=a&&!l?[]:u.map(Ki),h=w.useRef(!0),f=w.useRef(u),g=Yu(()=>new Map),[x,v]=w.useState(u),[S,p]=w.useState(u);S0(()=>{h.current=!1,f.current=u;for(let b=0;b<S.length;b++){const j=Ki(S[b]);d.includes(j)?g.delete(j):g.get(j)!==!0&&g.set(j,!1)}},[S,d.length,d.join("-")]);const m=[];if(u!==x){let b=[...u];for(let j=0;j<S.length;j++){const k=S[j],_=Ki(k);d.includes(_)||(b.splice(j,0,k),m.push(k))}i==="wait"&&m.length&&(b=m),p(uh(b)),v(u);return}const{forceRender:y}=w.useContext(Xu);return o.jsx(o.Fragment,{children:S.map(b=>{const j=Ki(b),k=a&&!l?!1:u===S||d.includes(j),_=()=>{if(g.has(j))g.set(j,!0);else return;let C=!0;g.forEach(P=>{P||(C=!1)}),C&&(y==null||y(),p(f.current),a&&(c==null||c()),r&&r())};return o.jsx(k1,{isPresent:k,initial:!h.current||n?void 0:!1,custom:k?void 0:t,presenceAffectsLayout:s,mode:i,onExitComplete:k?void 0:_,children:b},j)})})},it=e=>e;let b0=it;function ed(e){let t;return()=>(t===void 0&&(t=e()),t)}const qr=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},Jt=e=>e*1e3,en=e=>e/1e3,_1={useManualTiming:!1};function E1(e){let t=new Set,n=new Set,r=!1,s=!1;const i=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1};function l(u){i.has(u)&&(c.schedule(u),e()),u(a)}const c={schedule:(u,d=!1,h=!1)=>{const g=h&&r?t:n;return d&&i.add(u),g.has(u)||g.add(u),u},cancel:u=>{n.delete(u),i.delete(u)},process:u=>{if(a=u,r){s=!0;return}r=!0,[t,n]=[n,t],t.forEach(l),t.clear(),r=!1,s&&(s=!1,c.process(u))}};return c}const Gi=["read","resolveKeyframes","update","preRender","render","postRender"],P1=40;function j0(e,t){let n=!1,r=!0;const s={delta:0,timestamp:0,isProcessing:!1},i=()=>n=!0,a=Gi.reduce((p,m)=>(p[m]=E1(i),p),{}),{read:l,resolveKeyframes:c,update:u,preRender:d,render:h,postRender:f}=a,g=()=>{const p=performance.now();n=!1,s.delta=r?1e3/60:Math.max(Math.min(p-s.timestamp,P1),1),s.timestamp=p,s.isProcessing=!0,l.process(s),c.process(s),u.process(s),d.process(s),h.process(s),f.process(s),s.isProcessing=!1,n&&t&&(r=!1,e(g))},x=()=>{n=!0,r=!0,s.isProcessing||e(g)};return{schedule:Gi.reduce((p,m)=>{const y=a[m];return p[m]=(b,j=!1,k=!1)=>(n||x(),y.schedule(b,j,k)),p},{}),cancel:p=>{for(let m=0;m<Gi.length;m++)a[Gi[m]].cancel(p)},state:s,steps:a}}const{schedule:ce,cancel:Pn,state:De,steps:ml}=j0(typeof requestAnimationFrame<"u"?requestAnimationFrame:it,!0),N0=w.createContext({strict:!1}),dh={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Xr={};for(const e in dh)Xr[e]={isEnabled:t=>dh[e].some(n=>!!t[n])};function A1(e){for(const t in e)Xr[t]={...Xr[t],...e[t]}}const D1=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function $a(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||D1.has(e)}let C0=e=>!$a(e);function M1(e){e&&(C0=t=>t.startsWith("on")?!$a(t):e(t))}try{M1(require("@emotion/is-prop-valid").default)}catch{}function R1(e,t,n){const r={};for(const s in e)s==="values"&&typeof e.values=="object"||(C0(s)||n===!0&&$a(s)||!t&&!$a(s)||e.draggable&&s.startsWith("onDrag"))&&(r[s]=e[s]);return r}function L1(e){if(typeof Proxy>"u")return e;const t=new Map,n=(...r)=>e(...r);return new Proxy(n,{get:(r,s)=>s==="create"?e:(t.has(s)||t.set(s,e(s)),t.get(s))})}const So=w.createContext({});function oi(e){return typeof e=="string"||Array.isArray(e)}function bo(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const td=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],nd=["initial",...td];function jo(e){return bo(e.animate)||nd.some(t=>oi(e[t]))}function k0(e){return!!(jo(e)||e.variants)}function F1(e,t){if(jo(e)){const{initial:n,animate:r}=e;return{initial:n===!1||oi(n)?n:void 0,animate:oi(r)?r:void 0}}return e.inherit!==!1?t:{}}function V1(e){const{initial:t,animate:n}=F1(e,w.useContext(So));return w.useMemo(()=>({initial:t,animate:n}),[fh(t),fh(n)])}function fh(e){return Array.isArray(e)?e.join(" "):e}const I1=Symbol.for("motionComponentSymbol");function Ar(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function O1(e,t,n){return w.useCallback(r=>{r&&e.onMount&&e.onMount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):Ar(n)&&(n.current=r))},[t])}const rd=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),z1="framerAppearId",T0="data-"+rd(z1),{schedule:sd}=j0(queueMicrotask,!1),_0=w.createContext({});function $1(e,t,n,r,s){var i,a;const{visualElement:l}=w.useContext(So),c=w.useContext(N0),u=w.useContext(wo),d=w.useContext(Zu).reducedMotion,h=w.useRef(null);r=r||c.renderer,!h.current&&r&&(h.current=r(e,{visualState:t,parent:l,props:n,presenceContext:u,blockInitialAnimation:u?u.initial===!1:!1,reducedMotionConfig:d}));const f=h.current,g=w.useContext(_0);f&&!f.projection&&s&&(f.type==="html"||f.type==="svg")&&U1(h.current,n,s,g);const x=w.useRef(!1);w.useInsertionEffect(()=>{f&&x.current&&f.update(n,u)});const v=n[T0],S=w.useRef(!!v&&!(!((i=window.MotionHandoffIsComplete)===null||i===void 0)&&i.call(window,v))&&((a=window.MotionHasOptimisedAnimation)===null||a===void 0?void 0:a.call(window,v)));return S0(()=>{f&&(x.current=!0,window.MotionIsMounted=!0,f.updateFeatures(),sd.render(f.render),S.current&&f.animationState&&f.animationState.animateChanges())}),w.useEffect(()=>{f&&(!S.current&&f.animationState&&f.animationState.animateChanges(),S.current&&(queueMicrotask(()=>{var p;(p=window.MotionHandoffMarkAsComplete)===null||p===void 0||p.call(window,v)}),S.current=!1))}),f}function U1(e,t,n,r){const{layoutId:s,layout:i,drag:a,dragConstraints:l,layoutScroll:c,layoutRoot:u}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:E0(e.parent)),e.projection.setOptions({layoutId:s,layout:i,alwaysMeasureLayout:!!a||l&&Ar(l),visualElement:e,animationType:typeof i=="string"?i:"both",initialPromotionConfig:r,layoutScroll:c,layoutRoot:u})}function E0(e){if(e)return e.options.allowProjection!==!1?e.projection:E0(e.parent)}function B1({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:s}){var i,a;e&&A1(e);function l(u,d){let h;const f={...w.useContext(Zu),...u,layoutId:H1(u)},{isStatic:g}=f,x=V1(u),v=r(u,g);if(!g&&Ju){W1();const S=K1(f);h=S.MeasureLayout,x.visualElement=$1(s,v,f,t,S.ProjectionNode)}return o.jsxs(So.Provider,{value:x,children:[h&&x.visualElement?o.jsx(h,{visualElement:x.visualElement,...f}):null,n(s,u,O1(v,x.visualElement,d),v,g,x.visualElement)]})}l.displayName=`motion.${typeof s=="string"?s:`create(${(a=(i=s.displayName)!==null&&i!==void 0?i:s.name)!==null&&a!==void 0?a:""})`}`;const c=w.forwardRef(l);return c[I1]=s,c}function H1({layoutId:e}){const t=w.useContext(Xu).id;return t&&e!==void 0?t+"-"+e:e}function W1(e,t){w.useContext(N0).strict}function K1(e){const{drag:t,layout:n}=Xr;if(!t&&!n)return{};const r={...t,...n};return{MeasureLayout:t!=null&&t.isEnabled(e)||n!=null&&n.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}const G1=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function id(e){return typeof e!="string"||e.includes("-")?!1:!!(G1.indexOf(e)>-1||/[A-Z]/u.test(e))}function hh(e){const t=[{},{}];return e==null||e.values.forEach((n,r)=>{t[0][r]=n.get(),t[1][r]=n.getVelocity()}),t}function ad(e,t,n,r){if(typeof t=="function"){const[s,i]=hh(r);t=t(n!==void 0?n:e.custom,s,i)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[s,i]=hh(r);t=t(n!==void 0?n:e.custom,s,i)}return t}const Ec=e=>Array.isArray(e),Q1=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),q1=e=>Ec(e)?e[e.length-1]||0:e,Oe=e=>!!(e&&e.getVelocity);function fa(e){const t=Oe(e)?e.get():e;return Q1(t)?t.toValue():t}function X1({scrapeMotionValuesFromProps:e,createRenderState:t,onUpdate:n},r,s,i){const a={latestValues:Y1(r,s,i,e),renderState:t()};return n&&(a.onMount=l=>n({props:r,current:l,...a}),a.onUpdate=l=>n(l)),a}const P0=e=>(t,n)=>{const r=w.useContext(So),s=w.useContext(wo),i=()=>X1(e,t,r,s);return n?i():Yu(i)};function Y1(e,t,n,r){const s={},i=r(e,{});for(const f in i)s[f]=fa(i[f]);let{initial:a,animate:l}=e;const c=jo(e),u=k0(e);t&&u&&!c&&e.inherit!==!1&&(a===void 0&&(a=t.initial),l===void 0&&(l=t.animate));let d=n?n.initial===!1:!1;d=d||a===!1;const h=d?l:a;if(h&&typeof h!="boolean"&&!bo(h)){const f=Array.isArray(h)?h:[h];for(let g=0;g<f.length;g++){const x=ad(e,f[g]);if(x){const{transitionEnd:v,transition:S,...p}=x;for(const m in p){let y=p[m];if(Array.isArray(y)){const b=d?y.length-1:0;y=y[b]}y!==null&&(s[m]=y)}for(const m in v)s[m]=v[m]}}}return s}const as=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],mr=new Set(as),A0=e=>t=>typeof t=="string"&&t.startsWith(e),D0=A0("--"),Z1=A0("var(--"),od=e=>Z1(e)?J1.test(e.split("/*")[0].trim()):!1,J1=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,M0=(e,t)=>t&&typeof e=="number"?t.transform(e):e,on=(e,t,n)=>n>t?t:n<e?e:n,os={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},li={...os,transform:e=>on(0,1,e)},Qi={...os,default:1},ji=e=>({test:t=>typeof t=="string"&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),hn=ji("deg"),Ot=ji("%"),q=ji("px"),e2=ji("vh"),t2=ji("vw"),mh={...Ot,parse:e=>Ot.parse(e)/100,transform:e=>Ot.transform(e*100)},n2={borderWidth:q,borderTopWidth:q,borderRightWidth:q,borderBottomWidth:q,borderLeftWidth:q,borderRadius:q,radius:q,borderTopLeftRadius:q,borderTopRightRadius:q,borderBottomRightRadius:q,borderBottomLeftRadius:q,width:q,maxWidth:q,height:q,maxHeight:q,top:q,right:q,bottom:q,left:q,padding:q,paddingTop:q,paddingRight:q,paddingBottom:q,paddingLeft:q,margin:q,marginTop:q,marginRight:q,marginBottom:q,marginLeft:q,backgroundPositionX:q,backgroundPositionY:q},r2={rotate:hn,rotateX:hn,rotateY:hn,rotateZ:hn,scale:Qi,scaleX:Qi,scaleY:Qi,scaleZ:Qi,skew:hn,skewX:hn,skewY:hn,distance:q,translateX:q,translateY:q,translateZ:q,x:q,y:q,z:q,perspective:q,transformPerspective:q,opacity:li,originX:mh,originY:mh,originZ:q},ph={...os,transform:Math.round},ld={...n2,...r2,zIndex:ph,size:q,fillOpacity:li,strokeOpacity:li,numOctaves:ph},s2={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},i2=as.length;function a2(e,t,n){let r="",s=!0;for(let i=0;i<i2;i++){const a=as[i],l=e[a];if(l===void 0)continue;let c=!0;if(typeof l=="number"?c=l===(a.startsWith("scale")?1:0):c=parseFloat(l)===0,!c||n){const u=M0(l,ld[a]);if(!c){s=!1;const d=s2[a]||a;r+=`${d}(${u}) `}n&&(t[a]=u)}}return r=r.trim(),n?r=n(t,s?"":r):s&&(r="none"),r}function cd(e,t,n){const{style:r,vars:s,transformOrigin:i}=e;let a=!1,l=!1;for(const c in t){const u=t[c];if(mr.has(c)){a=!0;continue}else if(D0(c)){s[c]=u;continue}else{const d=M0(u,ld[c]);c.startsWith("origin")?(l=!0,i[c]=d):r[c]=d}}if(t.transform||(a||n?r.transform=a2(t,e.transform,n):r.transform&&(r.transform="none")),l){const{originX:c="50%",originY:u="50%",originZ:d=0}=i;r.transformOrigin=`${c} ${u} ${d}`}}const o2={offset:"stroke-dashoffset",array:"stroke-dasharray"},l2={offset:"strokeDashoffset",array:"strokeDasharray"};function c2(e,t,n=1,r=0,s=!0){e.pathLength=1;const i=s?o2:l2;e[i.offset]=q.transform(-r);const a=q.transform(t),l=q.transform(n);e[i.array]=`${a} ${l}`}function gh(e,t,n){return typeof e=="string"?e:q.transform(t+n*e)}function u2(e,t,n){const r=gh(t,e.x,e.width),s=gh(n,e.y,e.height);return`${r} ${s}`}function ud(e,{attrX:t,attrY:n,attrScale:r,originX:s,originY:i,pathLength:a,pathSpacing:l=1,pathOffset:c=0,...u},d,h){if(cd(e,u,h),d){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:f,style:g,dimensions:x}=e;f.transform&&(x&&(g.transform=f.transform),delete f.transform),x&&(s!==void 0||i!==void 0||g.transform)&&(g.transformOrigin=u2(x,s!==void 0?s:.5,i!==void 0?i:.5)),t!==void 0&&(f.x=t),n!==void 0&&(f.y=n),r!==void 0&&(f.scale=r),a!==void 0&&c2(f,a,l,c,!1)}const dd=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),R0=()=>({...dd(),attrs:{}}),fd=e=>typeof e=="string"&&e.toLowerCase()==="svg";function L0(e,{style:t,vars:n},r,s){Object.assign(e.style,t,s&&s.getProjectionStyles(r));for(const i in n)e.style.setProperty(i,n[i])}const F0=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function V0(e,t,n,r){L0(e,t,void 0,r);for(const s in t.attrs)e.setAttribute(F0.has(s)?s:rd(s),t.attrs[s])}const Ua={};function d2(e){Object.assign(Ua,e)}function I0(e,{layout:t,layoutId:n}){return mr.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!Ua[e]||e==="opacity")}function hd(e,t,n){var r;const{style:s}=e,i={};for(const a in s)(Oe(s[a])||t.style&&Oe(t.style[a])||I0(a,e)||((r=n==null?void 0:n.getValue(a))===null||r===void 0?void 0:r.liveStyle)!==void 0)&&(i[a]=s[a]);return i}function O0(e,t,n){const r=hd(e,t,n);for(const s in e)if(Oe(e[s])||Oe(t[s])){const i=as.indexOf(s)!==-1?"attr"+s.charAt(0).toUpperCase()+s.substring(1):s;r[i]=e[s]}return r}function f2(e,t){try{t.dimensions=typeof e.getBBox=="function"?e.getBBox():e.getBoundingClientRect()}catch{t.dimensions={x:0,y:0,width:0,height:0}}}const yh=["x","y","width","height","cx","cy","r"],h2={useVisualState:P0({scrapeMotionValuesFromProps:O0,createRenderState:R0,onUpdate:({props:e,prevProps:t,current:n,renderState:r,latestValues:s})=>{if(!n)return;let i=!!e.drag;if(!i){for(const l in s)if(mr.has(l)){i=!0;break}}if(!i)return;let a=!t;if(t)for(let l=0;l<yh.length;l++){const c=yh[l];e[c]!==t[c]&&(a=!0)}a&&ce.read(()=>{f2(n,r),ce.render(()=>{ud(r,s,fd(n.tagName),e.transformTemplate),V0(n,r)})})}})},m2={useVisualState:P0({scrapeMotionValuesFromProps:hd,createRenderState:dd})};function z0(e,t,n){for(const r in t)!Oe(t[r])&&!I0(r,n)&&(e[r]=t[r])}function p2({transformTemplate:e},t){return w.useMemo(()=>{const n=dd();return cd(n,t,e),Object.assign({},n.vars,n.style)},[t])}function g2(e,t){const n=e.style||{},r={};return z0(r,n,e),Object.assign(r,p2(e,t)),r}function y2(e,t){const n={},r=g2(e,t);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}function x2(e,t,n,r){const s=w.useMemo(()=>{const i=R0();return ud(i,t,fd(r),e.transformTemplate),{...i.attrs,style:{...i.style}}},[t]);if(e.style){const i={};z0(i,e.style,e),s.style={...i,...s.style}}return s}function v2(e=!1){return(n,r,s,{latestValues:i},a)=>{const c=(id(n)?x2:y2)(r,i,a,n),u=R1(r,typeof n=="string",e),d=n!==w.Fragment?{...u,...c,ref:s}:{},{children:h}=r,f=w.useMemo(()=>Oe(h)?h.get():h,[h]);return w.createElement(n,{...d,children:f})}}function w2(e,t){return function(r,{forwardMotionProps:s}={forwardMotionProps:!1}){const a={...id(r)?h2:m2,preloadedFeatures:e,useRender:v2(s),createVisualElement:t,Component:r};return B1(a)}}function $0(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function No(e,t,n){const r=e.getProps();return ad(r,t,n!==void 0?n:r.custom,e)}const S2=ed(()=>window.ScrollTimeline!==void 0);class b2{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,n){for(let r=0;r<this.animations.length;r++)this.animations[r][t]=n}attachTimeline(t,n){const r=this.animations.map(s=>{if(S2()&&s.attachTimeline)return s.attachTimeline(t);if(typeof n=="function")return n(s)});return()=>{r.forEach((s,i)=>{s&&s(),this.animations[i].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let n=0;n<this.animations.length;n++)t=Math.max(t,this.animations[n].duration);return t}runAll(t){this.animations.forEach(n=>n[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class j2 extends b2{then(t,n){return Promise.all(this.animations).then(t).catch(n)}}function md(e,t){return e?e[t]||e.default||e:void 0}const Pc=2e4;function U0(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<Pc;)t+=n,r=e.next(t);return t>=Pc?1/0:t}function pd(e){return typeof e=="function"}function xh(e,t){e.timeline=t,e.onfinish=null}const gd=e=>Array.isArray(e)&&typeof e[0]=="number",N2={linearEasing:void 0};function C2(e,t){const n=ed(e);return()=>{var r;return(r=N2[t])!==null&&r!==void 0?r:n()}}const Ba=C2(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),B0=(e,t,n=10)=>{let r="";const s=Math.max(Math.round(t/n),2);for(let i=0;i<s;i++)r+=e(qr(0,s-1,i))+", ";return`linear(${r.substring(0,r.length-2)})`};function H0(e){return!!(typeof e=="function"&&Ba()||!e||typeof e=="string"&&(e in Ac||Ba())||gd(e)||Array.isArray(e)&&e.every(H0))}const Cs=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,Ac={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Cs([0,.65,.55,1]),circOut:Cs([.55,0,1,.45]),backIn:Cs([.31,.01,.66,-.59]),backOut:Cs([.33,1.53,.69,.99])};function W0(e,t){if(e)return typeof e=="function"&&Ba()?B0(e,t):gd(e)?Cs(e):Array.isArray(e)?e.map(n=>W0(n,t)||Ac.easeOut):Ac[e]}const bt={x:!1,y:!1};function K0(){return bt.x||bt.y}function k2(e,t,n){var r;if(e instanceof Element)return[e];if(typeof e=="string"){let s=document;const i=(r=void 0)!==null&&r!==void 0?r:s.querySelectorAll(e);return i?Array.from(i):[]}return Array.from(e)}function G0(e,t){const n=k2(e),r=new AbortController,s={passive:!0,...t,signal:r.signal};return[n,s,()=>r.abort()]}function vh(e){return t=>{t.pointerType==="touch"||K0()||e(t)}}function T2(e,t,n={}){const[r,s,i]=G0(e,n),a=vh(l=>{const{target:c}=l,u=t(l);if(typeof u!="function"||!c)return;const d=vh(h=>{u(h),c.removeEventListener("pointerleave",d)});c.addEventListener("pointerleave",d,s)});return r.forEach(l=>{l.addEventListener("pointerenter",a,s)}),i}const Q0=(e,t)=>t?e===t?!0:Q0(e,t.parentElement):!1,yd=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1,_2=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function E2(e){return _2.has(e.tagName)||e.tabIndex!==-1}const ks=new WeakSet;function wh(e){return t=>{t.key==="Enter"&&e(t)}}function pl(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}const P2=(e,t)=>{const n=e.currentTarget;if(!n)return;const r=wh(()=>{if(ks.has(n))return;pl(n,"down");const s=wh(()=>{pl(n,"up")}),i=()=>pl(n,"cancel");n.addEventListener("keyup",s,t),n.addEventListener("blur",i,t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function Sh(e){return yd(e)&&!K0()}function A2(e,t,n={}){const[r,s,i]=G0(e,n),a=l=>{const c=l.currentTarget;if(!Sh(l)||ks.has(c))return;ks.add(c);const u=t(l),d=(g,x)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",f),!(!Sh(g)||!ks.has(c))&&(ks.delete(c),typeof u=="function"&&u(g,{success:x}))},h=g=>{d(g,n.useGlobalTarget||Q0(c,g.target))},f=g=>{d(g,!1)};window.addEventListener("pointerup",h,s),window.addEventListener("pointercancel",f,s)};return r.forEach(l=>{!E2(l)&&l.getAttribute("tabindex")===null&&(l.tabIndex=0),(n.useGlobalTarget?window:l).addEventListener("pointerdown",a,s),l.addEventListener("focus",u=>P2(u,s),s)}),i}function D2(e){return e==="x"||e==="y"?bt[e]?null:(bt[e]=!0,()=>{bt[e]=!1}):bt.x||bt.y?null:(bt.x=bt.y=!0,()=>{bt.x=bt.y=!1})}const q0=new Set(["width","height","top","left","right","bottom",...as]);let ha;function M2(){ha=void 0}const zt={now:()=>(ha===void 0&&zt.set(De.isProcessing||_1.useManualTiming?De.timestamp:performance.now()),ha),set:e=>{ha=e,queueMicrotask(M2)}};function xd(e,t){e.indexOf(t)===-1&&e.push(t)}function vd(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class wd{constructor(){this.subscriptions=[]}add(t){return xd(this.subscriptions,t),()=>vd(this.subscriptions,t)}notify(t,n,r){const s=this.subscriptions.length;if(s)if(s===1)this.subscriptions[0](t,n,r);else for(let i=0;i<s;i++){const a=this.subscriptions[i];a&&a(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function X0(e,t){return t?e*(1e3/t):0}const bh=30,R2=e=>!isNaN(parseFloat(e));class L2{constructor(t,n={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(r,s=!0)=>{const i=zt.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),s&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=n.owner}setCurrent(t){this.current=t,this.updatedAt=zt.now(),this.canTrackVelocity===null&&t!==void 0&&(this.canTrackVelocity=R2(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new wd);const r=this.events[t].add(n);return t==="change"?()=>{r(),ce.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-r}jump(t,n=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=zt.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||t-this.updatedAt>bh)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,bh);return X0(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ci(e,t){return new L2(e,t)}function F2(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,ci(n))}function V2(e,t){const n=No(e,t);let{transitionEnd:r={},transition:s={},...i}=n||{};i={...i,...r};for(const a in i){const l=q1(i[a]);F2(e,a,l)}}function I2(e){return!!(Oe(e)&&e.add)}function Dc(e,t){const n=e.getValue("willChange");if(I2(n))return n.add(t)}function Y0(e){return e.props[T0]}const Z0=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,O2=1e-7,z2=12;function $2(e,t,n,r,s){let i,a,l=0;do a=t+(n-t)/2,i=Z0(a,r,s)-e,i>0?n=a:t=a;while(Math.abs(i)>O2&&++l<z2);return a}function Ni(e,t,n,r){if(e===t&&n===r)return it;const s=i=>$2(i,0,1,e,n);return i=>i===0||i===1?i:Z0(s(i),t,r)}const J0=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,ey=e=>t=>1-e(1-t),ty=Ni(.33,1.53,.69,.99),Sd=ey(ty),ny=J0(Sd),ry=e=>(e*=2)<1?.5*Sd(e):.5*(2-Math.pow(2,-10*(e-1))),bd=e=>1-Math.sin(Math.acos(e)),sy=ey(bd),iy=J0(bd),ay=e=>/^0[^.\s]+$/u.test(e);function U2(e){return typeof e=="number"?e===0:e!==null?e==="none"||e==="0"||ay(e):!0}const Is=e=>Math.round(e*1e5)/1e5,jd=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function B2(e){return e==null}const H2=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Nd=(e,t)=>n=>!!(typeof n=="string"&&H2.test(n)&&n.startsWith(e)||t&&!B2(n)&&Object.prototype.hasOwnProperty.call(n,t)),oy=(e,t,n)=>r=>{if(typeof r!="string")return r;const[s,i,a,l]=r.match(jd);return{[e]:parseFloat(s),[t]:parseFloat(i),[n]:parseFloat(a),alpha:l!==void 0?parseFloat(l):1}},W2=e=>on(0,255,e),gl={...os,transform:e=>Math.round(W2(e))},Xn={test:Nd("rgb","red"),parse:oy("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+gl.transform(e)+", "+gl.transform(t)+", "+gl.transform(n)+", "+Is(li.transform(r))+")"};function K2(e){let t="",n="",r="",s="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),s=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),s=e.substring(4,5),t+=t,n+=n,r+=r,s+=s),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:s?parseInt(s,16)/255:1}}const Mc={test:Nd("#"),parse:K2,transform:Xn.transform},Dr={test:Nd("hsl","hue"),parse:oy("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+Ot.transform(Is(t))+", "+Ot.transform(Is(n))+", "+Is(li.transform(r))+")"},Ve={test:e=>Xn.test(e)||Mc.test(e)||Dr.test(e),parse:e=>Xn.test(e)?Xn.parse(e):Dr.test(e)?Dr.parse(e):Mc.parse(e),transform:e=>typeof e=="string"?e:e.hasOwnProperty("red")?Xn.transform(e):Dr.transform(e)},G2=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function Q2(e){var t,n;return isNaN(e)&&typeof e=="string"&&(((t=e.match(jd))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(G2))===null||n===void 0?void 0:n.length)||0)>0}const ly="number",cy="color",q2="var",X2="var(",jh="${}",Y2=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function ui(e){const t=e.toString(),n=[],r={color:[],number:[],var:[]},s=[];let i=0;const l=t.replace(Y2,c=>(Ve.test(c)?(r.color.push(i),s.push(cy),n.push(Ve.parse(c))):c.startsWith(X2)?(r.var.push(i),s.push(q2),n.push(c)):(r.number.push(i),s.push(ly),n.push(parseFloat(c))),++i,jh)).split(jh);return{values:n,split:l,indexes:r,types:s}}function uy(e){return ui(e).values}function dy(e){const{split:t,types:n}=ui(e),r=t.length;return s=>{let i="";for(let a=0;a<r;a++)if(i+=t[a],s[a]!==void 0){const l=n[a];l===ly?i+=Is(s[a]):l===cy?i+=Ve.transform(s[a]):i+=s[a]}return i}}const Z2=e=>typeof e=="number"?0:e;function J2(e){const t=uy(e);return dy(e)(t.map(Z2))}const An={test:Q2,parse:uy,createTransformer:dy,getAnimatableNone:J2},eS=new Set(["brightness","contrast","saturate","opacity"]);function tS(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(jd)||[];if(!r)return e;const s=n.replace(r,"");let i=eS.has(t)?1:0;return r!==n&&(i*=100),t+"("+i+s+")"}const nS=/\b([a-z-]*)\(.*?\)/gu,Rc={...An,getAnimatableNone:e=>{const t=e.match(nS);return t?t.map(tS).join(" "):e}},rS={...ld,color:Ve,backgroundColor:Ve,outlineColor:Ve,fill:Ve,stroke:Ve,borderColor:Ve,borderTopColor:Ve,borderRightColor:Ve,borderBottomColor:Ve,borderLeftColor:Ve,filter:Rc,WebkitFilter:Rc},Cd=e=>rS[e];function fy(e,t){let n=Cd(e);return n!==Rc&&(n=An),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const sS=new Set(["auto","none","0"]);function iS(e,t,n){let r=0,s;for(;r<e.length&&!s;){const i=e[r];typeof i=="string"&&!sS.has(i)&&ui(i).values.length&&(s=e[r]),r++}if(s&&n)for(const i of t)e[i]=fy(n,s)}const Nh=e=>e===os||e===q,Ch=(e,t)=>parseFloat(e.split(", ")[t]),kh=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const s=r.match(/^matrix3d\((.+)\)$/u);if(s)return Ch(s[1],t);{const i=r.match(/^matrix\((.+)\)$/u);return i?Ch(i[1],e):0}},aS=new Set(["x","y","z"]),oS=as.filter(e=>!aS.has(e));function lS(e){const t=[];return oS.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t}const Yr={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:kh(4,13),y:kh(5,14)};Yr.translateX=Yr.x;Yr.translateY=Yr.y;const er=new Set;let Lc=!1,Fc=!1;function hy(){if(Fc){const e=Array.from(er).filter(r=>r.needsMeasurement),t=new Set(e.map(r=>r.element)),n=new Map;t.forEach(r=>{const s=lS(r);s.length&&(n.set(r,s),r.render())}),e.forEach(r=>r.measureInitialState()),t.forEach(r=>{r.render();const s=n.get(r);s&&s.forEach(([i,a])=>{var l;(l=r.getValue(i))===null||l===void 0||l.set(a)})}),e.forEach(r=>r.measureEndState()),e.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}Fc=!1,Lc=!1,er.forEach(e=>e.complete()),er.clear()}function my(){er.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(Fc=!0)})}function cS(){my(),hy()}class kd{constructor(t,n,r,s,i,a=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=n,this.name=r,this.motionValue=s,this.element=i,this.isAsync=a}scheduleResolve(){this.isScheduled=!0,this.isAsync?(er.add(this),Lc||(Lc=!0,ce.read(my),ce.resolveKeyframes(hy))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:n,element:r,motionValue:s}=this;for(let i=0;i<t.length;i++)if(t[i]===null)if(i===0){const a=s==null?void 0:s.get(),l=t[t.length-1];if(a!==void 0)t[0]=a;else if(r&&n){const c=r.readValue(n,l);c!=null&&(t[0]=c)}t[0]===void 0&&(t[0]=l),s&&a===void 0&&s.set(t[0])}else t[i]=t[i-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),er.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,er.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const py=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),uS=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function dS(e){const t=uS.exec(e);if(!t)return[,];const[,n,r,s]=t;return[`--${n??r}`,s]}function gy(e,t,n=1){const[r,s]=dS(e);if(!r)return;const i=window.getComputedStyle(t).getPropertyValue(r);if(i){const a=i.trim();return py(a)?parseFloat(a):a}return od(s)?gy(s,t,n+1):s}const yy=e=>t=>t.test(e),fS={test:e=>e==="auto",parse:e=>e},xy=[os,q,Ot,hn,t2,e2,fS],Th=e=>xy.find(yy(e));class vy extends kd{constructor(t,n,r,s,i){super(t,n,r,s,i,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:n,name:r}=this;if(!n||!n.current)return;super.readKeyframes();for(let c=0;c<t.length;c++){let u=t[c];if(typeof u=="string"&&(u=u.trim(),od(u))){const d=gy(u,n.current);d!==void 0&&(t[c]=d),c===t.length-1&&(this.finalKeyframe=u)}}if(this.resolveNoneKeyframes(),!q0.has(r)||t.length!==2)return;const[s,i]=t,a=Th(s),l=Th(i);if(a!==l)if(Nh(a)&&Nh(l))for(let c=0;c<t.length;c++){const u=t[c];typeof u=="string"&&(t[c]=parseFloat(u))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:n}=this,r=[];for(let s=0;s<t.length;s++)U2(t[s])&&r.push(s);r.length&&iS(t,r,n)}measureInitialState(){const{element:t,unresolvedKeyframes:n,name:r}=this;if(!t||!t.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Yr[r](t.measureViewportBox(),window.getComputedStyle(t.current)),n[0]=this.measuredOrigin;const s=n[n.length-1];s!==void 0&&t.getValue(r,s).jump(s,!1)}measureEndState(){var t;const{element:n,name:r,unresolvedKeyframes:s}=this;if(!n||!n.current)return;const i=n.getValue(r);i&&i.jump(this.measuredOrigin,!1);const a=s.length-1,l=s[a];s[a]=Yr[r](n.measureViewportBox(),window.getComputedStyle(n.current)),l!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=l),!((t=this.removedTransforms)===null||t===void 0)&&t.length&&this.removedTransforms.forEach(([c,u])=>{n.getValue(c).set(u)}),this.resolveNoneKeyframes()}}const _h=(e,t)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(An.test(e)||e==="0")&&!e.startsWith("url("));function hS(e){const t=e[0];if(e.length===1)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}function mS(e,t,n,r){const s=e[0];if(s===null)return!1;if(t==="display"||t==="visibility")return!0;const i=e[e.length-1],a=_h(s,t),l=_h(i,t);return!a||!l?!1:hS(e)||(n==="spring"||pd(n))&&r}const pS=e=>e!==null;function Co(e,{repeat:t,repeatType:n="loop"},r){const s=e.filter(pS),i=t&&n!=="loop"&&t%2===1?0:s.length-1;return!i||r===void 0?s[i]:r}const gS=40;class wy{constructor({autoplay:t=!0,delay:n=0,type:r="keyframes",repeat:s=0,repeatDelay:i=0,repeatType:a="loop",...l}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=zt.now(),this.options={autoplay:t,delay:n,type:r,repeat:s,repeatDelay:i,repeatType:a,...l},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>gS?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&cS(),this._resolved}onKeyframesResolved(t,n){this.resolvedAt=zt.now(),this.hasAttemptedResolve=!0;const{name:r,type:s,velocity:i,delay:a,onComplete:l,onUpdate:c,isGenerator:u}=this.options;if(!u&&!mS(t,r,s,i))if(a)this.options.duration=0;else{c&&c(Co(t,this.options,n)),l&&l(),this.resolveFinishedPromise();return}const d=this.initPlayback(t,n);d!==!1&&(this._resolved={keyframes:t,finalKeyframe:n,...d},this.onPostResolved())}onPostResolved(){}then(t,n){return this.currentFinishedPromise.then(t,n)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}const me=(e,t,n)=>e+(t-e)*n;function yl(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function yS({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let s=0,i=0,a=0;if(!t)s=i=a=n;else{const l=n<.5?n*(1+t):n+t-n*t,c=2*n-l;s=yl(c,l,e+1/3),i=yl(c,l,e),a=yl(c,l,e-1/3)}return{red:Math.round(s*255),green:Math.round(i*255),blue:Math.round(a*255),alpha:r}}function Ha(e,t){return n=>n>0?t:e}const xl=(e,t,n)=>{const r=e*e,s=n*(t*t-r)+r;return s<0?0:Math.sqrt(s)},xS=[Mc,Xn,Dr],vS=e=>xS.find(t=>t.test(e));function Eh(e){const t=vS(e);if(!t)return!1;let n=t.parse(e);return t===Dr&&(n=yS(n)),n}const Ph=(e,t)=>{const n=Eh(e),r=Eh(t);if(!n||!r)return Ha(e,t);const s={...n};return i=>(s.red=xl(n.red,r.red,i),s.green=xl(n.green,r.green,i),s.blue=xl(n.blue,r.blue,i),s.alpha=me(n.alpha,r.alpha,i),Xn.transform(s))},wS=(e,t)=>n=>t(e(n)),Ci=(...e)=>e.reduce(wS),Vc=new Set(["none","hidden"]);function SS(e,t){return Vc.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}function bS(e,t){return n=>me(e,t,n)}function Td(e){return typeof e=="number"?bS:typeof e=="string"?od(e)?Ha:Ve.test(e)?Ph:CS:Array.isArray(e)?Sy:typeof e=="object"?Ve.test(e)?Ph:jS:Ha}function Sy(e,t){const n=[...e],r=n.length,s=e.map((i,a)=>Td(i)(i,t[a]));return i=>{for(let a=0;a<r;a++)n[a]=s[a](i);return n}}function jS(e,t){const n={...e,...t},r={};for(const s in n)e[s]!==void 0&&t[s]!==void 0&&(r[s]=Td(e[s])(e[s],t[s]));return s=>{for(const i in r)n[i]=r[i](s);return n}}function NS(e,t){var n;const r=[],s={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){const a=t.types[i],l=e.indexes[a][s[a]],c=(n=e.values[l])!==null&&n!==void 0?n:0;r[i]=c,s[a]++}return r}const CS=(e,t)=>{const n=An.createTransformer(t),r=ui(e),s=ui(t);return r.indexes.var.length===s.indexes.var.length&&r.indexes.color.length===s.indexes.color.length&&r.indexes.number.length>=s.indexes.number.length?Vc.has(e)&&!s.values.length||Vc.has(t)&&!r.values.length?SS(e,t):Ci(Sy(NS(r,s),s.values),n):Ha(e,t)};function by(e,t,n){return typeof e=="number"&&typeof t=="number"&&typeof n=="number"?me(e,t,n):Td(e)(e,t)}const kS=5;function jy(e,t,n){const r=Math.max(t-kS,0);return X0(n-e(r),t-r)}const ye={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},vl=.001;function TS({duration:e=ye.duration,bounce:t=ye.bounce,velocity:n=ye.velocity,mass:r=ye.mass}){let s,i,a=1-t;a=on(ye.minDamping,ye.maxDamping,a),e=on(ye.minDuration,ye.maxDuration,en(e)),a<1?(s=u=>{const d=u*a,h=d*e,f=d-n,g=Ic(u,a),x=Math.exp(-h);return vl-f/g*x},i=u=>{const h=u*a*e,f=h*n+n,g=Math.pow(a,2)*Math.pow(u,2)*e,x=Math.exp(-h),v=Ic(Math.pow(u,2),a);return(-s(u)+vl>0?-1:1)*((f-g)*x)/v}):(s=u=>{const d=Math.exp(-u*e),h=(u-n)*e+1;return-vl+d*h},i=u=>{const d=Math.exp(-u*e),h=(n-u)*(e*e);return d*h});const l=5/e,c=ES(s,i,l);if(e=Jt(e),isNaN(c))return{stiffness:ye.stiffness,damping:ye.damping,duration:e};{const u=Math.pow(c,2)*r;return{stiffness:u,damping:a*2*Math.sqrt(r*u),duration:e}}}const _S=12;function ES(e,t,n){let r=n;for(let s=1;s<_S;s++)r=r-e(r)/t(r);return r}function Ic(e,t){return e*Math.sqrt(1-t*t)}const PS=["duration","bounce"],AS=["stiffness","damping","mass"];function Ah(e,t){return t.some(n=>e[n]!==void 0)}function DS(e){let t={velocity:ye.velocity,stiffness:ye.stiffness,damping:ye.damping,mass:ye.mass,isResolvedFromDuration:!1,...e};if(!Ah(e,AS)&&Ah(e,PS))if(e.visualDuration){const n=e.visualDuration,r=2*Math.PI/(n*1.2),s=r*r,i=2*on(.05,1,1-(e.bounce||0))*Math.sqrt(s);t={...t,mass:ye.mass,stiffness:s,damping:i}}else{const n=TS(e);t={...t,...n,mass:ye.mass},t.isResolvedFromDuration=!0}return t}function Ny(e=ye.visualDuration,t=ye.bounce){const n=typeof e!="object"?{visualDuration:e,keyframes:[0,1],bounce:t}:e;let{restSpeed:r,restDelta:s}=n;const i=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],l={done:!1,value:i},{stiffness:c,damping:u,mass:d,duration:h,velocity:f,isResolvedFromDuration:g}=DS({...n,velocity:-en(n.velocity||0)}),x=f||0,v=u/(2*Math.sqrt(c*d)),S=a-i,p=en(Math.sqrt(c/d)),m=Math.abs(S)<5;r||(r=m?ye.restSpeed.granular:ye.restSpeed.default),s||(s=m?ye.restDelta.granular:ye.restDelta.default);let y;if(v<1){const j=Ic(p,v);y=k=>{const _=Math.exp(-v*p*k);return a-_*((x+v*p*S)/j*Math.sin(j*k)+S*Math.cos(j*k))}}else if(v===1)y=j=>a-Math.exp(-p*j)*(S+(x+p*S)*j);else{const j=p*Math.sqrt(v*v-1);y=k=>{const _=Math.exp(-v*p*k),C=Math.min(j*k,300);return a-_*((x+v*p*S)*Math.sinh(C)+j*S*Math.cosh(C))/j}}const b={calculatedDuration:g&&h||null,next:j=>{const k=y(j);if(g)l.done=j>=h;else{let _=0;v<1&&(_=j===0?Jt(x):jy(y,j,k));const C=Math.abs(_)<=r,P=Math.abs(a-k)<=s;l.done=C&&P}return l.value=l.done?a:k,l},toString:()=>{const j=Math.min(U0(b),Pc),k=B0(_=>b.next(j*_).value,j,30);return j+"ms "+k}};return b}function Dh({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:s=10,bounceStiffness:i=500,modifyTarget:a,min:l,max:c,restDelta:u=.5,restSpeed:d}){const h=e[0],f={done:!1,value:h},g=C=>l!==void 0&&C<l||c!==void 0&&C>c,x=C=>l===void 0?c:c===void 0||Math.abs(l-C)<Math.abs(c-C)?l:c;let v=n*t;const S=h+v,p=a===void 0?S:a(S);p!==S&&(v=p-h);const m=C=>-v*Math.exp(-C/r),y=C=>p+m(C),b=C=>{const P=m(C),D=y(C);f.done=Math.abs(P)<=u,f.value=f.done?p:D};let j,k;const _=C=>{g(f.value)&&(j=C,k=Ny({keyframes:[f.value,x(f.value)],velocity:jy(y,C,f.value),damping:s,stiffness:i,restDelta:u,restSpeed:d}))};return _(0),{calculatedDuration:null,next:C=>{let P=!1;return!k&&j===void 0&&(P=!0,b(C),_(C)),j!==void 0&&C>=j?k.next(C-j):(!P&&b(C),f)}}}const MS=Ni(.42,0,1,1),RS=Ni(0,0,.58,1),Cy=Ni(.42,0,.58,1),LS=e=>Array.isArray(e)&&typeof e[0]!="number",FS={linear:it,easeIn:MS,easeInOut:Cy,easeOut:RS,circIn:bd,circInOut:iy,circOut:sy,backIn:Sd,backInOut:ny,backOut:ty,anticipate:ry},Mh=e=>{if(gd(e)){b0(e.length===4);const[t,n,r,s]=e;return Ni(t,n,r,s)}else if(typeof e=="string")return FS[e];return e};function VS(e,t,n){const r=[],s=n||by,i=e.length-1;for(let a=0;a<i;a++){let l=s(e[a],e[a+1]);if(t){const c=Array.isArray(t)?t[a]||it:t;l=Ci(c,l)}r.push(l)}return r}function IS(e,t,{clamp:n=!0,ease:r,mixer:s}={}){const i=e.length;if(b0(i===t.length),i===1)return()=>t[0];if(i===2&&t[0]===t[1])return()=>t[1];const a=e[0]===e[1];e[0]>e[i-1]&&(e=[...e].reverse(),t=[...t].reverse());const l=VS(t,r,s),c=l.length,u=d=>{if(a&&d<e[0])return t[0];let h=0;if(c>1)for(;h<e.length-2&&!(d<e[h+1]);h++);const f=qr(e[h],e[h+1],d);return l[h](f)};return n?d=>u(on(e[0],e[i-1],d)):u}function OS(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const s=qr(0,t,r);e.push(me(n,1,s))}}function zS(e){const t=[0];return OS(t,e.length-1),t}function $S(e,t){return e.map(n=>n*t)}function US(e,t){return e.map(()=>t||Cy).splice(0,e.length-1)}function Wa({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const s=LS(r)?r.map(Mh):Mh(r),i={done:!1,value:t[0]},a=$S(n&&n.length===t.length?n:zS(t),e),l=IS(a,t,{ease:Array.isArray(s)?s:US(t,s)});return{calculatedDuration:e,next:c=>(i.value=l(c),i.done=c>=e,i)}}const BS=e=>{const t=({timestamp:n})=>e(n);return{start:()=>ce.update(t,!0),stop:()=>Pn(t),now:()=>De.isProcessing?De.timestamp:zt.now()}},HS={decay:Dh,inertia:Dh,tween:Wa,keyframes:Wa,spring:Ny},WS=e=>e/100;class _d extends wy{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:c}=this.options;c&&c()};const{name:n,motionValue:r,element:s,keyframes:i}=this.options,a=(s==null?void 0:s.KeyframeResolver)||kd,l=(c,u)=>this.onKeyframesResolved(c,u);this.resolver=new a(i,l,n,r,s),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){const{type:n="keyframes",repeat:r=0,repeatDelay:s=0,repeatType:i,velocity:a=0}=this.options,l=pd(n)?n:HS[n]||Wa;let c,u;l!==Wa&&typeof t[0]!="number"&&(c=Ci(WS,by(t[0],t[1])),t=[0,100]);const d=l({...this.options,keyframes:t});i==="mirror"&&(u=l({...this.options,keyframes:[...t].reverse(),velocity:-a})),d.calculatedDuration===null&&(d.calculatedDuration=U0(d));const{calculatedDuration:h}=d,f=h+s,g=f*(r+1)-s;return{generator:d,mirroredGenerator:u,mapPercentToKeyframes:c,calculatedDuration:h,resolvedDuration:f,totalDuration:g}}onPostResolved(){const{autoplay:t=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!t?this.pause():this.state=this.pendingPlayState}tick(t,n=!1){const{resolved:r}=this;if(!r){const{keyframes:C}=this.options;return{done:!0,value:C[C.length-1]}}const{finalKeyframe:s,generator:i,mirroredGenerator:a,mapPercentToKeyframes:l,keyframes:c,calculatedDuration:u,totalDuration:d,resolvedDuration:h}=r;if(this.startTime===null)return i.next(0);const{delay:f,repeat:g,repeatType:x,repeatDelay:v,onUpdate:S}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-d/this.speed,this.startTime)),n?this.currentTime=t:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;const p=this.currentTime-f*(this.speed>=0?1:-1),m=this.speed>=0?p<0:p>d;this.currentTime=Math.max(p,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=d);let y=this.currentTime,b=i;if(g){const C=Math.min(this.currentTime,d)/h;let P=Math.floor(C),D=C%1;!D&&C>=1&&(D=1),D===1&&P--,P=Math.min(P,g+1),!!(P%2)&&(x==="reverse"?(D=1-D,v&&(D-=v/h)):x==="mirror"&&(b=a)),y=on(0,1,D)*h}const j=m?{done:!1,value:c[0]}:b.next(y);l&&(j.value=l(j.value));let{done:k}=j;!m&&u!==null&&(k=this.speed>=0?this.currentTime>=d:this.currentTime<=0);const _=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&k);return _&&s!==void 0&&(j.value=Co(c,this.options,s)),S&&S(j.value),_&&this.finish(),j}get duration(){const{resolved:t}=this;return t?en(t.calculatedDuration):0}get time(){return en(this.currentTime)}set time(t){t=Jt(t),this.currentTime=t,this.holdTime!==null||this.speed===0?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){const n=this.playbackSpeed!==t;this.playbackSpeed=t,n&&(this.time=en(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:t=BS,onPlay:n,startTime:r}=this.options;this.driver||(this.driver=t(i=>this.tick(i))),n&&n();const s=this.driver.now();this.holdTime!==null?this.startTime=s-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=s):this.startTime=r??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(t=this.currentTime)!==null&&t!==void 0?t:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}const KS=new Set(["opacity","clipPath","filter","transform"]);function GS(e,t,n,{delay:r=0,duration:s=300,repeat:i=0,repeatType:a="loop",ease:l="easeInOut",times:c}={}){const u={[t]:n};c&&(u.offset=c);const d=W0(l,s);return Array.isArray(d)&&(u.easing=d),e.animate(u,{delay:r,duration:s,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:i+1,direction:a==="reverse"?"alternate":"normal"})}const QS=ed(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Ka=10,qS=2e4;function XS(e){return pd(e.type)||e.type==="spring"||!H0(e.ease)}function YS(e,t){const n=new _d({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0});let r={done:!1,value:e[0]};const s=[];let i=0;for(;!r.done&&i<qS;)r=n.sample(i),s.push(r.value),i+=Ka;return{times:void 0,keyframes:s,duration:i-Ka,ease:"linear"}}const ky={anticipate:ry,backInOut:ny,circInOut:iy};function ZS(e){return e in ky}class Rh extends wy{constructor(t){super(t);const{name:n,motionValue:r,element:s,keyframes:i}=this.options;this.resolver=new vy(i,(a,l)=>this.onKeyframesResolved(a,l),n,r,s),this.resolver.scheduleResolve()}initPlayback(t,n){let{duration:r=300,times:s,ease:i,type:a,motionValue:l,name:c,startTime:u}=this.options;if(!l.owner||!l.owner.current)return!1;if(typeof i=="string"&&Ba()&&ZS(i)&&(i=ky[i]),XS(this.options)){const{onComplete:h,onUpdate:f,motionValue:g,element:x,...v}=this.options,S=YS(t,v);t=S.keyframes,t.length===1&&(t[1]=t[0]),r=S.duration,s=S.times,i=S.ease,a="keyframes"}const d=GS(l.owner.current,c,t,{...this.options,duration:r,times:s,ease:i});return d.startTime=u??this.calcStartTime(),this.pendingTimeline?(xh(d,this.pendingTimeline),this.pendingTimeline=void 0):d.onfinish=()=>{const{onComplete:h}=this.options;l.set(Co(t,this.options,n)),h&&h(),this.cancel(),this.resolveFinishedPromise()},{animation:d,duration:r,times:s,type:a,ease:i,keyframes:t}}get duration(){const{resolved:t}=this;if(!t)return 0;const{duration:n}=t;return en(n)}get time(){const{resolved:t}=this;if(!t)return 0;const{animation:n}=t;return en(n.currentTime||0)}set time(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.currentTime=Jt(t)}get speed(){const{resolved:t}=this;if(!t)return 1;const{animation:n}=t;return n.playbackRate}set speed(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.playbackRate=t}get state(){const{resolved:t}=this;if(!t)return"idle";const{animation:n}=t;return n.playState}get startTime(){const{resolved:t}=this;if(!t)return null;const{animation:n}=t;return n.startTime}attachTimeline(t){if(!this._resolved)this.pendingTimeline=t;else{const{resolved:n}=this;if(!n)return it;const{animation:r}=n;xh(r,t)}return it}play(){if(this.isStopped)return;const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.playState==="finished"&&this.updateFinishedPromise(),n.play()}pause(){const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:t}=this;if(!t)return;const{animation:n,keyframes:r,duration:s,type:i,ease:a,times:l}=t;if(n.playState==="idle"||n.playState==="finished")return;if(this.time){const{motionValue:u,onUpdate:d,onComplete:h,element:f,...g}=this.options,x=new _d({...g,keyframes:r,duration:s,type:i,ease:a,times:l,isGenerator:!0}),v=Jt(this.time);u.setWithVelocity(x.sample(v-Ka).value,x.sample(v).value,Ka)}const{onStop:c}=this.options;c&&c(),this.cancel()}complete(){const{resolved:t}=this;t&&t.animation.finish()}cancel(){const{resolved:t}=this;t&&t.animation.cancel()}static supports(t){const{motionValue:n,name:r,repeatDelay:s,repeatType:i,damping:a,type:l}=t;if(!n||!n.owner||!(n.owner.current instanceof HTMLElement))return!1;const{onUpdate:c,transformTemplate:u}=n.owner.getProps();return QS()&&r&&KS.has(r)&&!c&&!u&&!s&&i!=="mirror"&&a!==0&&l!=="inertia"}}const JS={type:"spring",stiffness:500,damping:25,restSpeed:10},eb=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),tb={type:"keyframes",duration:.8},nb={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},rb=(e,{keyframes:t})=>t.length>2?tb:mr.has(e)?e.startsWith("scale")?eb(t[1]):JS:nb;function sb({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:s,repeat:i,repeatType:a,repeatDelay:l,from:c,elapsed:u,...d}){return!!Object.keys(d).length}const Ed=(e,t,n,r={},s,i)=>a=>{const l=md(r,e)||{},c=l.delay||r.delay||0;let{elapsed:u=0}=r;u=u-Jt(c);let d={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...l,delay:-u,onUpdate:f=>{t.set(f),l.onUpdate&&l.onUpdate(f)},onComplete:()=>{a(),l.onComplete&&l.onComplete()},name:e,motionValue:t,element:i?void 0:s};sb(l)||(d={...d,...rb(e,d)}),d.duration&&(d.duration=Jt(d.duration)),d.repeatDelay&&(d.repeatDelay=Jt(d.repeatDelay)),d.from!==void 0&&(d.keyframes[0]=d.from);let h=!1;if((d.type===!1||d.duration===0&&!d.repeatDelay)&&(d.duration=0,d.delay===0&&(h=!0)),h&&!i&&t.get()!==void 0){const f=Co(d.keyframes,l);if(f!==void 0)return ce.update(()=>{d.onUpdate(f),d.onComplete()}),new j2([])}return!i&&Rh.supports(d)?new Rh(d):new _d(d)};function ib({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function Ty(e,t,{delay:n=0,transitionOverride:r,type:s}={}){var i;let{transition:a=e.getDefaultTransition(),transitionEnd:l,...c}=t;r&&(a=r);const u=[],d=s&&e.animationState&&e.animationState.getState()[s];for(const h in c){const f=e.getValue(h,(i=e.latestValues[h])!==null&&i!==void 0?i:null),g=c[h];if(g===void 0||d&&ib(d,h))continue;const x={delay:n,...md(a||{},h)};let v=!1;if(window.MotionHandoffAnimation){const p=Y0(e);if(p){const m=window.MotionHandoffAnimation(p,h,ce);m!==null&&(x.startTime=m,v=!0)}}Dc(e,h),f.start(Ed(h,f,g,e.shouldReduceMotion&&q0.has(h)?{type:!1}:x,e,v));const S=f.animation;S&&u.push(S)}return l&&Promise.all(u).then(()=>{ce.update(()=>{l&&V2(e,l)})}),u}function Oc(e,t,n={}){var r;const s=No(e,t,n.type==="exit"?(r=e.presenceContext)===null||r===void 0?void 0:r.custom:void 0);let{transition:i=e.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(i=n.transitionOverride);const a=s?()=>Promise.all(Ty(e,s,n)):()=>Promise.resolve(),l=e.variantChildren&&e.variantChildren.size?(u=0)=>{const{delayChildren:d=0,staggerChildren:h,staggerDirection:f}=i;return ab(e,t,d+u,h,f,n)}:()=>Promise.resolve(),{when:c}=i;if(c){const[u,d]=c==="beforeChildren"?[a,l]:[l,a];return u().then(()=>d())}else return Promise.all([a(),l(n.delay)])}function ab(e,t,n=0,r=0,s=1,i){const a=[],l=(e.variantChildren.size-1)*r,c=s===1?(u=0)=>u*r:(u=0)=>l-u*r;return Array.from(e.variantChildren).sort(ob).forEach((u,d)=>{u.notify("AnimationStart",t),a.push(Oc(u,t,{...i,delay:n+c(d)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(a)}function ob(e,t){return e.sortNodePosition(t)}function lb(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const s=t.map(i=>Oc(e,i,n));r=Promise.all(s)}else if(typeof t=="string")r=Oc(e,t,n);else{const s=typeof t=="function"?No(e,t,n.custom):t;r=Promise.all(Ty(e,s,n))}return r.then(()=>{e.notify("AnimationComplete",t)})}const cb=nd.length;function _y(e){if(!e)return;if(!e.isControllingVariants){const n=e.parent?_y(e.parent)||{}:{};return e.props.initial!==void 0&&(n.initial=e.props.initial),n}const t={};for(let n=0;n<cb;n++){const r=nd[n],s=e.props[r];(oi(s)||s===!1)&&(t[r]=s)}return t}const ub=[...td].reverse(),db=td.length;function fb(e){return t=>Promise.all(t.map(({animation:n,options:r})=>lb(e,n,r)))}function hb(e){let t=fb(e),n=Lh(),r=!0;const s=c=>(u,d)=>{var h;const f=No(e,d,c==="exit"?(h=e.presenceContext)===null||h===void 0?void 0:h.custom:void 0);if(f){const{transition:g,transitionEnd:x,...v}=f;u={...u,...v,...x}}return u};function i(c){t=c(e)}function a(c){const{props:u}=e,d=_y(e.parent)||{},h=[],f=new Set;let g={},x=1/0;for(let S=0;S<db;S++){const p=ub[S],m=n[p],y=u[p]!==void 0?u[p]:d[p],b=oi(y),j=p===c?m.isActive:null;j===!1&&(x=S);let k=y===d[p]&&y!==u[p]&&b;if(k&&r&&e.manuallyAnimateOnMount&&(k=!1),m.protectedKeys={...g},!m.isActive&&j===null||!y&&!m.prevProp||bo(y)||typeof y=="boolean")continue;const _=mb(m.prevProp,y);let C=_||p===c&&m.isActive&&!k&&b||S>x&&b,P=!1;const D=Array.isArray(y)?y:[y];let V=D.reduce(s(p),{});j===!1&&(V={});const{prevResolvedValues:A={}}=m,R={...A,...V},X=U=>{C=!0,f.has(U)&&(P=!0,f.delete(U)),m.needsAnimating[U]=!0;const L=e.getValue(U);L&&(L.liveStyle=!1)};for(const U in R){const L=V[U],K=A[U];if(g.hasOwnProperty(U))continue;let Y=!1;Ec(L)&&Ec(K)?Y=!$0(L,K):Y=L!==K,Y?L!=null?X(U):f.add(U):L!==void 0&&f.has(U)?X(U):m.protectedKeys[U]=!0}m.prevProp=y,m.prevResolvedValues=V,m.isActive&&(g={...g,...V}),r&&e.blockInitialAnimation&&(C=!1),C&&(!(k&&_)||P)&&h.push(...D.map(U=>({animation:U,options:{type:p}})))}if(f.size){const S={};f.forEach(p=>{const m=e.getBaseTarget(p),y=e.getValue(p);y&&(y.liveStyle=!0),S[p]=m??null}),h.push({animation:S})}let v=!!h.length;return r&&(u.initial===!1||u.initial===u.animate)&&!e.manuallyAnimateOnMount&&(v=!1),r=!1,v?t(h):Promise.resolve()}function l(c,u){var d;if(n[c].isActive===u)return Promise.resolve();(d=e.variantChildren)===null||d===void 0||d.forEach(f=>{var g;return(g=f.animationState)===null||g===void 0?void 0:g.setActive(c,u)}),n[c].isActive=u;const h=a(c);for(const f in n)n[f].protectedKeys={};return h}return{animateChanges:a,setActive:l,setAnimateFunction:i,getState:()=>n,reset:()=>{n=Lh(),r=!0}}}function mb(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!$0(t,e):!1}function On(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Lh(){return{animate:On(!0),whileInView:On(),whileHover:On(),whileTap:On(),whileDrag:On(),whileFocus:On(),exit:On()}}class Ln{constructor(t){this.isMounted=!1,this.node=t}update(){}}class pb extends Ln{constructor(t){super(t),t.animationState||(t.animationState=hb(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();bo(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),(t=this.unmountControls)===null||t===void 0||t.call(this)}}let gb=0;class yb extends Ln{constructor(){super(...arguments),this.id=gb++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===r)return;const s=this.node.animationState.setActive("exit",!t);n&&!t&&s.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const xb={animation:{Feature:pb},exit:{Feature:yb}};function di(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function ki(e){return{point:{x:e.pageX,y:e.pageY}}}const vb=e=>t=>yd(t)&&e(t,ki(t));function Os(e,t,n,r){return di(e,t,vb(n),r)}const Fh=(e,t)=>Math.abs(e-t);function wb(e,t){const n=Fh(e.x,t.x),r=Fh(e.y,t.y);return Math.sqrt(n**2+r**2)}class Ey{constructor(t,n,{transformPagePoint:r,contextWindow:s,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const h=Sl(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,g=wb(h.offset,{x:0,y:0})>=3;if(!f&&!g)return;const{point:x}=h,{timestamp:v}=De;this.history.push({...x,timestamp:v});const{onStart:S,onMove:p}=this.handlers;f||(S&&S(this.lastMoveEvent,h),this.startEvent=this.lastMoveEvent),p&&p(this.lastMoveEvent,h)},this.handlePointerMove=(h,f)=>{this.lastMoveEvent=h,this.lastMoveEventInfo=wl(f,this.transformPagePoint),ce.update(this.updatePoint,!0)},this.handlePointerUp=(h,f)=>{this.end();const{onEnd:g,onSessionEnd:x,resumeAnimation:v}=this.handlers;if(this.dragSnapToOrigin&&v&&v(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const S=Sl(h.type==="pointercancel"?this.lastMoveEventInfo:wl(f,this.transformPagePoint),this.history);this.startEvent&&g&&g(h,S),x&&x(h,S)},!yd(t))return;this.dragSnapToOrigin=i,this.handlers=n,this.transformPagePoint=r,this.contextWindow=s||window;const a=ki(t),l=wl(a,this.transformPagePoint),{point:c}=l,{timestamp:u}=De;this.history=[{...c,timestamp:u}];const{onSessionStart:d}=n;d&&d(t,Sl(l,this.history)),this.removeListeners=Ci(Os(this.contextWindow,"pointermove",this.handlePointerMove),Os(this.contextWindow,"pointerup",this.handlePointerUp),Os(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Pn(this.updatePoint)}}function wl(e,t){return t?{point:t(e.point)}:e}function Vh(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Sl({point:e},t){return{point:e,delta:Vh(e,Py(t)),offset:Vh(e,Sb(t)),velocity:bb(t,.1)}}function Sb(e){return e[0]}function Py(e){return e[e.length-1]}function bb(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const s=Py(e);for(;n>=0&&(r=e[n],!(s.timestamp-r.timestamp>Jt(t)));)n--;if(!r)return{x:0,y:0};const i=en(s.timestamp-r.timestamp);if(i===0)return{x:0,y:0};const a={x:(s.x-r.x)/i,y:(s.y-r.y)/i};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}const Ay=1e-4,jb=1-Ay,Nb=1+Ay,Dy=.01,Cb=0-Dy,kb=0+Dy;function ot(e){return e.max-e.min}function Tb(e,t,n){return Math.abs(e-t)<=n}function Ih(e,t,n,r=.5){e.origin=r,e.originPoint=me(t.min,t.max,e.origin),e.scale=ot(n)/ot(t),e.translate=me(n.min,n.max,e.origin)-e.originPoint,(e.scale>=jb&&e.scale<=Nb||isNaN(e.scale))&&(e.scale=1),(e.translate>=Cb&&e.translate<=kb||isNaN(e.translate))&&(e.translate=0)}function zs(e,t,n,r){Ih(e.x,t.x,n.x,r?r.originX:void 0),Ih(e.y,t.y,n.y,r?r.originY:void 0)}function Oh(e,t,n){e.min=n.min+t.min,e.max=e.min+ot(t)}function _b(e,t,n){Oh(e.x,t.x,n.x),Oh(e.y,t.y,n.y)}function zh(e,t,n){e.min=t.min-n.min,e.max=e.min+ot(t)}function $s(e,t,n){zh(e.x,t.x,n.x),zh(e.y,t.y,n.y)}function Eb(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?me(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?me(n,e,r.max):Math.min(e,n)),e}function $h(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function Pb(e,{top:t,left:n,bottom:r,right:s}){return{x:$h(e.x,n,s),y:$h(e.y,t,r)}}function Uh(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function Ab(e,t){return{x:Uh(e.x,t.x),y:Uh(e.y,t.y)}}function Db(e,t){let n=.5;const r=ot(e),s=ot(t);return s>r?n=qr(t.min,t.max-r,e.min):r>s&&(n=qr(e.min,e.max-s,t.min)),on(0,1,n)}function Mb(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const zc=.35;function Rb(e=zc){return e===!1?e=0:e===!0&&(e=zc),{x:Bh(e,"left","right"),y:Bh(e,"top","bottom")}}function Bh(e,t,n){return{min:Hh(e,t),max:Hh(e,n)}}function Hh(e,t){return typeof e=="number"?e:e[t]||0}const Wh=()=>({translate:0,scale:1,origin:0,originPoint:0}),Mr=()=>({x:Wh(),y:Wh()}),Kh=()=>({min:0,max:0}),ve=()=>({x:Kh(),y:Kh()});function mt(e){return[e("x"),e("y")]}function My({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function Lb({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function Fb(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function bl(e){return e===void 0||e===1}function $c({scale:e,scaleX:t,scaleY:n}){return!bl(e)||!bl(t)||!bl(n)}function Bn(e){return $c(e)||Ry(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function Ry(e){return Gh(e.x)||Gh(e.y)}function Gh(e){return e&&e!=="0%"}function Ga(e,t,n){const r=e-n,s=t*r;return n+s}function Qh(e,t,n,r,s){return s!==void 0&&(e=Ga(e,s,r)),Ga(e,n,r)+t}function Uc(e,t=0,n=1,r,s){e.min=Qh(e.min,t,n,r,s),e.max=Qh(e.max,t,n,r,s)}function Ly(e,{x:t,y:n}){Uc(e.x,t.translate,t.scale,t.originPoint),Uc(e.y,n.translate,n.scale,n.originPoint)}const qh=.999999999999,Xh=1.0000000000001;function Vb(e,t,n,r=!1){const s=n.length;if(!s)return;t.x=t.y=1;let i,a;for(let l=0;l<s;l++){i=n[l],a=i.projectionDelta;const{visualElement:c}=i.options;c&&c.props.style&&c.props.style.display==="contents"||(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&Lr(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),a&&(t.x*=a.x.scale,t.y*=a.y.scale,Ly(e,a)),r&&Bn(i.latestValues)&&Lr(e,i.latestValues))}t.x<Xh&&t.x>qh&&(t.x=1),t.y<Xh&&t.y>qh&&(t.y=1)}function Rr(e,t){e.min=e.min+t,e.max=e.max+t}function Yh(e,t,n,r,s=.5){const i=me(e.min,e.max,s);Uc(e,t,n,i,r)}function Lr(e,t){Yh(e.x,t.x,t.scaleX,t.scale,t.originX),Yh(e.y,t.y,t.scaleY,t.scale,t.originY)}function Fy(e,t){return My(Fb(e.getBoundingClientRect(),t))}function Ib(e,t,n){const r=Fy(e,n),{scroll:s}=t;return s&&(Rr(r.x,s.offset.x),Rr(r.y,s.offset.y)),r}const Vy=({current:e})=>e?e.ownerDocument.defaultView:null,Ob=new WeakMap;class zb{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ve(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const s=d=>{const{dragSnapToOrigin:h}=this.getProps();h?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(ki(d).point)},i=(d,h)=>{const{drag:f,dragPropagation:g,onDragStart:x}=this.getProps();if(f&&!g&&(this.openDragLock&&this.openDragLock(),this.openDragLock=D2(f),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),mt(S=>{let p=this.getAxisMotionValue(S).get()||0;if(Ot.test(p)){const{projection:m}=this.visualElement;if(m&&m.layout){const y=m.layout.layoutBox[S];y&&(p=ot(y)*(parseFloat(p)/100))}}this.originPoint[S]=p}),x&&ce.postRender(()=>x(d,h)),Dc(this.visualElement,"transform");const{animationState:v}=this.visualElement;v&&v.setActive("whileDrag",!0)},a=(d,h)=>{const{dragPropagation:f,dragDirectionLock:g,onDirectionLock:x,onDrag:v}=this.getProps();if(!f&&!this.openDragLock)return;const{offset:S}=h;if(g&&this.currentDirection===null){this.currentDirection=$b(S),this.currentDirection!==null&&x&&x(this.currentDirection);return}this.updateAxis("x",h.point,S),this.updateAxis("y",h.point,S),this.visualElement.render(),v&&v(d,h)},l=(d,h)=>this.stop(d,h),c=()=>mt(d=>{var h;return this.getAnimationState(d)==="paused"&&((h=this.getAxisMotionValue(d).animation)===null||h===void 0?void 0:h.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new Ey(t,{onSessionStart:s,onStart:i,onMove:a,onSessionEnd:l,resumeAnimation:c},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:Vy(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:s}=n;this.startAnimation(s);const{onDragEnd:i}=this.getProps();i&&ce.postRender(()=>i(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:s}=this.getProps();if(!r||!qi(t,s,this.currentDirection))return;const i=this.getAxisMotionValue(t);let a=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(a=Eb(a,this.constraints[t],this.elastic[t])),i.set(a)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),s=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,i=this.constraints;n&&Ar(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&s?this.constraints=Pb(s.layoutBox,n):this.constraints=!1,this.elastic=Rb(r),i!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&mt(a=>{this.constraints!==!1&&this.getAxisMotionValue(a)&&(this.constraints[a]=Mb(s.layoutBox[a],this.constraints[a]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!Ar(t))return!1;const r=t.current,{projection:s}=this.visualElement;if(!s||!s.layout)return!1;const i=Ib(r,s.root,this.visualElement.getTransformPagePoint());let a=Ab(s.layout.layoutBox,i);if(n){const l=n(Lb(a));this.hasMutatedConstraints=!!l,l&&(a=My(l))}return a}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:s,dragTransition:i,dragSnapToOrigin:a,onDragTransitionEnd:l}=this.getProps(),c=this.constraints||{},u=mt(d=>{if(!qi(d,n,this.currentDirection))return;let h=c&&c[d]||{};a&&(h={min:0,max:0});const f=s?200:1e6,g=s?40:1e7,x={type:"inertia",velocity:r?t[d]:0,bounceStiffness:f,bounceDamping:g,timeConstant:750,restDelta:1,restSpeed:10,...i,...h};return this.startAxisValueAnimation(d,x)});return Promise.all(u).then(l)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return Dc(this.visualElement,t),r.start(Ed(t,r,0,n,this.visualElement,!1))}stopAnimation(){mt(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){mt(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n=`_drag${t.toUpperCase()}`,r=this.visualElement.getProps(),s=r[n];return s||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){mt(n=>{const{drag:r}=this.getProps();if(!qi(n,r,this.currentDirection))return;const{projection:s}=this.visualElement,i=this.getAxisMotionValue(n);if(s&&s.layout){const{min:a,max:l}=s.layout.layoutBox[n];i.set(t[n]-me(a,l,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!Ar(n)||!r||!this.constraints)return;this.stopAnimation();const s={x:0,y:0};mt(a=>{const l=this.getAxisMotionValue(a);if(l&&this.constraints!==!1){const c=l.get();s[a]=Db({min:c,max:c},this.constraints[a])}});const{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),mt(a=>{if(!qi(a,t,null))return;const l=this.getAxisMotionValue(a),{min:c,max:u}=this.constraints[a];l.set(me(c,u,s[a]))})}addListeners(){if(!this.visualElement.current)return;Ob.set(this.visualElement,this);const t=this.visualElement.current,n=Os(t,"pointerdown",c=>{const{drag:u,dragListener:d=!0}=this.getProps();u&&d&&this.start(c)}),r=()=>{const{dragConstraints:c}=this.getProps();Ar(c)&&c.current&&(this.constraints=this.resolveRefConstraints())},{projection:s}=this.visualElement,i=s.addEventListener("measure",r);s&&!s.layout&&(s.root&&s.root.updateScroll(),s.updateLayout()),ce.read(r);const a=di(window,"resize",()=>this.scalePositionWithinConstraints()),l=s.addEventListener("didUpdate",({delta:c,hasLayoutChanged:u})=>{this.isDragging&&u&&(mt(d=>{const h=this.getAxisMotionValue(d);h&&(this.originPoint[d]+=c[d].translate,h.set(h.get()+c[d].translate))}),this.visualElement.render())});return()=>{a(),n(),i(),l&&l()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:s=!1,dragConstraints:i=!1,dragElastic:a=zc,dragMomentum:l=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:s,dragConstraints:i,dragElastic:a,dragMomentum:l}}}function qi(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function $b(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class Ub extends Ln{constructor(t){super(t),this.removeGroupControls=it,this.removeListeners=it,this.controls=new zb(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||it}unmount(){this.removeGroupControls(),this.removeListeners()}}const Zh=e=>(t,n)=>{e&&ce.postRender(()=>e(t,n))};class Bb extends Ln{constructor(){super(...arguments),this.removePointerDownListener=it}onPointerDown(t){this.session=new Ey(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Vy(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:s}=this.node.getProps();return{onSessionStart:Zh(t),onStart:Zh(n),onMove:r,onEnd:(i,a)=>{delete this.session,s&&ce.postRender(()=>s(i,a))}}}mount(){this.removePointerDownListener=Os(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const ma={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Jh(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const ys={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(q.test(e))e=parseFloat(e);else return e;const n=Jh(e,t.target.x),r=Jh(e,t.target.y);return`${n}% ${r}%`}},Hb={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,s=An.parse(e);if(s.length>5)return r;const i=An.createTransformer(e),a=typeof s[0]!="number"?1:0,l=n.x.scale*t.x,c=n.y.scale*t.y;s[0+a]/=l,s[1+a]/=c;const u=me(l,c,.5);return typeof s[2+a]=="number"&&(s[2+a]/=u),typeof s[3+a]=="number"&&(s[3+a]/=u),i(s)}};class Wb extends w.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:s}=this.props,{projection:i}=t;d2(Kb),i&&(n.group&&n.group.add(i),r&&r.register&&s&&r.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),ma.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:s,isPresent:i}=this.props,a=r.projection;return a&&(a.isPresent=i,s||t.layoutDependency!==n||n===void 0?a.willUpdate():this.safeToRemove(),t.isPresent!==i&&(i?a.promote():a.relegate()||ce.postRender(()=>{const l=a.getStack();(!l||!l.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),sd.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(s),r&&r.deregister&&r.deregister(s))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Iy(e){const[t,n]=w0(),r=w.useContext(Xu);return o.jsx(Wb,{...e,layoutGroup:r,switchLayoutGroup:w.useContext(_0),isPresent:t,safeToRemove:n})}const Kb={borderRadius:{...ys,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ys,borderTopRightRadius:ys,borderBottomLeftRadius:ys,borderBottomRightRadius:ys,boxShadow:Hb};function Gb(e,t,n){const r=Oe(e)?e:ci(e);return r.start(Ed("",r,t,n)),r.animation}function Qb(e){return e instanceof SVGElement&&e.tagName!=="svg"}const qb=(e,t)=>e.depth-t.depth;class Xb{constructor(){this.children=[],this.isDirty=!1}add(t){xd(this.children,t),this.isDirty=!0}remove(t){vd(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(qb),this.isDirty=!1,this.children.forEach(t)}}function Yb(e,t){const n=zt.now(),r=({timestamp:s})=>{const i=s-n;i>=t&&(Pn(r),e(i-t))};return ce.read(r,!0),()=>Pn(r)}const Oy=["TopLeft","TopRight","BottomLeft","BottomRight"],Zb=Oy.length,em=e=>typeof e=="string"?parseFloat(e):e,tm=e=>typeof e=="number"||q.test(e);function Jb(e,t,n,r,s,i){s?(e.opacity=me(0,n.opacity!==void 0?n.opacity:1,ej(r)),e.opacityExit=me(t.opacity!==void 0?t.opacity:1,0,tj(r))):i&&(e.opacity=me(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let a=0;a<Zb;a++){const l=`border${Oy[a]}Radius`;let c=nm(t,l),u=nm(n,l);if(c===void 0&&u===void 0)continue;c||(c=0),u||(u=0),c===0||u===0||tm(c)===tm(u)?(e[l]=Math.max(me(em(c),em(u),r),0),(Ot.test(u)||Ot.test(c))&&(e[l]+="%")):e[l]=u}(t.rotate||n.rotate)&&(e.rotate=me(t.rotate||0,n.rotate||0,r))}function nm(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const ej=zy(0,.5,sy),tj=zy(.5,.95,it);function zy(e,t,n){return r=>r<e?0:r>t?1:n(qr(e,t,r))}function rm(e,t){e.min=t.min,e.max=t.max}function ht(e,t){rm(e.x,t.x),rm(e.y,t.y)}function sm(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function im(e,t,n,r,s){return e-=t,e=Ga(e,1/n,r),s!==void 0&&(e=Ga(e,1/s,r)),e}function nj(e,t=0,n=1,r=.5,s,i=e,a=e){if(Ot.test(t)&&(t=parseFloat(t),t=me(a.min,a.max,t/100)-a.min),typeof t!="number")return;let l=me(i.min,i.max,r);e===i&&(l-=t),e.min=im(e.min,t,n,l,s),e.max=im(e.max,t,n,l,s)}function am(e,t,[n,r,s],i,a){nj(e,t[n],t[r],t[s],t.scale,i,a)}const rj=["x","scaleX","originX"],sj=["y","scaleY","originY"];function om(e,t,n,r){am(e.x,t,rj,n?n.x:void 0,r?r.x:void 0),am(e.y,t,sj,n?n.y:void 0,r?r.y:void 0)}function lm(e){return e.translate===0&&e.scale===1}function $y(e){return lm(e.x)&&lm(e.y)}function cm(e,t){return e.min===t.min&&e.max===t.max}function ij(e,t){return cm(e.x,t.x)&&cm(e.y,t.y)}function um(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function Uy(e,t){return um(e.x,t.x)&&um(e.y,t.y)}function dm(e){return ot(e.x)/ot(e.y)}function fm(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class aj{constructor(){this.members=[]}add(t){xd(this.members,t),t.scheduleRender()}remove(t){if(vd(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(s=>t===s);if(n===0)return!1;let r;for(let s=n;s>=0;s--){const i=this.members[s];if(i.isPresent!==!1){r=i;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:s}=t.options;s===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function oj(e,t,n){let r="";const s=e.x.translate/t.x,i=e.y.translate/t.y,a=(n==null?void 0:n.z)||0;if((s||i||a)&&(r=`translate3d(${s}px, ${i}px, ${a}px) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{transformPerspective:u,rotate:d,rotateX:h,rotateY:f,skewX:g,skewY:x}=n;u&&(r=`perspective(${u}px) ${r}`),d&&(r+=`rotate(${d}deg) `),h&&(r+=`rotateX(${h}deg) `),f&&(r+=`rotateY(${f}deg) `),g&&(r+=`skewX(${g}deg) `),x&&(r+=`skewY(${x}deg) `)}const l=e.x.scale*t.x,c=e.y.scale*t.y;return(l!==1||c!==1)&&(r+=`scale(${l}, ${c})`),r||"none"}const Hn={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},Ts=typeof window<"u"&&window.MotionDebug!==void 0,jl=["","X","Y","Z"],lj={visibility:"hidden"},hm=1e3;let cj=0;function Nl(e,t,n,r){const{latestValues:s}=t;s[e]&&(n[e]=s[e],t.setStaticValue(e,0),r&&(r[e]=0))}function By(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const n=Y0(t);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:s,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",ce,!(s||i))}const{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&By(r)}function Hy({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:s}){return class{constructor(a={},l=t==null?void 0:t()){this.id=cj++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,Ts&&(Hn.totalNodes=Hn.resolvedTargetDeltas=Hn.recalculatedProjection=0),this.nodes.forEach(fj),this.nodes.forEach(yj),this.nodes.forEach(xj),this.nodes.forEach(hj),Ts&&window.MotionDebug.record(Hn)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=a,this.root=l?l.root||l:this,this.path=l?[...l.path,l]:[],this.parent=l,this.depth=l?l.depth+1:0;for(let c=0;c<this.path.length;c++)this.path[c].shouldResetTransform=!0;this.root===this&&(this.nodes=new Xb)}addEventListener(a,l){return this.eventHandlers.has(a)||this.eventHandlers.set(a,new wd),this.eventHandlers.get(a).add(l)}notifyListeners(a,...l){const c=this.eventHandlers.get(a);c&&c.notify(...l)}hasListeners(a){return this.eventHandlers.has(a)}mount(a,l=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=Qb(a),this.instance=a;const{layoutId:c,layout:u,visualElement:d}=this.options;if(d&&!d.current&&d.mount(a),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),l&&(u||c)&&(this.isLayoutDirty=!0),e){let h;const f=()=>this.root.updateBlockedByResize=!1;e(a,()=>{this.root.updateBlockedByResize=!0,h&&h(),h=Yb(f,250),ma.hasAnimatedSinceResize&&(ma.hasAnimatedSinceResize=!1,this.nodes.forEach(pm))})}c&&this.root.registerSharedNode(c,this),this.options.animate!==!1&&d&&(c||u)&&this.addEventListener("didUpdate",({delta:h,hasLayoutChanged:f,hasRelativeTargetChanged:g,layout:x})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const v=this.options.transition||d.getDefaultTransition()||jj,{onLayoutAnimationStart:S,onLayoutAnimationComplete:p}=d.getProps(),m=!this.targetLayout||!Uy(this.targetLayout,x)||g,y=!f&&g;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||y||f&&(m||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(h,y);const b={...md(v,"layout"),onPlay:S,onComplete:p};(d.shouldReduceMotion||this.options.layoutRoot)&&(b.delay=0,b.type=!1),this.startAnimation(b)}else f||pm(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=x})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const a=this.getStack();a&&a.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Pn(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(vj),this.animationId++)}getTransformTemplate(){const{visualElement:a}=this.options;return a&&a.getProps().transformTemplate}willUpdate(a=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&By(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let d=0;d<this.path.length;d++){const h=this.path[d];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:l,layout:c}=this.options;if(l===void 0&&!c)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),a&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(mm);return}this.isUpdating||this.nodes.forEach(pj),this.isUpdating=!1,this.nodes.forEach(gj),this.nodes.forEach(uj),this.nodes.forEach(dj),this.clearAllSnapshots();const l=zt.now();De.delta=on(0,1e3/60,l-De.timestamp),De.timestamp=l,De.isProcessing=!0,ml.update.process(De),ml.preRender.process(De),ml.render.process(De),De.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,sd.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(mj),this.sharedNodes.forEach(wj)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,ce.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){ce.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let c=0;c<this.path.length;c++)this.path[c].updateScroll();const a=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ve(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:l}=this.options;l&&l.notify("LayoutMeasure",this.layout.layoutBox,a?a.layoutBox:void 0)}updateScroll(a="measure"){let l=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===a&&(l=!1),l){const c=r(this.instance);this.scroll={animationId:this.root.animationId,phase:a,isRoot:c,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:c}}}resetTransform(){if(!s)return;const a=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,l=this.projectionDelta&&!$y(this.projectionDelta),c=this.getTransformTemplate(),u=c?c(this.latestValues,""):void 0,d=u!==this.prevTransformTemplateValue;a&&(l||Bn(this.latestValues)||d)&&(s(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(a=!0){const l=this.measurePageBox();let c=this.removeElementScroll(l);return a&&(c=this.removeTransform(c)),Nj(c),{animationId:this.root.animationId,measuredBox:l,layoutBox:c,latestValues:{},source:this.id}}measurePageBox(){var a;const{visualElement:l}=this.options;if(!l)return ve();const c=l.measureViewportBox();if(!(((a=this.scroll)===null||a===void 0?void 0:a.wasRoot)||this.path.some(Cj))){const{scroll:d}=this.root;d&&(Rr(c.x,d.offset.x),Rr(c.y,d.offset.y))}return c}removeElementScroll(a){var l;const c=ve();if(ht(c,a),!((l=this.scroll)===null||l===void 0)&&l.wasRoot)return c;for(let u=0;u<this.path.length;u++){const d=this.path[u],{scroll:h,options:f}=d;d!==this.root&&h&&f.layoutScroll&&(h.wasRoot&&ht(c,a),Rr(c.x,h.offset.x),Rr(c.y,h.offset.y))}return c}applyTransform(a,l=!1){const c=ve();ht(c,a);for(let u=0;u<this.path.length;u++){const d=this.path[u];!l&&d.options.layoutScroll&&d.scroll&&d!==d.root&&Lr(c,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),Bn(d.latestValues)&&Lr(c,d.latestValues)}return Bn(this.latestValues)&&Lr(c,this.latestValues),c}removeTransform(a){const l=ve();ht(l,a);for(let c=0;c<this.path.length;c++){const u=this.path[c];if(!u.instance||!Bn(u.latestValues))continue;$c(u.latestValues)&&u.updateSnapshot();const d=ve(),h=u.measurePageBox();ht(d,h),om(l,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,d)}return Bn(this.latestValues)&&om(l,this.latestValues),l}setTargetDelta(a){this.targetDelta=a,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(a){this.options={...this.options,...a,crossfade:a.crossfade!==void 0?a.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==De.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(a=!1){var l;const c=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=c.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=c.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=c.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==c;if(!(a||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((l=this.parent)===null||l===void 0)&&l.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:h,layoutId:f}=this.options;if(!(!this.layout||!(h||f))){if(this.resolvedRelativeTargetAt=De.timestamp,!this.targetDelta&&!this.relativeTarget){const g=this.getClosestProjectingParent();g&&g.layout&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ve(),this.relativeTargetOrigin=ve(),$s(this.relativeTargetOrigin,this.layout.layoutBox,g.layout.layoutBox),ht(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=ve(),this.targetWithTransforms=ve()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),_b(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):ht(this.target,this.layout.layoutBox),Ly(this.target,this.targetDelta)):ht(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const g=this.getClosestProjectingParent();g&&!!g.resumingFrom==!!this.resumingFrom&&!g.options.layoutScroll&&g.target&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ve(),this.relativeTargetOrigin=ve(),$s(this.relativeTargetOrigin,this.target,g.target),ht(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}Ts&&Hn.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||$c(this.parent.latestValues)||Ry(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var a;const l=this.getLead(),c=!!this.resumingFrom||this!==l;let u=!0;if((this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty)&&(u=!1),c&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===De.timestamp&&(u=!1),u)return;const{layout:d,layoutId:h}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(d||h))return;ht(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,g=this.treeScale.y;Vb(this.layoutCorrected,this.treeScale,this.path,c),l.layout&&!l.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(l.target=l.layout.layoutBox,l.targetWithTransforms=ve());const{target:x}=l;if(!x){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(sm(this.prevProjectionDelta.x,this.projectionDelta.x),sm(this.prevProjectionDelta.y,this.projectionDelta.y)),zs(this.projectionDelta,this.layoutCorrected,x,this.latestValues),(this.treeScale.x!==f||this.treeScale.y!==g||!fm(this.projectionDelta.x,this.prevProjectionDelta.x)||!fm(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",x)),Ts&&Hn.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(a=!0){var l;if((l=this.options.visualElement)===null||l===void 0||l.scheduleRender(),a){const c=this.getStack();c&&c.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=Mr(),this.projectionDelta=Mr(),this.projectionDeltaWithTransform=Mr()}setAnimationOrigin(a,l=!1){const c=this.snapshot,u=c?c.latestValues:{},d={...this.latestValues},h=Mr();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!l;const f=ve(),g=c?c.source:void 0,x=this.layout?this.layout.source:void 0,v=g!==x,S=this.getStack(),p=!S||S.members.length<=1,m=!!(v&&!p&&this.options.crossfade===!0&&!this.path.some(bj));this.animationProgress=0;let y;this.mixTargetDelta=b=>{const j=b/1e3;gm(h.x,a.x,j),gm(h.y,a.y,j),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&($s(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Sj(this.relativeTarget,this.relativeTargetOrigin,f,j),y&&ij(this.relativeTarget,y)&&(this.isProjectionDirty=!1),y||(y=ve()),ht(y,this.relativeTarget)),v&&(this.animationValues=d,Jb(d,u,this.latestValues,j,m,p)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=j},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(a){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Pn(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=ce.update(()=>{ma.hasAnimatedSinceResize=!0,this.currentAnimation=Gb(0,hm,{...a,onUpdate:l=>{this.mixTargetDelta(l),a.onUpdate&&a.onUpdate(l)},onComplete:()=>{a.onComplete&&a.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const a=this.getStack();a&&a.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(hm),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const a=this.getLead();let{targetWithTransforms:l,target:c,layout:u,latestValues:d}=a;if(!(!l||!c||!u)){if(this!==a&&this.layout&&u&&Wy(this.options.animationType,this.layout.layoutBox,u.layoutBox)){c=this.target||ve();const h=ot(this.layout.layoutBox.x);c.x.min=a.target.x.min,c.x.max=c.x.min+h;const f=ot(this.layout.layoutBox.y);c.y.min=a.target.y.min,c.y.max=c.y.min+f}ht(l,c),Lr(l,d),zs(this.projectionDeltaWithTransform,this.layoutCorrected,l,d)}}registerSharedNode(a,l){this.sharedNodes.has(a)||this.sharedNodes.set(a,new aj),this.sharedNodes.get(a).add(l);const u=l.options.initialPromotionConfig;l.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(l):void 0})}isLead(){const a=this.getStack();return a?a.lead===this:!0}getLead(){var a;const{layoutId:l}=this.options;return l?((a=this.getStack())===null||a===void 0?void 0:a.lead)||this:this}getPrevLead(){var a;const{layoutId:l}=this.options;return l?(a=this.getStack())===null||a===void 0?void 0:a.prevLead:void 0}getStack(){const{layoutId:a}=this.options;if(a)return this.root.sharedNodes.get(a)}promote({needsReset:a,transition:l,preserveFollowOpacity:c}={}){const u=this.getStack();u&&u.promote(this,c),a&&(this.projectionDelta=void 0,this.needsReset=!0),l&&this.setOptions({transition:l})}relegate(){const a=this.getStack();return a?a.relegate(this):!1}resetSkewAndRotation(){const{visualElement:a}=this.options;if(!a)return;let l=!1;const{latestValues:c}=a;if((c.z||c.rotate||c.rotateX||c.rotateY||c.rotateZ||c.skewX||c.skewY)&&(l=!0),!l)return;const u={};c.z&&Nl("z",a,u,this.animationValues);for(let d=0;d<jl.length;d++)Nl(`rotate${jl[d]}`,a,u,this.animationValues),Nl(`skew${jl[d]}`,a,u,this.animationValues);a.render();for(const d in u)a.setStaticValue(d,u[d]),this.animationValues&&(this.animationValues[d]=u[d]);a.scheduleRender()}getProjectionStyles(a){var l,c;if(!this.instance||this.isSVG)return;if(!this.isVisible)return lj;const u={visibility:""},d=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=fa(a==null?void 0:a.pointerEvents)||"",u.transform=d?d(this.latestValues,""):"none",u;const h=this.getLead();if(!this.projectionDelta||!this.layout||!h.target){const v={};return this.options.layoutId&&(v.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,v.pointerEvents=fa(a==null?void 0:a.pointerEvents)||""),this.hasProjected&&!Bn(this.latestValues)&&(v.transform=d?d({},""):"none",this.hasProjected=!1),v}const f=h.animationValues||h.latestValues;this.applyTransformsToTarget(),u.transform=oj(this.projectionDeltaWithTransform,this.treeScale,f),d&&(u.transform=d(f,u.transform));const{x:g,y:x}=this.projectionDelta;u.transformOrigin=`${g.origin*100}% ${x.origin*100}% 0`,h.animationValues?u.opacity=h===this?(c=(l=f.opacity)!==null&&l!==void 0?l:this.latestValues.opacity)!==null&&c!==void 0?c:1:this.preserveOpacity?this.latestValues.opacity:f.opacityExit:u.opacity=h===this?f.opacity!==void 0?f.opacity:"":f.opacityExit!==void 0?f.opacityExit:0;for(const v in Ua){if(f[v]===void 0)continue;const{correct:S,applyTo:p}=Ua[v],m=u.transform==="none"?f[v]:S(f[v],h);if(p){const y=p.length;for(let b=0;b<y;b++)u[p[b]]=m}else u[v]=m}return this.options.layoutId&&(u.pointerEvents=h===this?fa(a==null?void 0:a.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(a=>{var l;return(l=a.currentAnimation)===null||l===void 0?void 0:l.stop()}),this.root.nodes.forEach(mm),this.root.sharedNodes.clear()}}}function uj(e){e.updateLayout()}function dj(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:s}=e.layout,{animationType:i}=e.options,a=n.source!==e.layout.source;i==="size"?mt(h=>{const f=a?n.measuredBox[h]:n.layoutBox[h],g=ot(f);f.min=r[h].min,f.max=f.min+g}):Wy(i,n.layoutBox,r)&&mt(h=>{const f=a?n.measuredBox[h]:n.layoutBox[h],g=ot(r[h]);f.max=f.min+g,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[h].max=e.relativeTarget[h].min+g)});const l=Mr();zs(l,r,n.layoutBox);const c=Mr();a?zs(c,e.applyTransform(s,!0),n.measuredBox):zs(c,r,n.layoutBox);const u=!$y(l);let d=!1;if(!e.resumeFrom){const h=e.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:f,layout:g}=h;if(f&&g){const x=ve();$s(x,n.layoutBox,f.layoutBox);const v=ve();$s(v,r,g.layoutBox),Uy(x,v)||(d=!0),h.options.layoutRoot&&(e.relativeTarget=v,e.relativeTargetOrigin=x,e.relativeParent=h)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:c,layoutDelta:l,hasLayoutChanged:u,hasRelativeTargetChanged:d})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function fj(e){Ts&&Hn.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function hj(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function mj(e){e.clearSnapshot()}function mm(e){e.clearMeasurements()}function pj(e){e.isLayoutDirty=!1}function gj(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function pm(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function yj(e){e.resolveTargetDelta()}function xj(e){e.calcProjection()}function vj(e){e.resetSkewAndRotation()}function wj(e){e.removeLeadSnapshot()}function gm(e,t,n){e.translate=me(t.translate,0,n),e.scale=me(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function ym(e,t,n,r){e.min=me(t.min,n.min,r),e.max=me(t.max,n.max,r)}function Sj(e,t,n,r){ym(e.x,t.x,n.x,r),ym(e.y,t.y,n.y,r)}function bj(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const jj={duration:.45,ease:[.4,0,.1,1]},xm=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),vm=xm("applewebkit/")&&!xm("chrome/")?Math.round:it;function wm(e){e.min=vm(e.min),e.max=vm(e.max)}function Nj(e){wm(e.x),wm(e.y)}function Wy(e,t,n){return e==="position"||e==="preserve-aspect"&&!Tb(dm(t),dm(n),.2)}function Cj(e){var t;return e!==e.root&&((t=e.scroll)===null||t===void 0?void 0:t.wasRoot)}const kj=Hy({attachResizeListener:(e,t)=>di(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Cl={current:void 0},Ky=Hy({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Cl.current){const e=new kj({});e.mount(window),e.setOptions({layoutScroll:!0}),Cl.current=e}return Cl.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),Tj={pan:{Feature:Bb},drag:{Feature:Ub,ProjectionNode:Ky,MeasureLayout:Iy}};function Sm(e,t,n){const{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover",n==="Start");const s="onHover"+n,i=r[s];i&&ce.postRender(()=>i(t,ki(t)))}class _j extends Ln{mount(){const{current:t}=this.node;t&&(this.unmount=T2(t,n=>(Sm(this.node,n,"Start"),r=>Sm(this.node,r,"End"))))}unmount(){}}class Ej extends Ln{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Ci(di(this.node.current,"focus",()=>this.onFocus()),di(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function bm(e,t,n){const{props:r}=e;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap",n==="Start");const s="onTap"+(n==="End"?"":n),i=r[s];i&&ce.postRender(()=>i(t,ki(t)))}class Pj extends Ln{mount(){const{current:t}=this.node;t&&(this.unmount=A2(t,n=>(bm(this.node,n,"Start"),(r,{success:s})=>bm(this.node,r,s?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Bc=new WeakMap,kl=new WeakMap,Aj=e=>{const t=Bc.get(e.target);t&&t(e)},Dj=e=>{e.forEach(Aj)};function Mj({root:e,...t}){const n=e||document;kl.has(n)||kl.set(n,{});const r=kl.get(n),s=JSON.stringify(t);return r[s]||(r[s]=new IntersectionObserver(Dj,{root:e,...t})),r[s]}function Rj(e,t,n){const r=Mj(t);return Bc.set(e,n),r.observe(e),()=>{Bc.delete(e),r.unobserve(e)}}const Lj={some:0,all:1};class Fj extends Ln{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:s="some",once:i}=t,a={root:n?n.current:void 0,rootMargin:r,threshold:typeof s=="number"?s:Lj[s]},l=c=>{const{isIntersecting:u}=c;if(this.isInView===u||(this.isInView=u,i&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:d,onViewportLeave:h}=this.node.getProps(),f=u?d:h;f&&f(c)};return Rj(this.node.current,a,l)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(Vj(t,n))&&this.startObserver()}unmount(){}}function Vj({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const Ij={inView:{Feature:Fj},tap:{Feature:Pj},focus:{Feature:Ej},hover:{Feature:_j}},Oj={layout:{ProjectionNode:Ky,MeasureLayout:Iy}},Hc={current:null},Gy={current:!1};function zj(){if(Gy.current=!0,!!Ju)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Hc.current=e.matches;e.addListener(t),t()}else Hc.current=!1}const $j=[...xy,Ve,An],Uj=e=>$j.find(yy(e)),jm=new WeakMap;function Bj(e,t,n){for(const r in t){const s=t[r],i=n[r];if(Oe(s))e.addValue(r,s);else if(Oe(i))e.addValue(r,ci(s,{owner:e}));else if(i!==s)if(e.hasValue(r)){const a=e.getValue(r);a.liveStyle===!0?a.jump(s):a.hasAnimated||a.set(s)}else{const a=e.getStaticValue(r);e.addValue(r,ci(a!==void 0?a:s,{owner:e}))}}for(const r in n)t[r]===void 0&&e.removeValue(r);return t}const Nm=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Hj{scrapeMotionValuesFromProps(t,n,r){return{}}constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:s,blockInitialAnimation:i,visualState:a},l={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=kd,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const g=zt.now();this.renderScheduledAt<g&&(this.renderScheduledAt=g,ce.render(this.render,!1,!0))};const{latestValues:c,renderState:u,onUpdate:d}=a;this.onUpdate=d,this.latestValues=c,this.baseTarget={...c},this.initialValues=n.initial?{...c}:{},this.renderState=u,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=l,this.blockInitialAnimation=!!i,this.isControllingVariants=jo(n),this.isVariantNode=k0(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:h,...f}=this.scrapeMotionValuesFromProps(n,{},this);for(const g in f){const x=f[g];c[g]!==void 0&&Oe(x)&&x.set(c[g],!1)}}mount(t){this.current=t,jm.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),Gy.current||zj(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Hc.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){jm.delete(this.current),this.projection&&this.projection.unmount(),Pn(this.notifyUpdate),Pn(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const n=this.features[t];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(t,n){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const r=mr.has(t),s=n.on("change",l=>{this.latestValues[t]=l,this.props.onUpdate&&ce.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),i=n.on("renderRequest",this.scheduleRender);let a;window.MotionCheckAppearSync&&(a=window.MotionCheckAppearSync(this,t,n)),this.valueSubscriptions.set(t,()=>{s(),i(),a&&a(),n.owner&&n.stop()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}updateFeatures(){let t="animation";for(t in Xr){const n=Xr[t];if(!n)continue;const{isEnabled:r,Feature:s}=n;if(!this.features[t]&&s&&r(this.props)&&(this.features[t]=new s(this)),this.features[t]){const i=this.features[t];i.isMounted?i.update():(i.mount(),i.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ve()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<Nm.length;r++){const s=Nm[r];this.propEventSubscriptions[s]&&(this.propEventSubscriptions[s](),delete this.propEventSubscriptions[s]);const i="on"+s,a=t[i];a&&(this.propEventSubscriptions[s]=this.on(s,a))}this.prevMotionValues=Bj(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){const r=this.values.get(t);n!==r&&(r&&this.removeValue(t),this.bindToMotionValue(t,n),this.values.set(t,n),this.latestValues[t]=n.get())}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=ci(n===null?void 0:n,{owner:this}),this.addValue(t,r)),r}readValue(t,n){var r;let s=this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(r=this.getBaseTargetFromProps(this.props,t))!==null&&r!==void 0?r:this.readValueFromInstance(this.current,t,this.options);return s!=null&&(typeof s=="string"&&(py(s)||ay(s))?s=parseFloat(s):!Uj(s)&&An.test(n)&&(s=fy(t,n)),this.setBaseTarget(t,Oe(s)?s.get():s)),Oe(s)?s.get():s}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props;let s;if(typeof r=="string"||typeof r=="object"){const a=ad(this.props,r,(n=this.presenceContext)===null||n===void 0?void 0:n.custom);a&&(s=a[t])}if(r&&s!==void 0)return s;const i=this.getBaseTargetFromProps(this.props,t);return i!==void 0&&!Oe(i)?i:this.initialValues[t]!==void 0&&s===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new wd),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class Qy extends Hj{constructor(){super(...arguments),this.KeyframeResolver=vy}sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;Oe(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function Wj(e){return window.getComputedStyle(e)}class Kj extends Qy{constructor(){super(...arguments),this.type="html",this.renderInstance=L0}readValueFromInstance(t,n){if(mr.has(n)){const r=Cd(n);return r&&r.default||0}else{const r=Wj(t),s=(D0(n)?r.getPropertyValue(n):r[n])||0;return typeof s=="string"?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:n}){return Fy(t,n)}build(t,n,r){cd(t,n,r.transformTemplate)}scrapeMotionValuesFromProps(t,n,r){return hd(t,n,r)}}class Gj extends Qy{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=ve}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(mr.has(n)){const r=Cd(n);return r&&r.default||0}return n=F0.has(n)?n:rd(n),t.getAttribute(n)}scrapeMotionValuesFromProps(t,n,r){return O0(t,n,r)}build(t,n,r){ud(t,n,this.isSVGTag,r.transformTemplate)}renderInstance(t,n,r,s){V0(t,n,r,s)}mount(t){this.isSVGTag=fd(t.tagName),super.mount(t)}}const Qj=(e,t)=>id(e)?new Gj(t):new Kj(t,{allowProjection:e!==w.Fragment}),qj=w2({...xb,...Ij,...Tj,...Oj},Qj),E=L1(qj),Kn=class Kn{constructor(){Ht(this,"session",null);Ht(this,"user",null);Ht(this,"profile",null)}static getInstance(){return Kn.instance||(Kn.instance=new Kn),Kn.instance}async login(t){const n=t.username==="GOD"&&t.password==="123456";if(t.username==="GOD"&&t.password!=="123456")throw new Error("Invalid credentials");const r=n?"test-user-god":"mock-user-1",s={sessionId:"mock-session-"+Date.now(),userId:r,username:t.username,expiresAt:new Date(Date.now()+24*60*60*1e3)},i={id:r,username:t.username,password_hash:"",created_at:new Date,updated_at:new Date,is_active:!0,email_verified:!0},a={user_id:r,full_name:n?"God User (Test Account)":t.username,theme_preference:"frutiger-aero",notification_enabled:!0,timezone:"UTC",language_preference:"en",created_at:new Date,updated_at:new Date};return this.session=s,this.user=i,this.profile=a,localStorage.setItem("authSession",JSON.stringify(s)),s}async register(t){const n={id:"mock-user-"+Date.now(),username:t.username,password_hash:"",created_at:new Date,updated_at:new Date,is_active:!0,email_verified:!0},r={user_id:n.id,full_name:t.username,email:t.email,theme_preference:"frutiger-aero",notification_enabled:!0,timezone:"UTC",language_preference:"en",created_at:new Date,updated_at:new Date};return this.user=n,this.profile=r,{user:n,profile:r}}async logout(){this.session=null,this.user=null,this.profile=null,localStorage.removeItem("authSession")}async validateSession(){try{const t=localStorage.getItem("authSession");if(t){const n=JSON.parse(t);return new Date(n.expiresAt)>new Date?(this.session=n,!0):(localStorage.removeItem("authSession"),!1)}return!1}catch(t){return console.error("Session validation failed:",t),!1}}getSession(){return this.session}getUser(){return this.user}getProfile(){return this.profile}requiresMultiFactorAuth(){return!1}async verifyMFA(t){return this.session}async refreshSession(){return this.session?(this.session.expiresAt=new Date(Date.now()+24*60*60*1e3),localStorage.setItem("authSession",JSON.stringify(this.session)),this.session):null}async changePassword(t,n){console.log("Password changed (mock)")}async initialize(){await this.validateSession()}};Ht(Kn,"instance");let Wc=Kn;const fe=Wc.getInstance();/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xj=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),qy=(...e)=>e.filter((t,n,r)=>!!t&&r.indexOf(t)===n).join(" ");/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Yj={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zj=w.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:s="",children:i,iconNode:a,...l},c)=>w.createElement("svg",{ref:c,...Yj,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:qy("lucide",s),...l},[...a.map(([u,d])=>w.createElement(u,d)),...Array.isArray(i)?i:[i]]));/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const H=(e,t)=>{const n=w.forwardRef(({className:r,...s},i)=>w.createElement(Zj,{ref:i,iconNode:t,className:qy(`lucide-${Xj(e)}`,r),...s}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xy=H("Activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yy=H("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jj=H("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eN=H("ArrowUpDown",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zy=H("ArrowUp",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jy=H("Book",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ex=H("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fn=H("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tx=H("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tN=H("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zr=H("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rt=H("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fi=H("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qt=H("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xs=H("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jr=H("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nx=H("Coffee",[["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M14 2v2",key:"6buw04"}],["path",{d:"M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1",key:"pwadti"}],["path",{d:"M6 2v2",key:"colzsn"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nN=H("Command",[["path",{d:"M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3",key:"11bfej"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rN=H("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sN=H("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hi=H("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mi=H("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const iN=H("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rx=H("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tn=H("Folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cm=H("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const aN=H("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const km=H("Hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pd=H("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ad=H("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tm=H("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sx=H("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oN=H("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tr=H("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dd=H("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lN=H("Merge",[["path",{d:"m8 6 4-4 4 4",key:"ybng9g"}],["path",{d:"M12 2v10.3a4 4 0 0 1-1.172 2.872L4 22",key:"1hyw0i"}],["path",{d:"m20 22-5-5",key:"1m27yz"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cN=H("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uN=H("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ix=H("Music",[["path",{d:"M9 18V5l12-2v13",key:"1jmyc2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["circle",{cx:"18",cy:"16",r:"3",key:"1hluhg"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dN=H("OctagonAlert",[["path",{d:"M12 16h.01",key:"1drbdi"}],["path",{d:"M12 8v4",key:"1got3b"}],["path",{d:"M15.312 2a2 2 0 0 1 1.414.586l4.688 4.688A2 2 0 0 1 22 8.688v6.624a2 2 0 0 1-.586 1.414l-4.688 4.688a2 2 0 0 1-1.414.586H8.688a2 2 0 0 1-1.414-.586l-4.688-4.688A2 2 0 0 1 2 15.312V8.688a2 2 0 0 1 .586-1.414l4.688-4.688A2 2 0 0 1 8.688 2z",key:"1fd625"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fN=H("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hN=H("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ko=H("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lr=H("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mN=H("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Md=H("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kc=H("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ax=H("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pN=H("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ox=H("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gN=H("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yN=H("SquareCheckBig",[["path",{d:"M21 10.5V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h12.5",key:"1uzm8b"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _m=H("Square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rd=H("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xN=H("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pr=H("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lx=H("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cr=H("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vN=H("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $t=H("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gc=H("WifiOff",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wN=H("Wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const et=H("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const To=H("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),SN=({onClose:e,onVerify:t})=>{const[n,r]=w.useState(""),[s,i]=w.useState(!1),[a,l]=w.useState(null),[c,u]=w.useState("totp"),d=async f=>{f.preventDefault(),i(!0),l(null);try{await t(n),e()}catch(g){l(g instanceof Error?g.message:"Verification failed")}finally{i(!1)}},h=async()=>{i(!0),l(null);try{await new Promise(f=>setTimeout(f,1e3)),l("Code sent successfully")}catch{l("Failed to send code")}finally{i(!1)}};return o.jsx(E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",onClick:f=>f.target===f.currentTarget&&e(),children:o.jsxs(E.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},className:"fa-glass-panel-frosted rounded-2xl p-6 w-full max-w-md",children:[o.jsxs("div",{className:"flex justify-between items-center mb-6",children:[o.jsx("h2",{className:"fa-heading-2 text-gray-800",children:"Two-Factor Authentication"}),o.jsx("button",{onClick:e,className:"text-gray-500 hover:text-gray-700",children:o.jsx(et,{className:"w-6 h-6"})})]}),a&&o.jsx(E.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm",children:a}),o.jsxs("div",{className:"mb-6",children:[o.jsx("p",{className:"fa-body text-gray-600 mb-4",children:"Enter the verification code from your authenticator app"}),o.jsxs("div",{className:"flex space-x-2 mb-6",children:[o.jsxs("button",{onClick:()=>u("totp"),className:`flex-1 py-2 px-3 rounded-lg text-sm font-medium flex items-center justify-center ${c==="totp"?"bg-blue-100 text-blue-700 border border-blue-200":"bg-white bg-opacity-20 text-gray-700"}`,children:[o.jsx(gN,{className:"w-4 h-4 mr-2"}),"Authenticator App"]}),o.jsxs("button",{onClick:()=>u("email"),className:`flex-1 py-2 px-3 rounded-lg text-sm font-medium flex items-center justify-center ${c==="email"?"bg-blue-100 text-blue-700 border border-blue-200":"bg-white bg-opacity-20 text-gray-700"}`,children:[o.jsx(Dd,{className:"w-4 h-4 mr-2"}),"Email"]})]})]}),o.jsxs("form",{onSubmit:d,className:"space-y-6",children:[o.jsxs("div",{children:[o.jsx("label",{htmlFor:"code",className:"block text-sm font-medium text-gray-700 mb-2",children:"Verification Code"}),o.jsx("input",{id:"code",type:"text",value:n,onChange:f=>r(f.target.value),className:"fa-input w-full text-center text-lg tracking-widest",placeholder:"000000",maxLength:6,required:!0})]}),o.jsxs("div",{className:"flex space-x-3",children:[o.jsx(E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"button",onClick:h,disabled:s,className:"flex-1 fa-button-glass py-3 px-4 rounded-lg font-medium text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed",children:"Resend Code"}),o.jsx(E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:s||n.length!==6,className:"flex-1 fa-button-primary py-3 px-4 rounded-lg font-medium text-white disabled:opacity-50 disabled:cursor-not-allowed",children:s?o.jsxs("div",{className:"flex items-center justify-center",children:[o.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Verifying..."]}):"Verify"})]})]}),o.jsx("div",{className:"mt-6 pt-6 border-t border-gray-200",children:o.jsxs("div",{className:"flex items-center text-sm text-gray-500",children:[o.jsx(pN,{className:"w-4 h-4 mr-2"}),o.jsx("span",{children:"Two-factor authentication adds an extra layer of security to your account"})]})})]})})},cx=w.createContext(void 0),bN=({children:e})=>{const[t,n]=w.useState(null),[r,s]=w.useState(null),[i,a]=w.useState(null),[l,c]=w.useState(!1),[u,d]=w.useState(!1),[h,f]=w.useState(!1),[g,x]=w.useState(!0),[v,S]=w.useState(null);w.useEffect(()=>{(async()=>{try{x(!0),await fe.initialize();const P=fe.getSession(),D=fe.getUser(),V=fe.getProfile();n(P),s(D),a(V),c(!!P&&!!D),d(fe.requiresMultiFactorAuth())}catch(P){console.error("Auth initialization error:",P),S("Failed to initialize authentication")}finally{x(!1)}})()},[]);const p=async(C,P)=>{try{x(!0),S(null);const D={username:C,password:P},V=await fe.login(D),A=fe.getUser(),R=fe.getProfile();n(V),s(A),a(R),c(!0),d(!1)}catch(D){if(D instanceof Error&&D.message==="MFA_REQUIRED")d(!0),f(!0),S(null);else{const V=D instanceof Error?D.message:"Login failed";throw S(V),D}}finally{x(!1)}},m=async C=>{try{x(!0),S(null);const P=await fe.verifyMFA(C),D=fe.getUser(),V=fe.getProfile();n(P),s(D),a(V),c(!0),d(!1),f(!1)}catch(P){const D=P instanceof Error?P.message:"MFA verification failed";throw S(D),P}finally{x(!1)}},y=async(C,P,D,V)=>{try{x(!0),S(null);const A={username:C,password:P,email:D,fullName:V};await fe.register(A),await p(C,P)}catch(A){const R=A instanceof Error?A.message:"Registration failed";throw S(R),A}finally{x(!1)}},b=async()=>{try{x(!0),S(null),await fe.logout(),n(null),s(null),a(null),c(!1),d(!1),f(!1)}catch(C){const P=C instanceof Error?C.message:"Logout failed";S(P)}finally{x(!1)}},_={session:t,user:r,profile:i,isAuthenticated:l,requiresMFA:u,login:p,register:y,verifyMFA:m,logout:b,refreshSession:async()=>{try{x(!0),S(null);const C=await fe.refreshSession();C?(n(C),c(!0)):await b()}catch(C){const P=C instanceof Error?C.message:"Session refresh failed";S(P)}finally{x(!1)}},changePassword:async(C,P)=>{try{x(!0),S(null),await fe.changePassword(C,P)}catch(D){const V=D instanceof Error?D.message:"Password change failed";throw S(V),D}finally{x(!1)}},loading:g,error:v};return o.jsxs(cx.Provider,{value:_,children:[e,h&&o.jsx(SN,{onClose:()=>f(!1),onVerify:m})]})},ls=()=>{const e=w.useContext(cx);if(e===void 0)throw new Error("useAuth must be used within an AuthProvider");return e},jN=()=>{const{session:e,refreshSession:t,logout:n}=ls(),r=w.useCallback(async()=>{if(e){const s=new Date().getTime();if(new Date(e.expiresAt).getTime()-s<5*60*1e3)try{await t()}catch(a){console.error("Failed to refresh session:",a),await n()}}},[e,t,n]);return w.useEffect(()=>{r();const s=setInterval(r,60*1e3);return()=>clearInterval(s)},[r]),{session:e,checkSessionExpiry:r}},NN=w.createContext(void 0),CN=({children:e})=>{const[t,n]=w.useState(!1),[r,s]=w.useState(null);jN(),w.useEffect(()=>{(async()=>{try{if(window.electronAPI){const l=await window.electronAPI.system.getInfo();s(l)}n(!0)}catch(l){console.error("Failed to initialize application:",l),n(!0)}})()},[]);const i={isInitialized:t,electronAPI:window.electronAPI||null,systemInfo:r};return o.jsx(NN.Provider,{value:i,children:e})},ux=w.createContext(void 0),kN=()=>{const e=w.useContext(ux);if(!e)throw new Error("useTheme must be used within ThemeProvider");return e},TN=({children:e})=>{const[t,n]=w.useState("light");w.useEffect(()=>{const a=localStorage.getItem("fa-theme");if(a)n(a);else{const l=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";n(l)}},[]),w.useEffect(()=>{document.documentElement.setAttribute("data-theme",t),localStorage.setItem("fa-theme",t),t==="dark"?document.body.style.background=`linear-gradient(135deg, 
        rgba(30, 58, 138, 0.1) 0%, 
        rgba(37, 99, 235, 0.05) 50%, 
        rgba(59, 130, 246, 0.1) 100%)`:document.body.style.background=`linear-gradient(135deg, 
        rgba(74, 144, 226, 0.1) 0%, 
        rgba(126, 211, 33, 0.05) 50%, 
        rgba(135, 206, 235, 0.1) 100%)`},[t]);const i={theme:t,toggleTheme:()=>{n(a=>a==="light"?"dark":"light")},setTheme:a=>{n(a)}};return o.jsx(ux.Provider,{value:i,children:e})},_N=({onClose:e})=>{const{user:t,profile:n,changePassword:r}=ls(),[s,i]=w.useState(""),[a,l]=w.useState(""),[c,u]=w.useState(""),[d,h]=w.useState(!1),[f,g]=w.useState(null),[x,v]=w.useState(!1),S=async p=>{if(p.preventDefault(),a!==c){g({type:"error",text:"New passwords do not match"});return}if(a.length<12){g({type:"error",text:"Password must be at least 12 characters long"});return}v(!0),g(null);try{await r(s,a),g({type:"success",text:"Password changed successfully"}),i(""),l(""),u(""),h(!1)}catch(m){g({type:"error",text:m instanceof Error?m.message:"Failed to change password"})}finally{v(!1)}};return o.jsx(E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",onClick:p=>p.target===p.currentTarget&&e(),children:o.jsxs(E.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},className:"fa-glass-panel-frosted rounded-2xl p-6 w-full max-w-md",children:[o.jsxs("div",{className:"flex justify-between items-center mb-6",children:[o.jsx("h2",{className:"fa-heading-2 text-gray-800",children:"User Profile"}),o.jsx("button",{onClick:e,className:"text-gray-500 hover:text-gray-700",children:o.jsx(et,{className:"w-6 h-6"})})]}),f&&o.jsx(E.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:`mb-4 p-3 rounded-lg text-sm ${f.type==="success"?"bg-green-50 text-green-700 border border-green-200":"bg-red-50 text-red-700 border border-red-200"}`,children:f.text}),o.jsxs("div",{className:"space-y-6",children:[o.jsxs("div",{className:"flex flex-col items-center",children:[o.jsx("div",{className:"w-20 h-20 bg-gradient-to-br from-blue-400 to-aqua-400 rounded-full flex items-center justify-center mb-4",children:o.jsx($t,{className:"w-10 h-10 text-white"})}),o.jsx("h3",{className:"fa-heading-3 text-gray-800",children:t==null?void 0:t.username}),(n==null?void 0:n.full_name)&&o.jsx("p",{className:"fa-body text-gray-600",children:n.full_name})]}),o.jsxs("div",{className:"space-y-4",children:[o.jsxs("div",{className:"flex items-center p-3 bg-white bg-opacity-20 rounded-lg",children:[o.jsx($t,{className:"w-5 h-5 text-gray-500 mr-3"}),o.jsxs("div",{children:[o.jsx("p",{className:"fa-caption text-gray-500",children:"Username"}),o.jsx("p",{className:"fa-body text-gray-800",children:t==null?void 0:t.username})]})]}),(n==null?void 0:n.email)&&o.jsxs("div",{className:"flex items-center p-3 bg-white bg-opacity-20 rounded-lg",children:[o.jsx(Dd,{className:"w-5 h-5 text-gray-500 mr-3"}),o.jsxs("div",{children:[o.jsx("p",{className:"fa-caption text-gray-500",children:"Email"}),o.jsx("p",{className:"fa-body text-gray-800",children:n.email})]})]}),o.jsxs("div",{className:"flex items-center p-3 bg-white bg-opacity-20 rounded-lg",children:[o.jsx(Fn,{className:"w-5 h-5 text-gray-500 mr-3"}),o.jsxs("div",{children:[o.jsx("p",{className:"fa-caption text-gray-500",children:"Member since"}),o.jsx("p",{className:"fa-body text-gray-800",children:t!=null&&t.created_at?new Date(t.created_at).toLocaleDateString():"Unknown"})]})]})]}),o.jsxs("div",{className:"pt-4",children:[o.jsxs("button",{onClick:()=>h(!d),className:"w-full fa-button-glass py-3 px-4 rounded-lg font-medium text-gray-700 flex items-center justify-center",children:[o.jsx(tr,{className:"w-5 h-5 mr-2"}),d?"Cancel":"Change Password"]}),d&&o.jsxs(E.form,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},onSubmit:S,className:"mt-4 space-y-4",children:[o.jsxs("div",{children:[o.jsx("label",{htmlFor:"currentPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"Current Password"}),o.jsx("input",{id:"currentPassword",type:"password",value:s,onChange:p=>i(p.target.value),className:"fa-input w-full",required:!0})]}),o.jsxs("div",{children:[o.jsx("label",{htmlFor:"newPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"New Password"}),o.jsx("input",{id:"newPassword",type:"password",value:a,onChange:p=>l(p.target.value),className:"fa-input w-full",required:!0})]}),o.jsxs("div",{children:[o.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm New Password"}),o.jsx("input",{id:"confirmPassword",type:"password",value:c,onChange:p=>u(p.target.value),className:"fa-input w-full",required:!0})]}),o.jsx(E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:x,className:"w-full fa-button-primary py-3 px-4 rounded-lg font-medium text-white flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed",children:x?o.jsxs(o.Fragment,{children:[o.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Changing..."]}):o.jsxs(o.Fragment,{children:[o.jsx(Md,{className:"w-4 h-4 mr-2"}),"Save Changes"]})})]})]})]})]})})},EN=({systemInfo:e,onToggleTheme:t,theme:n})=>{const{user:r,logout:s}=ls(),[i,a]=w.useState(!1);return o.jsxs(o.Fragment,{children:[o.jsxs("div",{className:"w-full h-12 flex items-center justify-between px-4 bg-fa-white-glass backdrop-blur-xl border-b border-fa-white-frosted",children:[o.jsxs("div",{className:"flex items-center space-x-3",children:[o.jsx(E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"p-2 rounded-lg fa-hover-lift",onClick:t,children:n==="light"?o.jsx(xN,{className:"w-5 h-5 text-fa-blue-600"}):o.jsx(uN,{className:"w-5 h-5 text-fa-aqua-400"})}),o.jsx("h1",{className:"fa-heading-3 font-bold text-fa-gray-800",children:"Modern Todo"})]}),o.jsxs("div",{className:"flex items-center space-x-3",children:[r&&o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsxs(E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"p-2 rounded-lg fa-hover-lift flex items-center text-fa-gray-700",onClick:()=>a(!0),children:[o.jsx($t,{className:"w-5 h-5"}),o.jsx("span",{className:"ml-2 text-sm font-medium hidden md:inline",children:r.username})]}),o.jsx(E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"p-2 rounded-lg fa-hover-lift text-fa-gray-700",onClick:s,children:o.jsx("span",{className:"text-sm font-medium",children:"Logout"})})]}),o.jsxs("div",{className:"text-xs text-fa-gray-500",children:[e==null?void 0:e.platform," • ",e==null?void 0:e.version]})]})]}),i&&o.jsx(_N,{onClose:()=>a(!1)})]})},PN=()=>{const e=[{icon:Ad,label:"Dashboard",active:!0},{icon:sx,label:"All Tasks"},{icon:Fn,label:"Today"},{icon:tn,label:"Projects"},{icon:pr,label:"Tags"},{icon:Pd,label:"Favorites"},{icon:To,label:"Quick Add"}],t=[{name:"Personal",count:12},{name:"Work",count:8},{name:"Shopping",count:3},{name:"Health",count:5}];return o.jsxs("div",{className:"h-full fa-glass-panel rounded-r-2xl flex flex-col",children:[o.jsx("div",{className:"p-4 border-b border-fa-white-frosted",children:o.jsx("h2",{className:"fa-heading-3 font-bold text-fa-gray-800 mb-2",children:"Navigation"})}),o.jsxs("div",{className:"flex-1 overflow-y-auto p-2",children:[o.jsx("nav",{className:"space-y-1",children:e.map((n,r)=>{const s=n.icon;return o.jsxs(E.button,{whileHover:{x:4},whileTap:{scale:.98},className:`w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-left transition-all ${n.active?"bg-fa-white-frosted text-fa-blue-600 shadow-md":"text-fa-gray-600 hover:bg-fa-white-glass hover:text-fa-gray-800"}`,children:[o.jsx(s,{className:"w-5 h-5"}),o.jsx("span",{className:"font-medium",children:n.label})]},r)})}),o.jsxs("div",{className:"mt-8",children:[o.jsxs("div",{className:"flex items-center justify-between px-4 mb-3",children:[o.jsx("h3",{className:"fa-body font-semibold text-fa-gray-700",children:"Categories"}),o.jsx(E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"p-1 rounded-lg text-fa-blue-500 hover:bg-fa-white-glass",children:o.jsx(ko,{className:"w-4 h-4"})})]}),o.jsx("div",{className:"space-y-1",children:t.map((n,r)=>o.jsxs(E.button,{whileHover:{x:4},whileTap:{scale:.98},className:"w-full flex items-center justify-between px-4 py-2 rounded-lg text-left text-fa-gray-600 hover:bg-fa-white-glass hover:text-fa-gray-800",children:[o.jsx("span",{children:n.name}),o.jsx("span",{className:"text-xs bg-fa-white-glass px-2 py-1 rounded-full",children:n.count})]},r))})]})]}),o.jsx("div",{className:"p-4 border-t border-fa-white-frosted",children:o.jsxs(E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},className:"w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-fa-gray-600 hover:bg-fa-white-glass hover:text-fa-gray-800",children:[o.jsx(ax,{className:"w-5 h-5"}),o.jsx("span",{className:"font-medium",children:"Settings"})]})})]})},AN="modulepreload",DN=function(e,t){return new URL(e,t).href},Em={},MN=function(t,n,r){let s=Promise.resolve();if(n&&n.length>0){const a=document.getElementsByTagName("link"),l=document.querySelector("meta[property=csp-nonce]"),c=(l==null?void 0:l.nonce)||(l==null?void 0:l.getAttribute("nonce"));s=Promise.allSettled(n.map(u=>{if(u=DN(u,r),u in Em)return;Em[u]=!0;const d=u.endsWith(".css"),h=d?'[rel="stylesheet"]':"";if(!!r)for(let x=a.length-1;x>=0;x--){const v=a[x];if(v.href===u&&(!d||v.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${u}"]${h}`))return;const g=document.createElement("link");if(g.rel=d?"stylesheet":AN,d||(g.as="script"),g.crossOrigin="",g.href=u,c&&g.setAttribute("nonce",c),document.head.appendChild(g),d)return new Promise((x,v)=>{g.addEventListener("load",x),g.addEventListener("error",()=>v(new Error(`Unable to preload CSS for ${u}`)))})}))}function i(a){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=a,window.dispatchEvent(l),!l.defaultPrevented)throw a}return s.then(a=>{for(const l of a||[])l.status==="rejected"&&i(l.reason);return t().catch(i)})},RN={},Pm=e=>{let t;const n=new Set,r=(d,h)=>{const f=typeof d=="function"?d(t):d;if(!Object.is(f,t)){const g=t;t=h??(typeof f!="object"||f===null)?f:Object.assign({},t,f),n.forEach(x=>x(t,g))}},s=()=>t,c={setState:r,getState:s,getInitialState:()=>u,subscribe:d=>(n.add(d),()=>n.delete(d)),destroy:()=>{(RN?"production":void 0)!=="production"&&console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},u=t=e(r,s,c);return c},LN=e=>e?Pm(e):Pm;var dx={exports:{}},fx={},hx={exports:{}},mx={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var es=w;function FN(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var VN=typeof Object.is=="function"?Object.is:FN,IN=es.useState,ON=es.useEffect,zN=es.useLayoutEffect,$N=es.useDebugValue;function UN(e,t){var n=t(),r=IN({inst:{value:n,getSnapshot:t}}),s=r[0].inst,i=r[1];return zN(function(){s.value=n,s.getSnapshot=t,Tl(s)&&i({inst:s})},[e,n,t]),ON(function(){return Tl(s)&&i({inst:s}),e(function(){Tl(s)&&i({inst:s})})},[e]),$N(n),n}function Tl(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!VN(e,n)}catch{return!0}}function BN(e,t){return t()}var HN=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?BN:UN;mx.useSyncExternalStore=es.useSyncExternalStore!==void 0?es.useSyncExternalStore:HN;hx.exports=mx;var WN=hx.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var _o=w,KN=WN;function GN(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var QN=typeof Object.is=="function"?Object.is:GN,qN=KN.useSyncExternalStore,XN=_o.useRef,YN=_o.useEffect,ZN=_o.useMemo,JN=_o.useDebugValue;fx.useSyncExternalStoreWithSelector=function(e,t,n,r,s){var i=XN(null);if(i.current===null){var a={hasValue:!1,value:null};i.current=a}else a=i.current;i=ZN(function(){function c(g){if(!u){if(u=!0,d=g,g=r(g),s!==void 0&&a.hasValue){var x=a.value;if(s(x,g))return h=x}return h=g}if(x=h,QN(d,g))return x;var v=r(g);return s!==void 0&&s(x,v)?(d=g,x):(d=g,h=v)}var u=!1,d,h,f=n===void 0?null:n;return[function(){return c(t())},f===null?void 0:function(){return c(f())}]},[t,n,r,s]);var l=qN(e,i[0],i[1]);return YN(function(){a.hasValue=!0,a.value=l},[l]),JN(l),l};dx.exports=fx;var eC=dx.exports;const tC=op(eC),px={},{useDebugValue:nC}=Z,{useSyncExternalStoreWithSelector:rC}=tC;let Am=!1;const sC=e=>e;function iC(e,t=sC,n){(px?"production":void 0)!=="production"&&n&&!Am&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),Am=!0);const r=rC(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return nC(r),r}const aC=e=>{(px?"production":void 0)!=="production"&&typeof e!="function"&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const t=typeof e=="function"?LN(e):e,n=(r,s)=>iC(t,r,s);return Object.assign(n,t),n},Eo=e=>aC,oC={},lC=e=>(t,n,r)=>{const s=r.subscribe;return r.subscribe=(a,l,c)=>{let u=a;if(l){const d=(c==null?void 0:c.equalityFn)||Object.is;let h=a(r.getState());u=f=>{const g=a(f);if(!d(h,g)){const x=h;l(h=g,x)}},c!=null&&c.fireImmediately&&l(h,h)}return s(u)},e(t,n,r)},Po=lC;function cC(e,t){let n;try{n=e()}catch{return}return{getItem:s=>{var i;const a=c=>c===null?null:JSON.parse(c,void 0),l=(i=n.getItem(s))!=null?i:null;return l instanceof Promise?l.then(a):a(l)},setItem:(s,i)=>n.setItem(s,JSON.stringify(i,void 0)),removeItem:s=>n.removeItem(s)}}const pi=e=>t=>{try{const n=e(t);return n instanceof Promise?n:{then(r){return pi(r)(n)},catch(r){return this}}}catch(n){return{then(r){return this},catch(r){return pi(r)(n)}}}},uC=(e,t)=>(n,r,s)=>{let i={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:S=>S,version:0,merge:(S,p)=>({...p,...S}),...t},a=!1;const l=new Set,c=new Set;let u;try{u=i.getStorage()}catch{}if(!u)return e((...S)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),n(...S)},r,s);const d=pi(i.serialize),h=()=>{const S=i.partialize({...r()});let p;const m=d({state:S,version:i.version}).then(y=>u.setItem(i.name,y)).catch(y=>{p=y});if(p)throw p;return m},f=s.setState;s.setState=(S,p)=>{f(S,p),h()};const g=e((...S)=>{n(...S),h()},r,s);let x;const v=()=>{var S;if(!u)return;a=!1,l.forEach(m=>m(r()));const p=((S=i.onRehydrateStorage)==null?void 0:S.call(i,r()))||void 0;return pi(u.getItem.bind(u))(i.name).then(m=>{if(m)return i.deserialize(m)}).then(m=>{if(m)if(typeof m.version=="number"&&m.version!==i.version){if(i.migrate)return i.migrate(m.state,m.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return m.state}).then(m=>{var y;return x=i.merge(m,(y=r())!=null?y:g),n(x,!0),h()}).then(()=>{p==null||p(x,void 0),a=!0,c.forEach(m=>m(x))}).catch(m=>{p==null||p(void 0,m)})};return s.persist={setOptions:S=>{i={...i,...S},S.getStorage&&(u=S.getStorage())},clearStorage:()=>{u==null||u.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>v(),hasHydrated:()=>a,onHydrate:S=>(l.add(S),()=>{l.delete(S)}),onFinishHydration:S=>(c.add(S),()=>{c.delete(S)})},v(),x||g},dC=(e,t)=>(n,r,s)=>{let i={storage:cC(()=>localStorage),partialize:v=>v,version:0,merge:(v,S)=>({...S,...v}),...t},a=!1;const l=new Set,c=new Set;let u=i.storage;if(!u)return e((...v)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),n(...v)},r,s);const d=()=>{const v=i.partialize({...r()});return u.setItem(i.name,{state:v,version:i.version})},h=s.setState;s.setState=(v,S)=>{h(v,S),d()};const f=e((...v)=>{n(...v),d()},r,s);s.getInitialState=()=>f;let g;const x=()=>{var v,S;if(!u)return;a=!1,l.forEach(m=>{var y;return m((y=r())!=null?y:f)});const p=((S=i.onRehydrateStorage)==null?void 0:S.call(i,(v=r())!=null?v:f))||void 0;return pi(u.getItem.bind(u))(i.name).then(m=>{if(m)if(typeof m.version=="number"&&m.version!==i.version){if(i.migrate)return[!0,i.migrate(m.state,m.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,m.state];return[!1,void 0]}).then(m=>{var y;const[b,j]=m;if(g=i.merge(j,(y=r())!=null?y:f),n(g,!0),b)return d()}).then(()=>{p==null||p(g,void 0),g=r(),a=!0,c.forEach(m=>m(g))}).catch(m=>{p==null||p(void 0,m)})};return s.persist={setOptions:v=>{i={...i,...v},v.storage&&(u=v.storage)},clearStorage:()=>{u==null||u.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>x(),hasHydrated:()=>a,onHydrate:v=>(l.add(v),()=>{l.delete(v)}),onFinishHydration:v=>(c.add(v),()=>{c.delete(v)})},i.skipHydration||x(),g||f},fC=(e,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?((oC?"production":void 0)!=="production"&&console.warn("[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead."),uC(e,t)):dC(e,t),Ld=fC;var gx=Symbol.for("immer-nothing"),Dm=Symbol.for("immer-draftable"),lt=Symbol.for("immer-state");function kt(e,...t){throw new Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var ts=Object.getPrototypeOf;function ns(e){return!!e&&!!e[lt]}function ur(e){var t;return e?yx(e)||Array.isArray(e)||!!e[Dm]||!!((t=e.constructor)!=null&&t[Dm])||Do(e)||Mo(e):!1}var hC=Object.prototype.constructor.toString();function yx(e){if(!e||typeof e!="object")return!1;const t=ts(e);if(t===null)return!0;const n=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return n===Object?!0:typeof n=="function"&&Function.toString.call(n)===hC}function Qa(e,t){Ao(e)===0?Reflect.ownKeys(e).forEach(n=>{t(n,e[n],e)}):e.forEach((n,r)=>t(r,n,e))}function Ao(e){const t=e[lt];return t?t.type_:Array.isArray(e)?1:Do(e)?2:Mo(e)?3:0}function Qc(e,t){return Ao(e)===2?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function xx(e,t,n){const r=Ao(e);r===2?e.set(t,n):r===3?e.add(n):e[t]=n}function mC(e,t){return e===t?e!==0||1/e===1/t:e!==e&&t!==t}function Do(e){return e instanceof Map}function Mo(e){return e instanceof Set}function Wn(e){return e.copy_||e.base_}function qc(e,t){if(Do(e))return new Map(e);if(Mo(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);const n=yx(e);if(t===!0||t==="class_only"&&!n){const r=Object.getOwnPropertyDescriptors(e);delete r[lt];let s=Reflect.ownKeys(r);for(let i=0;i<s.length;i++){const a=s[i],l=r[a];l.writable===!1&&(l.writable=!0,l.configurable=!0),(l.get||l.set)&&(r[a]={configurable:!0,writable:!0,enumerable:l.enumerable,value:e[a]})}return Object.create(ts(e),r)}else{const r=ts(e);if(r!==null&&n)return{...e};const s=Object.create(r);return Object.assign(s,e)}}function Fd(e,t=!1){return Ro(e)||ns(e)||!ur(e)||(Ao(e)>1&&(e.set=e.add=e.clear=e.delete=pC),Object.freeze(e),t&&Object.entries(e).forEach(([n,r])=>Fd(r,!0))),e}function pC(){kt(2)}function Ro(e){return Object.isFrozen(e)}var gC={};function dr(e){const t=gC[e];return t||kt(0,e),t}var gi;function vx(){return gi}function yC(e,t){return{drafts_:[],parent_:e,immer_:t,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function Mm(e,t){t&&(dr("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function Xc(e){Yc(e),e.drafts_.forEach(xC),e.drafts_=null}function Yc(e){e===gi&&(gi=e.parent_)}function Rm(e){return gi=yC(gi,e)}function xC(e){const t=e[lt];t.type_===0||t.type_===1?t.revoke_():t.revoked_=!0}function Lm(e,t){t.unfinalizedDrafts_=t.drafts_.length;const n=t.drafts_[0];return e!==void 0&&e!==n?(n[lt].modified_&&(Xc(t),kt(4)),ur(e)&&(e=qa(t,e),t.parent_||Xa(t,e)),t.patches_&&dr("Patches").generateReplacementPatches_(n[lt].base_,e,t.patches_,t.inversePatches_)):e=qa(t,n,[]),Xc(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==gx?e:void 0}function qa(e,t,n){if(Ro(t))return t;const r=t[lt];if(!r)return Qa(t,(s,i)=>Fm(e,r,t,s,i,n)),t;if(r.scope_!==e)return t;if(!r.modified_)return Xa(e,r.base_,!0),r.base_;if(!r.finalized_){r.finalized_=!0,r.scope_.unfinalizedDrafts_--;const s=r.copy_;let i=s,a=!1;r.type_===3&&(i=new Set(s),s.clear(),a=!0),Qa(i,(l,c)=>Fm(e,r,s,l,c,n,a)),Xa(e,s,!1),n&&e.patches_&&dr("Patches").generatePatches_(r,n,e.patches_,e.inversePatches_)}return r.copy_}function Fm(e,t,n,r,s,i,a){if(ns(s)){const l=i&&t&&t.type_!==3&&!Qc(t.assigned_,r)?i.concat(r):void 0,c=qa(e,s,l);if(xx(n,r,c),ns(c))e.canAutoFreeze_=!1;else return}else a&&n.add(s);if(ur(s)&&!Ro(s)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;qa(e,s),(!t||!t.scope_.parent_)&&typeof r!="symbol"&&Object.prototype.propertyIsEnumerable.call(n,r)&&Xa(e,s)}}function Xa(e,t,n=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&Fd(t,n)}function vC(e,t){const n=Array.isArray(e),r={type_:n?1:0,scope_:t?t.scope_:vx(),modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1};let s=r,i=Vd;n&&(s=[r],i=yi);const{revoke:a,proxy:l}=Proxy.revocable(s,i);return r.draft_=l,r.revoke_=a,l}var Vd={get(e,t){if(t===lt)return e;const n=Wn(e);if(!Qc(n,t))return wC(e,n,t);const r=n[t];return e.finalized_||!ur(r)?r:r===_l(e.base_,t)?(El(e),e.copy_[t]=Jc(r,e)):r},has(e,t){return t in Wn(e)},ownKeys(e){return Reflect.ownKeys(Wn(e))},set(e,t,n){const r=wx(Wn(e),t);if(r!=null&&r.set)return r.set.call(e.draft_,n),!0;if(!e.modified_){const s=_l(Wn(e),t),i=s==null?void 0:s[lt];if(i&&i.base_===n)return e.copy_[t]=n,e.assigned_[t]=!1,!0;if(mC(n,s)&&(n!==void 0||Qc(e.base_,t)))return!0;El(e),Zc(e)}return e.copy_[t]===n&&(n!==void 0||t in e.copy_)||Number.isNaN(n)&&Number.isNaN(e.copy_[t])||(e.copy_[t]=n,e.assigned_[t]=!0),!0},deleteProperty(e,t){return _l(e.base_,t)!==void 0||t in e.base_?(e.assigned_[t]=!1,El(e),Zc(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0},getOwnPropertyDescriptor(e,t){const n=Wn(e),r=Reflect.getOwnPropertyDescriptor(n,t);return r&&{writable:!0,configurable:e.type_!==1||t!=="length",enumerable:r.enumerable,value:n[t]}},defineProperty(){kt(11)},getPrototypeOf(e){return ts(e.base_)},setPrototypeOf(){kt(12)}},yi={};Qa(Vd,(e,t)=>{yi[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}});yi.deleteProperty=function(e,t){return yi.set.call(this,e,t,void 0)};yi.set=function(e,t,n){return Vd.set.call(this,e[0],t,n,e[0])};function _l(e,t){const n=e[lt];return(n?Wn(n):e)[t]}function wC(e,t,n){var s;const r=wx(t,n);return r?"value"in r?r.value:(s=r.get)==null?void 0:s.call(e.draft_):void 0}function wx(e,t){if(!(t in e))return;let n=ts(e);for(;n;){const r=Object.getOwnPropertyDescriptor(n,t);if(r)return r;n=ts(n)}}function Zc(e){e.modified_||(e.modified_=!0,e.parent_&&Zc(e.parent_))}function El(e){e.copy_||(e.copy_=qc(e.base_,e.scope_.immer_.useStrictShallowCopy_))}var SC=class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(t,n,r)=>{if(typeof t=="function"&&typeof n!="function"){const i=n;n=t;const a=this;return function(c=i,...u){return a.produce(c,d=>n.call(this,d,...u))}}typeof n!="function"&&kt(6),r!==void 0&&typeof r!="function"&&kt(7);let s;if(ur(t)){const i=Rm(this),a=Jc(t,void 0);let l=!0;try{s=n(a),l=!1}finally{l?Xc(i):Yc(i)}return Mm(i,r),Lm(s,i)}else if(!t||typeof t!="object"){if(s=n(t),s===void 0&&(s=t),s===gx&&(s=void 0),this.autoFreeze_&&Fd(s,!0),r){const i=[],a=[];dr("Patches").generateReplacementPatches_(t,s,i,a),r(i,a)}return s}else kt(1,t)},this.produceWithPatches=(t,n)=>{if(typeof t=="function")return(a,...l)=>this.produceWithPatches(a,c=>t(c,...l));let r,s;return[this.produce(t,n,(a,l)=>{r=a,s=l}),r,s]},typeof(e==null?void 0:e.autoFreeze)=="boolean"&&this.setAutoFreeze(e.autoFreeze),typeof(e==null?void 0:e.useStrictShallowCopy)=="boolean"&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){ur(e)||kt(8),ns(e)&&(e=bC(e));const t=Rm(this),n=Jc(e,void 0);return n[lt].isManual_=!0,Yc(t),n}finishDraft(e,t){const n=e&&e[lt];(!n||!n.isManual_)&&kt(9);const{scope_:r}=n;return Mm(r,t),Lm(void 0,r)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let n;for(n=t.length-1;n>=0;n--){const s=t[n];if(s.path.length===0&&s.op==="replace"){e=s.value;break}}n>-1&&(t=t.slice(n+1));const r=dr("Patches").applyPatches_;return ns(e)?r(e,t):this.produce(e,s=>r(s,t))}};function Jc(e,t){const n=Do(e)?dr("MapSet").proxyMap_(e,t):Mo(e)?dr("MapSet").proxySet_(e,t):vC(e,t);return(t?t.scope_:vx()).drafts_.push(n),n}function bC(e){return ns(e)||kt(10,e),Sx(e)}function Sx(e){if(!ur(e)||Ro(e))return e;const t=e[lt];let n;if(t){if(!t.modified_)return t.base_;t.finalized_=!0,n=qc(e,t.scope_.immer_.useStrictShallowCopy_)}else n=qc(e,!0);return Qa(n,(r,s)=>{xx(n,r,Sx(s))}),t&&(t.finalized_=!1),n}var ct=new SC,jC=ct.produce;ct.produceWithPatches.bind(ct);ct.setAutoFreeze.bind(ct);ct.setUseStrictShallowCopy.bind(ct);ct.applyPatches.bind(ct);ct.createDraft.bind(ct);ct.finishDraft.bind(ct);const NC=e=>(t,n,r)=>(r.setState=(s,i,...a)=>{const l=typeof s=="function"?jC(s):s;return t(l,i,...a)},e(r.setState,n,r)),Lo=NC;class CC{constructor(){Ht(this,"sessionId",null);this.sessionId="mock-session-id"}setSessionId(t){this.sessionId=t}async makeRequest(t,...n){var s;if(!this.sessionId)throw new Error("No active session");if(!((s=window.electronAPI)!=null&&s.todos))throw new Error("Electron API not available");const r=await window.electronAPI.todos[t](this.sessionId,...n);if(!r.success)throw new Error(r.error||"Unknown error");return r.data}async getAllTodos(t,n){return this.makeRequest("getAll",t,n)}async createTodo(t){return this.makeRequest("create",t)}async updateTodo(t,n){return this.makeRequest("update",t,n)}async deleteTodo(t){return this.makeRequest("delete",t)}async updateTodoStatus(t,n){return this.makeRequest("updateStatus",t,n)}async toggleTodoCompletion(t,n){const r=n==="completed"?"pending":"completed";return this.updateTodoStatus(t,r)}isOverdue(t){return t.due_date?new Date(t.due_date)<new Date&&t.status!=="completed":!1}getPriorityWeight(t){return{very_low:1,low:2,medium:3,high:4,very_high:5}[t]||3}formatDueDate(t){const n=new Date(t),r=new Date,s=n.getTime()-r.getTime(),i=Math.ceil(s/(1e3*60*60*24));return i<0?`${Math.abs(i)} days overdue`:i===0?"Due today":i===1?"Due tomorrow":i<=7?`Due in ${i} days`:n.toLocaleDateString("en-US",{month:"short",day:"numeric",year:n.getFullYear()!==r.getFullYear()?"numeric":void 0})}}const Be=new CC;class kC{constructor(){Ht(this,"sessionId",null);this.sessionId="mock-session-id"}setSessionId(t){this.sessionId=t}async makeRequest(t,...n){var s;if(!this.sessionId)throw new Error("No active session");if(!((s=window.electronAPI)!=null&&s.categories))throw new Error("Electron API not available");const r=await window.electronAPI.categories[t](this.sessionId,...n);if(!r.success)throw new Error(r.error||"Unknown error");return r.data}async getAllCategories(){return this.makeRequest("getAll")}async createCategory(t){return this.makeRequest("create",t)}async updateCategory(t,n){return this.makeRequest("update",t,n)}async deleteCategory(t){return this.makeRequest("delete",t)}async reorderCategories(t){return this.makeRequest("reorder",t)}}const Xi=new kC;class TC{constructor(){Ht(this,"sessionId",null);this.sessionId="mock-session-id"}setSessionId(t){this.sessionId=t}async makeRequest(t,...n){var s;if(!this.sessionId)throw new Error("No active session");if(!((s=window.electronAPI)!=null&&s.tags))throw new Error("Electron API not available");const r=await window.electronAPI.tags[t](this.sessionId,...n);if(!r.success)throw new Error(r.error||"Unknown error");return r.data}async getAllTags(){return this.makeRequest("getAll")}async getTagSuggestions(t,n=10){return this.makeRequest("getSuggestions",t,n)}async getPopularTags(t=20){return this.makeRequest("getPopular",t)}async getRecentTags(t=10){return this.makeRequest("getRecent",t)}async getTagStats(t){return this.makeRequest("getStats",t)}async getSmartSuggestions(t="",n=10){try{if(t.trim())return await this.getTagSuggestions(t,n);{const[r,s]=await Promise.all([this.getPopularTags(Math.ceil(n*.7)),this.getRecentTags(Math.ceil(n*.3))]),i=[...r,...s];return Array.from(new Set(i)).slice(0,n)}}catch(r){return console.error("Error getting smart suggestions:",r),[]}}async getAutocompleteSuggestions(t,n=[],r=5){try{let s=[];return t.trim()?s=await this.getTagSuggestions(t,r*2):s=await this.getSmartSuggestions("",r*2),s.filter(a=>!n.includes(a)).slice(0,r)}catch(s){return console.error("Error getting autocomplete suggestions:",s),[]}}}const _s=new TC,Vm={status:[],priority:[],categoryId:[],tags:[],dueDateRange:{},searchQuery:"",sortBy:"position",sortOrder:"asc"},_C=()=>`temp_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,EC=(e,t)=>e.filter(n=>{if(t.status.length>0&&!t.status.includes(n.status)||t.priority.length>0&&!t.priority.includes(n.priority)||t.categoryId.length>0&&(!n.category_id||!t.categoryId.includes(n.category_id))||t.tags.length>0&&!t.tags.some(s=>n.tags.includes(s)))return!1;if(t.dueDateRange.start||t.dueDateRange.end){if(!n.due_date)return!1;const r=new Date(n.due_date);if(t.dueDateRange.start&&r<t.dueDateRange.start||t.dueDateRange.end&&r>t.dueDateRange.end)return!1}if(t.searchQuery){const r=t.searchQuery.toLowerCase();if(![n.title,n.description||"",...n.tags].join(" ").toLowerCase().includes(r))return!1}return!0}),PC=(e,t,n)=>[...e].sort((s,i)=>{let a,l;switch(t){case"position":a=s.position,l=i.position;break;case"created_at":a=new Date(s.created_at),l=new Date(i.created_at);break;case"updated_at":a=new Date(s.updated_at),l=new Date(i.updated_at);break;case"due_date":a=s.due_date?new Date(s.due_date):new Date("9999-12-31"),l=i.due_date?new Date(i.due_date):new Date("9999-12-31");break;case"priority":const c={very_low:1,low:2,medium:3,high:4,very_high:5};a=c[s.priority],l=c[i.priority];break;case"title":a=s.title.toLowerCase(),l=i.title.toLowerCase();break;default:return 0}return a<l?n==="asc"?-1:1:a>l?n==="asc"?1:-1:0}),Fo=Eo()(Po(Lo(Ld((e,t)=>({todos:[],categories:[],filters:Vm,isLoading:!1,error:null,selectedTodos:[],draggedTodo:null,totalCount:0,completedCount:0,pendingCount:0,loadTodos:async()=>{e(n=>{n.isLoading=!0,n.error=null});try{const r=await Be.getAll("current-session");e(s=>{var i,a,l;s.todos=r.todos||[],s.totalCount=((i=r.todos)==null?void 0:i.length)||0,s.completedCount=((a=r.todos)==null?void 0:a.filter(c=>c.status==="completed").length)||0,s.pendingCount=((l=r.todos)==null?void 0:l.filter(c=>c.status==="pending").length)||0,s.isLoading=!1})}catch(n){e(r=>{r.error=n instanceof Error?n.message:"Failed to load todos",r.isLoading=!1})}},createTodo:async n=>{const r=_C(),s={id:r,...n,created_at:new Date,updated_at:new Date,is_deleted:!1};e(i=>{i.todos.unshift(s),i.totalCount+=1,s.status==="pending"&&(i.pendingCount+=1)});try{const a=await Be.create("current-session",n);return e(l=>{const c=l.todos.findIndex(u=>u.id===r);c!==-1&&(l.todos[c]=a.todo)}),a.todo.id}catch(i){throw e(a=>{a.todos=a.todos.filter(l=>l.id!==r),a.totalCount-=1,s.status==="pending"&&(a.pendingCount-=1),a.error=i instanceof Error?i.message:"Failed to create todo"}),i}},updateTodo:async(n,r)=>{const s=t().todos.find(i=>i.id===n);if(s){e(i=>{const a=i.todos.findIndex(l=>l.id===n);a!==-1&&(i.todos[a]={...i.todos[a],...r,updated_at:new Date})});try{const a=await Be.update("current-session",n,r);e(l=>{const c=l.todos.findIndex(u=>u.id===n);c!==-1&&(l.todos[c]=a.todo)})}catch(i){throw e(a=>{const l=a.todos.findIndex(c=>c.id===n);l!==-1&&(a.todos[l]=s),a.error=i instanceof Error?i.message:"Failed to update todo"}),i}}},deleteTodo:async n=>{const r=t().todos.find(s=>s.id===n);if(r){e(s=>{s.todos=s.todos.filter(i=>i.id!==n),s.totalCount-=1,r.status==="completed"?s.completedCount-=1:r.status==="pending"&&(s.pendingCount-=1)});try{await Be.delete("current-session",n)}catch(s){throw e(i=>{i.todos.push(r),i.totalCount+=1,r.status==="completed"?i.completedCount+=1:r.status==="pending"&&(i.pendingCount+=1),i.error=s instanceof Error?s.message:"Failed to delete todo"}),s}}},toggleTodoComplete:async n=>{const r=t().todos.find(a=>a.id===n);if(!r)return;const s=r.status==="completed"?"pending":"completed",i={status:s,completed_at:s==="completed"?new Date:void 0};await t().updateTodo(n,i)},reorderTodos:async(n,r,s)=>{console.log("Reorder todos:",{sourceIndex:n,destinationIndex:r,categoryId:s})},createCategory:async n=>{e(r=>{r.isLoading=!0,r.error=null});try{const r=await Xi.createCategory(n);return e(s=>{s.categories.push(r),s.isLoading=!1}),r.id}catch(r){const s=r instanceof Error?r.message:"Failed to create category";throw e(i=>{i.error=s,i.isLoading=!1}),r}},updateCategory:async(n,r)=>{e(s=>{s.isLoading=!0,s.error=null});try{const s=await Xi.updateCategory(n,r);e(i=>{const a=i.categories.findIndex(l=>l.id===n);a!==-1&&(i.categories[a]=s),i.isLoading=!1})}catch(s){const i=s instanceof Error?s.message:"Failed to update category";throw e(a=>{a.error=i,a.isLoading=!1}),s}},deleteCategory:async n=>{e(r=>{r.isLoading=!0,r.error=null});try{await Xi.deleteCategory(n),e(r=>{r.categories=r.categories.filter(s=>s.id!==n),r.todos.forEach(s=>{s.category_id===n&&(s.category_id=void 0)}),r.isLoading=!1})}catch(r){const s=r instanceof Error?r.message:"Failed to delete category";throw e(i=>{i.error=s,i.isLoading=!1}),r}},loadCategories:async()=>{e(n=>{n.isLoading=!0,n.error=null});try{const n=await Xi.getAllCategories();e(r=>{r.categories=n,r.isLoading=!1})}catch(n){const r=n instanceof Error?n.message:"Failed to load categories";throw e(s=>{s.error=r,s.isLoading=!1}),n}},setFilters:n=>{e(r=>{r.filters={...r.filters,...n}})},clearFilters:()=>{e(n=>{n.filters=Vm})},setSearchQuery:n=>{e(r=>{r.filters.searchQuery=n})},setSortConfig:(n,r)=>{e(s=>{s.filters.sortBy=n,s.filters.sortOrder=r})},getAllTags:()=>{const{todos:n}=t(),r=new Set;return n.forEach(s=>{s.tags.forEach(i=>r.add(i))}),Array.from(r).sort()},getTagsWithStats:async()=>{try{return await _s.getAllTags()}catch(n){return console.error("Error getting tags with stats:",n),[]}},getTagSuggestions:async(n,r)=>{try{return await _s.getTagSuggestions(n,r)}catch(s){return console.error("Error getting tag suggestions:",s),[]}},getPopularTags:async n=>{try{return await _s.getPopularTags(n)}catch(r){return console.error("Error getting popular tags:",r),[]}},getRecentTags:async n=>{try{return await _s.getRecentTags(n)}catch(r){return console.error("Error getting recent tags:",r),[]}},bulkUpdateTags:async(n,r)=>{const{todos:s}=t(),i=s.filter(a=>a.tags.includes(n));e(a=>{a.isLoading=!0,a.error=null});try{await Promise.all(i.map(a=>{const l=a.tags.map(c=>c===n?r:c);return Be.updateTodo(a.id,{tags:l})})),e(a=>{a.todos.forEach(l=>{l.tags.includes(n)&&(l.tags=l.tags.map(c=>c===n?r:c))}),a.isLoading=!1})}catch(a){const l=a instanceof Error?a.message:"Failed to update tags";throw e(c=>{c.error=l,c.isLoading=!1}),a}},deleteTag:async n=>{const{todos:r}=t(),s=r.filter(i=>i.tags.includes(n));e(i=>{i.isLoading=!0,i.error=null});try{await Promise.all(s.map(i=>{const a=i.tags.filter(l=>l!==n);return Be.updateTodo(i.id,{tags:a})})),e(i=>{i.todos.forEach(a=>{a.tags=a.tags.filter(l=>l!==n)}),i.filters.tags=i.filters.tags.filter(a=>a!==n),i.isLoading=!1})}catch(i){const a=i instanceof Error?i.message:"Failed to delete tag";throw e(l=>{l.error=a,l.isLoading=!1}),i}},selectTodo:n=>{e(r=>{r.selectedTodos.includes(n)||r.selectedTodos.push(n)})},deselectTodo:n=>{e(r=>{r.selectedTodos=r.selectedTodos.filter(s=>s!==n)})},selectAllTodos:()=>{e(n=>{const r=t().getFilteredTodos();n.selectedTodos=r.map(s=>s.id)})},clearSelection:()=>{e(n=>{n.selectedTodos=[]})},toggleTodoSelection:n=>{const{selectedTodos:r}=t();r.includes(n)?t().deselectTodo(n):t().selectTodo(n)},setDraggedTodo:n=>{e(r=>{r.draggedTodo=n})},getFilteredTodos:()=>{const{todos:n,filters:r}=t(),s=EC(n,r);return PC(s,r.sortBy,r.sortOrder)},getTodosByCategory:n=>{const{todos:r}=t();return r.filter(s=>s.category_id===n)},getCompletedTodos:()=>{const{todos:n}=t();return n.filter(r=>r.status==="completed")},getPendingTodos:()=>{const{todos:n}=t();return n.filter(r=>r.status==="pending")},getOverdueTodos:()=>{const{todos:n}=t(),r=new Date;return n.filter(s=>s.due_date&&new Date(s.due_date)<r&&s.status!=="completed")},getTodoStats:()=>{const{todos:n}=t(),r=new Date,s=n.filter(i=>i.due_date&&new Date(i.due_date)<r&&i.status!=="completed").length;return{total:n.length,completed:n.filter(i=>i.status==="completed").length,pending:n.filter(i=>i.status==="pending").length,overdue:s}}}),{name:"todo-store",partialize:e=>({filters:e.filters,selectedTodos:e.selectedTodos})}))));Fo.subscribe(e=>e.todos,e=>{MN(async()=>{const{overdueNotificationService:t}=await import("./overdueNotification.service-C35WopeJ.js");return{overdueNotificationService:t}},[],import.meta.url).then(({overdueNotificationService:t})=>{t.triggerCheck()}).catch(t=>{console.error("Error triggering overdue check:",t)})});const Im=e=>e?new Date>=e:!0,AC=e=>{if(!e)return 0;const t=new Date,n=e.getTime()-t.getTime();return Math.max(0,n)};Eo()(Po(Lo(Ld((e,t)=>({user:null,profile:null,session:null,isAuthenticated:!1,isLoading:!1,error:null,sessionId:null,sessionExpiry:null,lastActivity:null,login:async(n,r)=>{e(s=>{s.isLoading=!0,s.error=null});try{const s=await fe.login(n,r);e(i=>{i.user=s.user,i.session=s.session,i.sessionId=s.session.session_id,i.sessionExpiry=new Date(s.session.expires_at),i.lastActivity=new Date,i.isAuthenticated=!0,i.isLoading=!1}),await t().loadProfile()}catch(s){throw e(i=>{i.error=s instanceof Error?s.message:"Login failed",i.isLoading=!1,i.isAuthenticated=!1}),s}},logout:async()=>{e(n=>{n.isLoading=!0});try{const{sessionId:n}=t();n&&await fe.logout(n)}catch(n){console.error("Logout error:",n)}finally{e(n=>{n.user=null,n.profile=null,n.session=null,n.sessionId=null,n.sessionExpiry=null,n.lastActivity=null,n.isAuthenticated=!1,n.isLoading=!1,n.error=null})}},register:async n=>{e(r=>{r.isLoading=!0,r.error=null});try{const r=await fe.register(n);e(s=>{s.user=r.user,s.session=r.session,s.sessionId=r.session.session_id,s.sessionExpiry=new Date(r.session.expires_at),s.lastActivity=new Date,s.isAuthenticated=!0,s.isLoading=!1}),await t().loadProfile()}catch(r){throw e(s=>{s.error=r instanceof Error?r.message:"Registration failed",s.isLoading=!1,s.isAuthenticated=!1}),r}},refreshSession:async()=>{const{sessionId:n}=t();if(!n)throw new Error("No active session to refresh");e(r=>{r.isLoading=!0});try{const r=await fe.refreshSession(n);e(s=>{s.session=r.session,s.sessionExpiry=new Date(r.session.expires_at),s.lastActivity=new Date,s.isLoading=!1})}catch(r){throw e(s=>{s.error=r instanceof Error?r.message:"Session refresh failed",s.isLoading=!1}),await t().logout(),r}},updateProfile:async n=>{const{user:r}=t();if(!r)throw new Error("No authenticated user");e(s=>{s.isLoading=!0,s.error=null});try{const s=await fe.updateProfile(r.id,n);e(i=>{i.profile=s.profile,i.isLoading=!1})}catch(s){throw e(i=>{i.error=s instanceof Error?s.message:"Profile update failed",i.isLoading=!1}),s}},loadProfile:async()=>{const{user:n}=t();if(n)try{const r=await fe.getProfile(n.id);e(s=>{s.profile=r})}catch(r){console.error("Failed to load profile:",r)}},validateSession:async()=>{const{sessionId:n,sessionExpiry:r}=t();if(!n||!r)return!1;if(Im(r))return await t().logout(),!1;try{return await fe.validateSession(n)?(e(i=>{i.lastActivity=new Date}),!0):(await t().logout(),!1)}catch(s){return console.error("Session validation error:",s),await t().logout(),!1}},extendSession:async()=>{const{sessionId:n}=t();if(n)try{const r=await fe.extendSession(n);e(s=>{s.sessionExpiry=new Date(r.expiresAt),s.lastActivity=new Date})}catch(r){console.error("Failed to extend session:",r)}},clearSession:()=>{e(n=>{n.user=null,n.profile=null,n.session=null,n.sessionId=null,n.sessionExpiry=null,n.lastActivity=null,n.isAuthenticated=!1,n.error=null})},clearError:()=>{e(n=>{n.error=null})},setError:n=>{e(r=>{r.error=n})},isSessionValid:()=>{const{sessionExpiry:n}=t();return!Im(n)},getSessionTimeRemaining:()=>{const{sessionExpiry:n}=t();return AC(n)},getUserDisplayName:()=>{const{user:n,profile:r}=t();return r!=null&&r.full_name?r.full_name:n!=null&&n.username?n.username:"User"}}),{name:"auth-store",partialize:e=>({user:e.user,profile:e.profile,session:e.session,sessionId:e.sessionId,sessionExpiry:e.sessionExpiry,lastActivity:e.lastActivity,isAuthenticated:e.isAuthenticated})}))));const DC=()=>`notification_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,MC=e=>{const t=["light","dark","frutiger-aero"],n=t.indexOf(e);return t[(n+1)%t.length]},RC=Eo()(Po(Lo(Ld((e,t)=>({theme:"frutiger-aero",sidebarOpen:!0,sidebarWidth:280,activeModal:null,modalData:null,notifications:[],globalLoading:!1,loadingStates:{},currentView:"todos",previousView:null,todoViewMode:"list",showCompletedTodos:!0,autoSaveEnabled:!0,compactMode:!1,showNotifications:!0,soundEnabled:!0,setTheme:n=>{e(r=>{r.theme=n}),document.documentElement.setAttribute("data-theme",n),localStorage.setItem("fa-theme",n)},toggleTheme:()=>{const n=t().theme,r=MC(n);t().setTheme(r)},initializeTheme:()=>{try{const n=localStorage.getItem("fa-theme");if(n&&["light","dark","frutiger-aero"].includes(n))t().setTheme(n);else{const r=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";t().setTheme(r)}}catch(n){console.error("Error initializing theme:",n),t().setTheme("light")}},toggleSidebar:()=>{e(n=>{n.sidebarOpen=!n.sidebarOpen})},setSidebarOpen:n=>{e(r=>{r.sidebarOpen=n})},setSidebarWidth:n=>{e(r=>{r.sidebarWidth=Math.max(200,Math.min(400,n))})},openModal:(n,r=null)=>{e(s=>{s.activeModal=n,s.modalData=r})},closeModal:()=>{e(n=>{n.activeModal=null,n.modalData=null})},addNotification:n=>{const r=DC(),s={id:r,...n,createdAt:new Date};return e(i=>{i.notifications.push(s)}),s.duration&&s.duration>0&&setTimeout(()=>{t().removeNotification(r)},s.duration),r},removeNotification:n=>{e(r=>{r.notifications=r.notifications.filter(s=>s.id!==n)})},clearNotifications:()=>{e(n=>{n.notifications=[]})},setGlobalLoading:n=>{e(r=>{r.globalLoading=n})},setLoadingState:(n,r)=>{e(s=>{r?s.loadingStates[n]=!0:delete s.loadingStates[n]})},clearLoadingState:n=>{e(r=>{delete r.loadingStates[n]})},setCurrentView:n=>{e(r=>{r.previousView=r.currentView,r.currentView=n})},setTodoViewMode:n=>{e(r=>{r.todoViewMode=n})},toggleCompletedTodos:()=>{e(n=>{n.showCompletedTodos=!n.showCompletedTodos})},setAutoSave:n=>{e(r=>{r.autoSaveEnabled=n})},setCompactMode:n=>{e(r=>{r.compactMode=n})},setShowNotifications:n=>{e(r=>{r.showNotifications=n})},setSoundEnabled:n=>{e(r=>{r.soundEnabled=n})},getActiveNotifications:()=>{const{notifications:n,showNotifications:r}=t();return r?n:[]},isLoading:n=>{const{globalLoading:r,loadingStates:s}=t();return n?s[n]||!1:r||Object.keys(s).length>0}}),{name:"ui-store",partialize:e=>({theme:e.theme,sidebarOpen:e.sidebarOpen,sidebarWidth:e.sidebarWidth,currentView:e.currentView,todoViewMode:e.todoViewMode,showCompletedTodos:e.showCompletedTodos,autoSaveEnabled:e.autoSaveEnabled,compactMode:e.compactMode,showNotifications:e.showNotifications,soundEnabled:e.soundEnabled})})))),Id=Eo()(Po(Lo((e,t)=>({syncStatus:{status:"idle",lastSyncAt:null,pendingChanges:0},queueStatus:{totalOperations:0,pendingOperations:0,failedOperations:0,conflictOperations:0,isOnline:!1,isSyncing:!1},networkStatus:null,conflicts:[],activeConflict:null,showSyncPanel:!1,showConflictDialog:!1,autoSyncEnabled:!0,syncInterval:3e4,isLoadingStatus:!1,isLoadingConflicts:!1,isResolvingConflict:!1,loadSyncStatus:async()=>{var n;e(r=>{r.isLoadingStatus=!0});try{if((n=window.electronAPI)!=null&&n.sync){const r=await window.electronAPI.sync.getStatus();r.success&&e(s=>{s.syncStatus={...r.data,lastSyncAt:r.data.lastSyncAt?new Date(r.data.lastSyncAt):null}})}}catch(r){console.error("Failed to load sync status:",r)}finally{e(r=>{r.isLoadingStatus=!1})}},loadQueueStatus:async()=>{var n;try{if((n=window.electronAPI)!=null&&n.sync){const r=await window.electronAPI.sync.getQueueStatus();r.success&&e(s=>{s.queueStatus=r.data})}}catch(r){console.error("Failed to load queue status:",r)}},loadNetworkStatus:async()=>{var n;try{if((n=window.electronAPI)!=null&&n.sync){const r=await window.electronAPI.sync.getNetworkStatus();r.success&&e(s=>{s.networkStatus={...r.data,lastChecked:new Date(r.data.lastChecked)}})}}catch(r){console.error("Failed to load network status:",r)}},loadConflicts:async()=>{var n;e(r=>{r.isLoadingConflicts=!0});try{if((n=window.electronAPI)!=null&&n.sync){const r=await window.electronAPI.sync.getConflicts();r.success&&e(s=>{s.conflicts=r.data.map(i=>({...i,createdAt:new Date(i.created_at),resolved:!1}))})}}catch(r){console.error("Failed to load conflicts:",r)}finally{e(r=>{r.isLoadingConflicts=!1})}},performSync:async()=>{var n;try{if((n=window.electronAPI)!=null&&n.sync){const r=await window.electronAPI.sync.performSync();if(r.success)return await t().loadSyncStatus(),await t().loadQueueStatus(),await t().loadConflicts(),r.data;throw new Error(r.error||"Sync failed")}throw new Error("Sync API not available")}catch(r){throw console.error("Sync failed:",r),r}},forcePushChanges:async()=>{var n;try{if((n=window.electronAPI)!=null&&n.sync){const r=await window.electronAPI.sync.forcePush();if(r.success)return await t().refreshAll(),r.data;throw new Error(r.error||"Force push failed")}throw new Error("Sync API not available")}catch(r){throw console.error("Force push failed:",r),r}},forcePullChanges:async()=>{var n;try{if((n=window.electronAPI)!=null&&n.sync){const r=await window.electronAPI.sync.forcePull();if(r.success)return await t().refreshAll(),r.data;throw new Error(r.error||"Force pull failed")}throw new Error("Sync API not available")}catch(r){throw console.error("Force pull failed:",r),r}},resolveConflict:async(n,r,s)=>{var i;e(a=>{a.isResolvingConflict=!0});try{if((i=window.electronAPI)!=null&&i.sync){const a=await window.electronAPI.sync.resolveConflict(n,r,s);if(a.success)e(l=>{var c;l.conflicts=l.conflicts.filter(u=>u.id!==n),((c=l.activeConflict)==null?void 0:c.id)===n&&(l.activeConflict=null,l.showConflictDialog=!1)}),await t().loadSyncStatus(),await t().loadQueueStatus();else throw new Error(a.error||"Failed to resolve conflict")}}catch(a){throw console.error("Failed to resolve conflict:",a),a}finally{e(a=>{a.isResolvingConflict=!1})}},resolveConflictManually:async(n,r)=>{await t().resolveConflict(n,"merge",r)},clearSyncQueue:async()=>{var n;try{(n=window.electronAPI)!=null&&n.sync&&(await window.electronAPI.sync.clearQueue()).success&&await t().loadQueueStatus()}catch(r){throw console.error("Failed to clear sync queue:",r),r}},toggleSyncPanel:()=>{e(n=>{n.showSyncPanel=!n.showSyncPanel})},showConflictResolution:n=>{e(r=>{r.activeConflict=n,r.showConflictDialog=!0})},hideConflictResolution:()=>{e(n=>{n.activeConflict=null,n.showConflictDialog=!1})},setAutoSync:n=>{e(r=>{r.autoSyncEnabled=n})},setSyncInterval:n=>{e(r=>{r.syncInterval=n})},refreshAll:async()=>{await Promise.all([t().loadSyncStatus(),t().loadQueueStatus(),t().loadNetworkStatus(),t().loadConflicts()])},clearErrors:()=>{e(n=>{n.syncStatus.errorMessage=void 0})}})))),LC=(e=!0)=>{const t=Id();return Z.useEffect(()=>{if(e){t.refreshAll();const n=setInterval(()=>{t.loadSyncStatus(),t.loadQueueStatus(),t.loadNetworkStatus()},5e3);return()=>clearInterval(n)}},[e,t]),t},bx=()=>{const e=Id();return{conflicts:e.conflicts,activeConflict:e.activeConflict,showConflictDialog:e.showConflictDialog,isResolvingConflict:e.isResolvingConflict,showConflictResolution:e.showConflictResolution,hideConflictResolution:e.hideConflictResolution,resolveConflict:e.resolveConflict,resolveConflictManually:e.resolveConflictManually,loadConflicts:e.loadConflicts}},FC=()=>{try{const e=useUIStore.getState();if(e&&e.initializeTheme)e.initializeTheme();else{const t=localStorage.getItem("fa-theme");t?document.documentElement.setAttribute("data-theme",t):document.documentElement.setAttribute("data-theme","light")}console.log("Stores initialized successfully")}catch(e){console.error("Error initializing stores:",e)}};function VC(e,t){const[n,r]=w.useState(e);return w.useEffect(()=>{const s=setTimeout(()=>{r(e)},t);return()=>{clearTimeout(s)}},[e,t]),n}function IC(e,t,n=[]){const r=w.useRef(),s=w.useRef(e);w.useEffect(()=>{s.current=e},[e,...n]);const i=w.useCallback((...a)=>{r.current&&clearTimeout(r.current),r.current=setTimeout(()=>{s.current(...a)},t)},[t]);return w.useEffect(()=>()=>{r.current&&clearTimeout(r.current)},[]),i}const OC=({value:e,onChange:t,onFilterToggle:n,placeholder:r="Search todos...",showFilterButton:s=!0,isFilterActive:i=!1,suggestions:a=[],className:l=""})=>{const[c,u]=w.useState(!1),[d,h]=w.useState(!1),[f,g]=w.useState(-1),x=w.useRef(null),v=w.useRef(null),S=IC(t,300,[t]),p=a.filter(C=>C.toLowerCase().includes(e.toLowerCase())&&C!==e).slice(0,5),m=C=>{const P=C.target.value;t(P),S(P),g(-1),h(P.length>0&&p.length>0)},y=()=>{var C;t(""),h(!1),(C=x.current)==null||C.focus()},b=C=>{var P;t(C),h(!1),g(-1),(P=x.current)==null||P.focus()},j=C=>{var P,D;if(!d||p.length===0){C.key==="Escape"&&((P=x.current)==null||P.blur());return}switch(C.key){case"ArrowDown":C.preventDefault(),g(V=>V<p.length-1?V+1:0);break;case"ArrowUp":C.preventDefault(),g(V=>V>0?V-1:p.length-1);break;case"Enter":C.preventDefault(),f>=0&&b(p[f]);break;case"Escape":h(!1),g(-1),(D=x.current)==null||D.blur();break}},k=()=>{u(!0),e.length>0&&p.length>0&&h(!0)},_=()=>{u(!1),setTimeout(()=>{h(!1),g(-1)},150)};return w.useEffect(()=>{const C=P=>{var D;(P.metaKey||P.ctrlKey)&&P.key==="k"&&(P.preventDefault(),(D=x.current)==null||D.focus())};return document.addEventListener("keydown",C),()=>document.removeEventListener("keydown",C)},[]),o.jsxs("div",{className:`relative ${l}`,children:[o.jsx("div",{className:`fa-glass-panel relative transition-all duration-300 ${c?"ring-2 ring-fa-blue-400 ring-opacity-50":""}`,children:o.jsxs("div",{className:"flex items-center",children:[o.jsx("div",{className:"flex-shrink-0 pl-4",children:o.jsx(Kc,{className:`w-5 h-5 transition-colors duration-200 ${c?"text-fa-blue-500":"text-fa-gray-400"}`})}),o.jsx("input",{ref:x,type:"text",value:e,onChange:m,onFocus:k,onBlur:_,onKeyDown:j,placeholder:r,className:"flex-1 bg-transparent px-4 py-3 text-fa-gray-800 placeholder-fa-gray-400 focus:outline-none"}),!c&&!e&&o.jsx("div",{className:"flex-shrink-0 pr-2",children:o.jsxs("div",{className:"flex items-center space-x-1 text-fa-gray-400 text-sm",children:[o.jsx(nN,{className:"w-3 h-3"}),o.jsx("span",{children:"K"})]})}),e&&o.jsx(E.button,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},whileHover:{scale:1.1},whileTap:{scale:.9},onClick:y,className:"flex-shrink-0 p-1 mx-2 text-fa-gray-400 hover:text-fa-gray-600 rounded-lg hover:bg-fa-white-glass",children:o.jsx(et,{className:"w-4 h-4"})}),s&&o.jsx(E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:n,className:`flex-shrink-0 p-2 mx-2 rounded-lg transition-all duration-200 ${i?"bg-fa-blue-500 text-white":"text-fa-gray-400 hover:text-fa-gray-600 hover:bg-fa-white-glass"}`,children:o.jsx(iN,{className:"w-4 h-4"})})]})}),o.jsx(re,{children:d&&p.length>0&&o.jsx(E.div,{ref:v,initial:{opacity:0,y:-10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-10,scale:.95},transition:{duration:.2},className:"absolute top-full left-0 right-0 mt-2 fa-glass-panel-frosted border border-fa-gray-200 rounded-xl shadow-lg z-50 max-h-60 overflow-y-auto",children:p.map((C,P)=>o.jsx(E.button,{whileHover:{backgroundColor:"rgba(59, 130, 246, 0.1)"},onClick:()=>b(C),className:`w-full text-left px-4 py-3 transition-colors duration-150 ${P===f?"bg-fa-blue-100 text-fa-blue-800":"text-fa-gray-700 hover:bg-fa-gray-50"} ${P===0?"rounded-t-xl":""} ${P===p.length-1?"rounded-b-xl":""}`,children:o.jsxs("div",{className:"flex items-center",children:[o.jsx(Kc,{className:"w-4 h-4 mr-3 text-fa-gray-400"}),o.jsx("span",{children:C})]})},C))})})]})},eu={very_high:{label:"Very High",color:"text-red-600",bgColor:"bg-red-100",borderColor:"border-red-300",textColor:"text-red-800",icon:cr,weight:5,gradient:"from-red-500 to-red-600",glowColor:"shadow-red-500/50"},high:{label:"High",color:"text-orange-600",bgColor:"bg-orange-100",borderColor:"border-orange-300",textColor:"text-orange-800",icon:Zy,weight:4,gradient:"from-orange-500 to-orange-600",glowColor:"shadow-orange-500/30"},medium:{label:"Medium",color:"text-yellow-600",bgColor:"bg-yellow-100",borderColor:"border-yellow-300",textColor:"text-yellow-800",icon:rx,weight:3,gradient:"from-yellow-500 to-yellow-600"},low:{label:"Low",color:"text-blue-600",bgColor:"bg-blue-100",borderColor:"border-blue-300",textColor:"text-blue-800",icon:Yy,weight:2,gradient:"from-blue-500 to-blue-600"},very_low:{label:"Very Low",color:"text-gray-500",bgColor:"bg-gray-100",borderColor:"border-gray-300",textColor:"text-gray-700",icon:cN,weight:1,gradient:"from-gray-400 to-gray-500"}};function Vn(e){return eu[e]||eu.medium}function Om(e){return Vn(e).color}function Pl(e){return Vn(e).bgColor}function Al(e){return Vn(e).borderColor}function fn(e){return Vn(e).textColor}function zC(e){return Vn(e).icon}function zn(e){return Vn(e).label}function zm(e){return e==="high"||e==="very_high"}function pa(e){return e==="very_high"}function Yi(e){return Vn(e).gradient||""}function $m(e){return Vn(e).glowColor||""}function $C(){return Object.entries(eu).map(([e,t])=>({value:e,label:t.label,config:t}))}function UC(e){switch(e){case"very_high":return"h-2";case"high":return"h-1.5";case"medium":return"h-1";case"low":return"h-0.5";case"very_low":return"h-0.5";default:return"h-1"}}function Zi(e){return e==="very_high"?"animate-pulse":""}const Ya=({priority:e,variant:t="icon",size:n="md",showLabel:r=!1,className:s="",animated:i=!0})=>{const a=zC(e),l={sm:"w-3 h-3 text-xs",md:"w-4 h-4 text-sm",lg:"w-5 h-5 text-base"},c={sm:"px-1.5 py-0.5 text-xs",md:"px-2 py-1 text-sm",lg:"px-3 py-1.5 text-base"},u=l[n],d=c[n];return t==="icon"?o.jsxs(E.div,{className:`inline-flex items-center ${s}`,initial:i?{scale:.8,opacity:0}:void 0,animate:i?{scale:1,opacity:1}:void 0,transition:{duration:.2},children:[o.jsx(a,{className:`${u} ${Om(e)} ${Zi(e)}`}),r&&o.jsx("span",{className:`ml-1 ${fn(e)} font-medium`,children:zn(e)})]}):t==="badge"?o.jsxs(E.div,{className:`inline-flex items-center rounded-full ${d} ${Pl(e)} ${Al(e)} border ${s}`,initial:i?{scale:.8,opacity:0}:void 0,animate:i?{scale:1,opacity:1}:void 0,transition:{duration:.2},children:[o.jsx(a,{className:`${l.sm} ${fn(e)}`}),r&&o.jsx("span",{className:`ml-1 ${fn(e)} font-medium`,children:zn(e)})]}):t==="bar"?o.jsx(E.div,{className:`w-full ${UC(e)} bg-gradient-to-r ${Yi(e)} rounded-full ${s} ${Zi(e)}`,initial:i?{scaleX:0}:void 0,animate:i?{scaleX:1}:void 0,transition:{duration:.3}}):t==="chip"?o.jsxs(E.div,{className:`inline-flex items-center rounded-lg ${d} ${Pl(e)} ${Al(e)} border ${s}`,initial:i?{scale:.8,opacity:0}:void 0,animate:i?{scale:1,opacity:1}:void 0,transition:{duration:.2},children:[o.jsx(a,{className:`${l.sm} ${fn(e)}`}),o.jsx("span",{className:`ml-1 ${fn(e)} font-medium`,children:zn(e)})]}):t==="full"?o.jsxs(E.div,{className:`inline-flex items-center rounded-lg ${d} bg-gradient-to-r ${Yi(e)} text-white shadow-lg ${$m(e)} ${s} ${Zi(e)}`,initial:i?{scale:.8,opacity:0}:void 0,animate:i?{scale:1,opacity:1}:void 0,transition:{duration:.2},children:[o.jsx(a,{className:`${l.sm} text-white`}),o.jsx("span",{className:"ml-1 text-white font-medium",children:zn(e)})]}):t==="minimal"?o.jsxs(E.div,{className:`inline-flex items-center ${s}`,initial:i?{scale:.8,opacity:0}:void 0,animate:i?{scale:1,opacity:1}:void 0,transition:{duration:.2},children:[o.jsx("div",{className:`w-2 h-2 rounded-full bg-gradient-to-r ${Yi(e)} ${Zi(e)}`}),r&&o.jsx("span",{className:`ml-2 ${fn(e)} text-sm font-medium`,children:zn(e)})]}):t==="glow"?o.jsx(E.div,{className:`inline-flex items-center rounded-lg ${d} ${s}`,initial:i?{scale:.8,opacity:0}:void 0,animate:i?{scale:1,opacity:1}:void 0,transition:{duration:.2},children:pa(e)?o.jsxs(E.div,{className:`inline-flex items-center rounded-lg ${d} bg-gradient-to-r ${Yi(e)} text-white shadow-lg ${$m(e)} animate-pulse`,animate:{boxShadow:["0 0 20px rgba(239, 68, 68, 0.5)","0 0 30px rgba(239, 68, 68, 0.8)","0 0 20px rgba(239, 68, 68, 0.5)"]},transition:{duration:2,repeat:1/0,ease:"easeInOut"},children:[o.jsx(a,{className:`${l.sm} text-white`}),r&&o.jsx("span",{className:"ml-1 text-white font-bold",children:zn(e)})]}):o.jsxs("div",{className:`inline-flex items-center rounded-lg ${d} ${Pl(e)} ${Al(e)} border`,children:[o.jsx(a,{className:`${l.sm} ${fn(e)}`}),r&&o.jsx("span",{className:`ml-1 ${fn(e)} font-medium`,children:zn(e)})]})}):o.jsx(E.div,{className:`inline-flex items-center ${s}`,initial:i?{scale:.8,opacity:0}:void 0,animate:i?{scale:1,opacity:1}:void 0,transition:{duration:.2},children:o.jsx(a,{className:`${u} ${Om(e)}`})})},BC={pending:"Pending",in_progress:"In Progress",completed:"Completed",archived:"Archived",cancelled:"Cancelled"},HC=[{value:"all",label:"All"},{value:"overdue",label:"Overdue"},{value:"today",label:"Due Today"},{value:"tomorrow",label:"Due Tomorrow"},{value:"this_week",label:"This Week"},{value:"this_month",label:"This Month"},{value:"no_due_date",label:"No Due Date"}],WC=({isOpen:e,onClose:t,filters:n,onUpdateFilter:r,onApplyPreset:s,onClearFilters:i,presets:a,activePreset:l,filterOptions:c,filterSummary:u})=>{const[d,h]=w.useState(new Set(["presets","status","priority","categories"])),f=p=>{h(m=>{const y=new Set(m);return y.has(p)?y.delete(p):y.add(p),y})},g=p=>{const m=n.status.includes(p)?n.status.filter(y=>y!==p):[...n.status,p];r("status",m)},x=p=>{const m=n.priority.includes(p)?n.priority.filter(y=>y!==p):[...n.priority,p];r("priority",m)},v=p=>{const m=n.tags.includes(p)?n.tags.filter(y=>y!==p):[...n.tags,p];r("tags",m)},S=p=>{const m=n.category.includes(p)?n.category.filter(y=>y!==p):[...n.category,p];r("category",m)};return e?o.jsx(re,{children:o.jsx(E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50",onClick:t,children:o.jsxs(E.div,{initial:{opacity:0,x:300},animate:{opacity:1,x:0},exit:{opacity:0,x:300},transition:{type:"spring",damping:25,stiffness:300},className:"absolute right-0 top-0 h-full w-96 fa-glass-panel-frosted border-l border-fa-gray-200 overflow-y-auto",onClick:p=>p.stopPropagation(),children:[o.jsxs("div",{className:"sticky top-0 bg-fa-white-glass backdrop-blur-md border-b border-fa-gray-200 p-6",children:[o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsx("h2",{className:"fa-heading-2",children:"Filters"}),o.jsx(E.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:t,className:"p-2 text-fa-gray-400 hover:text-fa-gray-600 rounded-lg hover:bg-fa-white-glass",children:o.jsx(et,{className:"w-5 h-5"})})]}),o.jsx("div",{className:"mt-4 p-3 bg-fa-blue-50 rounded-lg",children:o.jsxs("div",{className:"flex items-center justify-between text-sm",children:[o.jsxs("span",{className:"text-fa-gray-600",children:["Showing ",u.totalFiltered," of ",u.totalOriginal," tasks"]}),u.hasActiveFilters&&o.jsxs(E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:i,className:"flex items-center space-x-1 text-fa-blue-600 hover:text-fa-blue-700",children:[o.jsx(mN,{className:"w-3 h-3"}),o.jsx("span",{children:"Clear"})]})]})})]}),o.jsxs("div",{className:"p-6 space-y-6",children:[o.jsxs("div",{children:[o.jsxs("button",{onClick:()=>f("presets"),className:"flex items-center justify-between w-full text-left",children:[o.jsx("h3",{className:"fa-heading-3",children:"Quick Filters"}),o.jsx(Rt,{className:`w-4 h-4 transition-transform ${d.has("presets")?"rotate-180":""}`})]}),o.jsx(re,{children:d.has("presets")&&o.jsx(E.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},className:"mt-3 space-y-2",children:a.map(p=>o.jsx(E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>s(p.id),className:`w-full text-left p-3 rounded-lg transition-all duration-200 ${l===p.id?"bg-fa-blue-100 border border-fa-blue-300 text-fa-blue-800":"bg-fa-white-glass hover:bg-fa-gray-50 border border-fa-gray-200"}`,children:o.jsxs("div",{className:"flex items-center space-x-3",children:[o.jsx("span",{className:"text-lg",children:p.icon}),o.jsx("div",{children:o.jsx("div",{className:"font-medium",children:p.name})})]})},p.id))})})]}),o.jsxs("div",{children:[o.jsxs("button",{onClick:()=>f("status"),className:"flex items-center justify-between w-full text-left",children:[o.jsxs("h3",{className:"fa-heading-3 flex items-center",children:[o.jsx(Qt,{className:"w-4 h-4 mr-2"}),"Status"]}),o.jsx(Rt,{className:`w-4 h-4 transition-transform ${d.has("status")?"rotate-180":""}`})]}),o.jsx(re,{children:d.has("status")&&o.jsx(E.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},className:"mt-3 space-y-2",children:c.statuses.map(p=>o.jsxs(E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>g(p),className:`w-full text-left p-2 rounded-lg transition-all duration-200 flex items-center space-x-3 ${n.status.includes(p)?"bg-fa-blue-100 text-fa-blue-800":"hover:bg-fa-gray-50"}`,children:[n.status.includes(p)?o.jsx(Qt,{className:"w-4 h-4 text-fa-blue-600"}):o.jsx(xs,{className:"w-4 h-4 text-fa-gray-400"}),o.jsx("span",{children:BC[p]})]},p))})})]}),o.jsxs("div",{children:[o.jsxs("button",{onClick:()=>f("priority"),className:"flex items-center justify-between w-full text-left",children:[o.jsxs("h3",{className:"fa-heading-3 flex items-center",children:[o.jsx(rx,{className:"w-4 h-4 mr-2"}),"Priority"]}),o.jsx(Rt,{className:`w-4 h-4 transition-transform ${d.has("priority")?"rotate-180":""}`})]}),o.jsx(re,{children:d.has("priority")&&o.jsx(E.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},className:"mt-3 space-y-2",children:c.priorities.map(p=>o.jsxs(E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>x(p),className:`w-full text-left p-2 rounded-lg transition-all duration-200 flex items-center space-x-3 ${n.priority.includes(p)?"bg-fa-blue-100 text-fa-blue-800":"hover:bg-fa-gray-50"}`,children:[n.priority.includes(p)?o.jsx(Qt,{className:"w-4 h-4 text-fa-blue-600"}):o.jsx(xs,{className:"w-4 h-4 text-fa-gray-400"}),o.jsx(Ya,{priority:p,variant:"icon",size:"sm",showLabel:!0,animated:!1})]},p))})})]}),o.jsxs("div",{children:[o.jsxs("button",{onClick:()=>f("dueDate"),className:"flex items-center justify-between w-full text-left",children:[o.jsxs("h3",{className:"fa-heading-3 flex items-center",children:[o.jsx(Fn,{className:"w-4 h-4 mr-2"}),"Due Date"]}),o.jsx(Rt,{className:`w-4 h-4 transition-transform ${d.has("dueDate")?"rotate-180":""}`})]}),o.jsx(re,{children:d.has("dueDate")&&o.jsx(E.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},className:"mt-3 space-y-2",children:HC.map(p=>o.jsxs(E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>r("dueDate",p.value),className:`w-full text-left p-2 rounded-lg transition-all duration-200 flex items-center space-x-3 ${n.dueDate===p.value?"bg-fa-blue-100 text-fa-blue-800":"hover:bg-fa-gray-50"}`,children:[n.dueDate===p.value?o.jsx(Qt,{className:"w-4 h-4 text-fa-blue-600"}):o.jsx(xs,{className:"w-4 h-4 text-fa-gray-400"}),o.jsx("span",{children:p.label})]},p.value))})})]}),c.categories.length>0&&o.jsxs("div",{children:[o.jsxs("button",{onClick:()=>f("categories"),className:"flex items-center justify-between w-full text-left",children:[o.jsxs("h3",{className:"fa-heading-3 flex items-center",children:[o.jsx(tn,{className:"w-4 h-4 mr-2"}),"Categories"]}),o.jsx(Rt,{className:`w-4 h-4 transition-transform ${d.has("categories")?"rotate-180":""}`})]}),o.jsx(re,{children:d.has("categories")&&o.jsx(E.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},className:"mt-3 space-y-2",children:c.categories.map(p=>o.jsxs(E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>S(p.id),className:`w-full text-left p-2 rounded-lg transition-all duration-200 flex items-center space-x-3 ${n.category.includes(p.id)?"bg-fa-blue-100 text-fa-blue-800":"hover:bg-fa-gray-50"}`,children:[n.category.includes(p.id)?o.jsx(Qt,{className:"w-4 h-4 text-fa-blue-600"}):o.jsx(xs,{className:"w-4 h-4 text-fa-gray-400"}),o.jsx("div",{className:"w-3 h-3 rounded",style:{backgroundColor:p.color}}),o.jsx("span",{children:p.name})]},p.id))})})]}),c.tags.length>0&&o.jsxs("div",{children:[o.jsxs("button",{onClick:()=>f("tags"),className:"flex items-center justify-between w-full text-left",children:[o.jsxs("h3",{className:"fa-heading-3 flex items-center",children:[o.jsx(pr,{className:"w-4 h-4 mr-2"}),"Tags"]}),o.jsx(Rt,{className:`w-4 h-4 transition-transform ${d.has("tags")?"rotate-180":""}`})]}),o.jsx(re,{children:d.has("tags")&&o.jsx(E.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},className:"mt-3 space-y-2",children:c.tags.map(p=>o.jsxs(E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>v(p),className:`w-full text-left p-2 rounded-lg transition-all duration-200 flex items-center space-x-3 ${n.tags.includes(p)?"bg-fa-blue-100 text-fa-blue-800":"hover:bg-fa-gray-50"}`,children:[n.tags.includes(p)?o.jsx(Qt,{className:"w-4 h-4 text-fa-blue-600"}):o.jsx(xs,{className:"w-4 h-4 text-fa-gray-400"}),o.jsx("span",{children:p})]},p))})})]})]})]})})}):null},Um={title:"Title",status:"Status",priority:"Priority",due_date:"Due Date",created_at:"Created",updated_at:"Updated",position:"Position",estimated_duration:"Duration"},KC=({sortConfig:e,onUpdateSort:t,onApplyPreset:n,onToggleSortOrder:r,presets:s,activePreset:i,sortDescription:a,className:l=""})=>{var p;const[c,u]=w.useState(!1),[d,h]=w.useState(!1),f=w.useRef(null),g=w.useRef(null);w.useEffect(()=>{const m=y=>{f.current&&!f.current.contains(y.target)&&u(!1),g.current&&!g.current.contains(y.target)&&h(!1)};return document.addEventListener("mousedown",m),()=>document.removeEventListener("mousedown",m)},[]);const x=m=>{t(m),u(!1)},v=m=>{n(m),h(!1)},S=()=>{switch(e.order){case"asc":return o.jsx(Zy,{className:"w-4 h-4"});case"desc":return o.jsx(Yy,{className:"w-4 h-4"});default:return o.jsx(eN,{className:"w-4 h-4"})}};return o.jsxs("div",{className:`flex items-center space-x-2 ${l}`,children:[o.jsxs("div",{className:"relative",ref:g,children:[o.jsxs(E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>h(!d),className:`fa-button-glass px-3 py-2 flex items-center space-x-2 ${i?"ring-2 ring-fa-blue-400 ring-opacity-50":""}`,children:[o.jsx("span",{className:"text-sm font-medium",children:i?(p=s.find(m=>m.id===i))==null?void 0:p.name:"Sort by"}),o.jsx(Rt,{className:`w-4 h-4 transition-transform ${d?"rotate-180":""}`})]}),o.jsx(re,{children:d&&o.jsx(E.div,{initial:{opacity:0,y:-10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-10,scale:.95},transition:{duration:.2},className:"absolute top-full left-0 mt-2 w-64 fa-glass-panel-frosted border border-fa-gray-200 rounded-xl shadow-lg z-50 max-h-80 overflow-y-auto",children:o.jsxs("div",{className:"p-2",children:[o.jsx("div",{className:"px-3 py-2 text-xs font-medium text-fa-gray-500 uppercase tracking-wide",children:"Quick Sort Options"}),s.map(m=>o.jsxs(E.button,{whileHover:{backgroundColor:"rgba(59, 130, 246, 0.1)"},onClick:()=>v(m.id),className:`w-full text-left px-3 py-2 rounded-lg transition-colors duration-150 flex items-center justify-between ${i===m.id?"bg-fa-blue-100 text-fa-blue-800":"text-fa-gray-700 hover:bg-fa-gray-50"}`,children:[o.jsxs("div",{className:"flex items-center space-x-3",children:[o.jsx("span",{className:"text-lg",children:m.icon}),o.jsxs("div",{children:[o.jsx("div",{className:"font-medium",children:m.name}),m.description&&o.jsx("div",{className:"text-xs text-fa-gray-500",children:m.description})]})]}),i===m.id&&o.jsx(Zr,{className:"w-4 h-4 text-fa-blue-600"})]},m.id))]})})})]}),o.jsxs("div",{className:"flex items-center space-x-1",children:[o.jsxs("div",{className:"relative",ref:f,children:[o.jsxs(E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>u(!c),className:"fa-button-glass px-3 py-2 flex items-center space-x-2",children:[o.jsx("span",{className:"text-sm",children:Um[e.field]}),o.jsx(Rt,{className:`w-4 h-4 transition-transform ${c?"rotate-180":""}`})]}),o.jsx(re,{children:c&&o.jsx(E.div,{initial:{opacity:0,y:-10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-10,scale:.95},transition:{duration:.2},className:"absolute top-full left-0 mt-2 w-48 fa-glass-panel-frosted border border-fa-gray-200 rounded-xl shadow-lg z-50",children:o.jsx("div",{className:"p-2",children:Object.entries(Um).map(([m,y])=>o.jsxs(E.button,{whileHover:{backgroundColor:"rgba(59, 130, 246, 0.1)"},onClick:()=>x(m),className:`w-full text-left px-3 py-2 rounded-lg transition-colors duration-150 flex items-center justify-between ${e.field===m?"bg-fa-blue-100 text-fa-blue-800":"text-fa-gray-700 hover:bg-fa-gray-50"}`,children:[o.jsx("span",{children:y}),e.field===m&&o.jsx(Zr,{className:"w-4 h-4 text-fa-blue-600"})]},m))})})})]}),o.jsx(E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:r,className:"fa-button-glass p-2 flex items-center justify-center",title:`Sort ${e.order==="asc"?"descending":"ascending"}`,children:S()})]}),o.jsx("div",{className:"hidden md:block",children:o.jsx("span",{className:"text-sm text-fa-gray-500",children:a})})]})},jx=({isOpen:e,onClose:t,onConfirm:n,title:r,message:s,confirmText:i="Confirm",cancelText:a="Cancel",variant:l="danger",isLoading:c=!1})=>{const d=(()=>{switch(l){case"danger":return{icon:"text-fa-error",confirmButton:"bg-fa-error hover:bg-red-600 text-white",border:"border-fa-error-border"};case"warning":return{icon:"text-fa-warning",confirmButton:"bg-fa-warning hover:bg-yellow-600 text-white",border:"border-fa-warning-border"};case"info":return{icon:"text-fa-info",confirmButton:"bg-fa-info hover:bg-blue-600 text-white",border:"border-fa-info-border"};default:return{icon:"text-fa-error",confirmButton:"bg-fa-error hover:bg-red-600 text-white",border:"border-fa-error-border"}}})();return o.jsx(re,{children:e&&o.jsxs(o.Fragment,{children:[o.jsx(E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50",onClick:t}),o.jsx("div",{className:"fixed inset-0 flex items-center justify-center z-50 p-4",children:o.jsxs(E.div,{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:20},transition:{type:"spring",damping:25,stiffness:300},className:`fa-glass-panel-frosted max-w-md w-full p-6 ${d.border}`,onClick:h=>h.stopPropagation(),children:[o.jsxs("div",{className:"flex items-start justify-between mb-4",children:[o.jsxs("div",{className:"flex items-center space-x-3",children:[o.jsx("div",{className:`p-2 rounded-full bg-fa-white-glass ${d.icon}`,children:o.jsx(cr,{className:"w-5 h-5"})}),o.jsx("h3",{className:"fa-heading-3 text-fa-gray-800",children:r})]}),o.jsx(E.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:t,className:"p-1 text-fa-gray-400 hover:text-fa-gray-600 rounded-lg hover:bg-fa-white-glass",disabled:c,children:o.jsx(et,{className:"w-5 h-5"})})]}),o.jsx("div",{className:"mb-6",children:o.jsx("p",{className:"fa-body text-fa-gray-600",children:s})}),o.jsxs("div",{className:"flex items-center justify-end space-x-3",children:[o.jsx(E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:t,disabled:c,className:"fa-button-glass px-4 py-2 disabled:opacity-50 disabled:cursor-not-allowed",children:a}),o.jsx(E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:n,disabled:c,className:`px-4 py-2 rounded-lg font-medium transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed ${d.confirmButton}`,children:c?o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),o.jsx("span",{children:"Processing..."})]}):i})]})]})})]})})};var Ti=e=>e.type==="checkbox",Yn=e=>e instanceof Date,Ue=e=>e==null;const Nx=e=>typeof e=="object";var be=e=>!Ue(e)&&!Array.isArray(e)&&Nx(e)&&!Yn(e),Cx=e=>be(e)&&e.target?Ti(e.target)?e.target.checked:e.target.value:e,GC=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,kx=(e,t)=>e.has(GC(t)),QC=e=>{const t=e.constructor&&e.constructor.prototype;return be(t)&&t.hasOwnProperty("isPrototypeOf")},Od=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function ke(e){let t;const n=Array.isArray(e),r=typeof FileList<"u"?e instanceof FileList:!1;if(e instanceof Date)t=new Date(e);else if(!(Od&&(e instanceof Blob||r))&&(n||be(e)))if(t=n?[]:Object.create(Object.getPrototypeOf(e)),!n&&!QC(e))t=e;else for(const s in e)e.hasOwnProperty(s)&&(t[s]=ke(e[s]));else return e;return t}var Vo=e=>/^\w*$/.test(e),we=e=>e===void 0,zd=e=>Array.isArray(e)?e.filter(Boolean):[],$d=e=>zd(e.replace(/["|']|\]/g,"").split(/\.|\[/)),z=(e,t,n)=>{if(!t||!be(e))return n;const r=(Vo(t)?[t]:$d(t)).reduce((s,i)=>Ue(s)?s:s[i],e);return we(r)||r===e?we(e[t])?n:e[t]:r},nt=e=>typeof e=="boolean",ie=(e,t,n)=>{let r=-1;const s=Vo(t)?[t]:$d(t),i=s.length,a=i-1;for(;++r<i;){const l=s[r];let c=n;if(r!==a){const u=e[l];c=be(u)||Array.isArray(u)?u:isNaN(+s[r+1])?{}:[]}if(l==="__proto__"||l==="constructor"||l==="prototype")return;e[l]=c,e=e[l]}};const Za={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},Tt={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},Wt={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},Tx=Z.createContext(null);Tx.displayName="HookFormContext";const Ud=()=>Z.useContext(Tx);var _x=(e,t,n,r=!0)=>{const s={defaultValues:t._defaultValues};for(const i in e)Object.defineProperty(s,i,{get:()=>{const a=i;return t._proxyFormState[a]!==Tt.all&&(t._proxyFormState[a]=!r||Tt.all),n&&(n[a]=!0),e[a]}});return s};const Bd=typeof window<"u"?Z.useLayoutEffect:Z.useEffect;function qC(e){const t=Ud(),{control:n=t.control,disabled:r,name:s,exact:i}=e||{},[a,l]=Z.useState(n._formState),c=Z.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1});return Bd(()=>n._subscribe({name:s,formState:c.current,exact:i,callback:u=>{!r&&l({...n._formState,...u})}}),[s,r,i]),Z.useEffect(()=>{c.current.isValid&&n._setValid(!0)},[n]),Z.useMemo(()=>_x(a,n,c.current,!1),[a,n])}var Ft=e=>typeof e=="string",Ex=(e,t,n,r,s)=>Ft(e)?(r&&t.watch.add(e),z(n,e,s)):Array.isArray(e)?e.map(i=>(r&&t.watch.add(i),z(n,i))):(r&&(t.watchAll=!0),n),tu=e=>Ue(e)||!Nx(e);function qt(e,t,n=new WeakSet){if(tu(e)||tu(t))return e===t;if(Yn(e)&&Yn(t))return e.getTime()===t.getTime();const r=Object.keys(e),s=Object.keys(t);if(r.length!==s.length)return!1;if(n.has(e)||n.has(t))return!0;n.add(e),n.add(t);for(const i of r){const a=e[i];if(!s.includes(i))return!1;if(i!=="ref"){const l=t[i];if(Yn(a)&&Yn(l)||be(a)&&be(l)||Array.isArray(a)&&Array.isArray(l)?!qt(a,l,n):a!==l)return!1}}return!0}function XC(e){const t=Ud(),{control:n=t.control,name:r,defaultValue:s,disabled:i,exact:a,compute:l}=e||{},c=Z.useRef(s),u=Z.useRef(l),d=Z.useRef(void 0);u.current=l;const h=Z.useMemo(()=>n._getWatch(r,c.current),[n,r]),[f,g]=Z.useState(u.current?u.current(h):h);return Bd(()=>n._subscribe({name:r,formState:{values:!0},exact:a,callback:x=>{if(!i){const v=Ex(r,n._names,x.values||n._formValues,!1,c.current);if(u.current){const S=u.current(v);qt(S,d.current)||(g(S),d.current=S)}else g(v)}}}),[n,i,r,a]),Z.useEffect(()=>n._removeUnmounted()),f}function YC(e){const t=Ud(),{name:n,disabled:r,control:s=t.control,shouldUnregister:i,defaultValue:a}=e,l=kx(s._names.array,n),c=Z.useMemo(()=>z(s._formValues,n,z(s._defaultValues,n,a)),[s,n,a]),u=XC({control:s,name:n,defaultValue:c,exact:!0}),d=qC({control:s,name:n,exact:!0}),h=Z.useRef(e),f=Z.useRef(s.register(n,{...e.rules,value:u,...nt(e.disabled)?{disabled:e.disabled}:{}}));h.current=e;const g=Z.useMemo(()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!z(d.errors,n)},isDirty:{enumerable:!0,get:()=>!!z(d.dirtyFields,n)},isTouched:{enumerable:!0,get:()=>!!z(d.touchedFields,n)},isValidating:{enumerable:!0,get:()=>!!z(d.validatingFields,n)},error:{enumerable:!0,get:()=>z(d.errors,n)}}),[d,n]),x=Z.useCallback(m=>f.current.onChange({target:{value:Cx(m),name:n},type:Za.CHANGE}),[n]),v=Z.useCallback(()=>f.current.onBlur({target:{value:z(s._formValues,n),name:n},type:Za.BLUR}),[n,s._formValues]),S=Z.useCallback(m=>{const y=z(s._fields,n);y&&m&&(y._f.ref={focus:()=>m.focus&&m.focus(),select:()=>m.select&&m.select(),setCustomValidity:b=>m.setCustomValidity(b),reportValidity:()=>m.reportValidity()})},[s._fields,n]),p=Z.useMemo(()=>({name:n,value:u,...nt(r)||d.disabled?{disabled:d.disabled||r}:{},onChange:x,onBlur:v,ref:S}),[n,r,d.disabled,x,v,S,u]);return Z.useEffect(()=>{const m=s._options.shouldUnregister||i;s.register(n,{...h.current.rules,...nt(h.current.disabled)?{disabled:h.current.disabled}:{}});const y=(b,j)=>{const k=z(s._fields,b);k&&k._f&&(k._f.mount=j)};if(y(n,!0),m){const b=ke(z(s._options.defaultValues,n));ie(s._defaultValues,n,b),we(z(s._formValues,n))&&ie(s._formValues,n,b)}return!l&&s.register(n),()=>{(l?m&&!s._state.action:m)?s.unregister(n):y(n,!1)}},[n,s,l,i]),Z.useEffect(()=>{s._setDisabledField({disabled:r,name:n})},[r,n,s]),Z.useMemo(()=>({field:p,formState:d,fieldState:g}),[p,d,g])}const Ji=e=>e.render(YC(e));var ZC=(e,t,n,r,s)=>t?{...n[e],types:{...n[e]&&n[e].types?n[e].types:{},[r]:s||!0}}:{},Us=e=>Array.isArray(e)?e:[e],Bm=()=>{let e=[];return{get observers(){return e},next:s=>{for(const i of e)i.next&&i.next(s)},subscribe:s=>(e.push(s),{unsubscribe:()=>{e=e.filter(i=>i!==s)}}),unsubscribe:()=>{e=[]}}},Qe=e=>be(e)&&!Object.keys(e).length,Hd=e=>e.type==="file",_t=e=>typeof e=="function",Ja=e=>{if(!Od)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},Px=e=>e.type==="select-multiple",Wd=e=>e.type==="radio",JC=e=>Wd(e)||Ti(e),Dl=e=>Ja(e)&&e.isConnected;function ek(e,t){const n=t.slice(0,-1).length;let r=0;for(;r<n;)e=we(e)?r++:e[t[r++]];return e}function tk(e){for(const t in e)if(e.hasOwnProperty(t)&&!we(e[t]))return!1;return!0}function Ne(e,t){const n=Array.isArray(t)?t:Vo(t)?[t]:$d(t),r=n.length===1?e:ek(e,n),s=n.length-1,i=n[s];return r&&delete r[i],s!==0&&(be(r)&&Qe(r)||Array.isArray(r)&&tk(r))&&Ne(e,n.slice(0,-1)),e}var Ax=e=>{for(const t in e)if(_t(e[t]))return!0;return!1};function eo(e,t={}){const n=Array.isArray(e);if(be(e)||n)for(const r in e)Array.isArray(e[r])||be(e[r])&&!Ax(e[r])?(t[r]=Array.isArray(e[r])?[]:{},eo(e[r],t[r])):Ue(e[r])||(t[r]=!0);return t}function Dx(e,t,n){const r=Array.isArray(e);if(be(e)||r)for(const s in e)Array.isArray(e[s])||be(e[s])&&!Ax(e[s])?we(t)||tu(n[s])?n[s]=Array.isArray(e[s])?eo(e[s],[]):{...eo(e[s])}:Dx(e[s],Ue(t)?{}:t[s],n[s]):n[s]=!qt(e[s],t[s]);return n}var vs=(e,t)=>Dx(e,t,eo(t));const Hm={value:!1,isValid:!1},Wm={value:!0,isValid:!0};var Mx=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter(n=>n&&n.checked&&!n.disabled).map(n=>n.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!we(e[0].attributes.value)?we(e[0].value)||e[0].value===""?Wm:{value:e[0].value,isValid:!0}:Wm:Hm}return Hm},Rx=(e,{valueAsNumber:t,valueAsDate:n,setValueAs:r})=>we(e)?e:t?e===""?NaN:e&&+e:n&&Ft(e)?new Date(e):r?r(e):e;const Km={isValid:!1,value:null};var Lx=e=>Array.isArray(e)?e.reduce((t,n)=>n&&n.checked&&!n.disabled?{isValid:!0,value:n.value}:t,Km):Km;function Gm(e){const t=e.ref;return Hd(t)?t.files:Wd(t)?Lx(e.refs).value:Px(t)?[...t.selectedOptions].map(({value:n})=>n):Ti(t)?Mx(e.refs).value:Rx(we(t.value)?e.ref.value:t.value,e)}var nk=(e,t,n,r)=>{const s={};for(const i of e){const a=z(t,i);a&&ie(s,i,a._f)}return{criteriaMode:n,names:[...e],fields:s,shouldUseNativeValidation:r}},to=e=>e instanceof RegExp,ws=e=>we(e)?e:to(e)?e.source:be(e)?to(e.value)?e.value.source:e.value:e,Qm=e=>({isOnSubmit:!e||e===Tt.onSubmit,isOnBlur:e===Tt.onBlur,isOnChange:e===Tt.onChange,isOnAll:e===Tt.all,isOnTouch:e===Tt.onTouched});const qm="AsyncFunction";var rk=e=>!!e&&!!e.validate&&!!(_t(e.validate)&&e.validate.constructor.name===qm||be(e.validate)&&Object.values(e.validate).find(t=>t.constructor.name===qm)),sk=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),Xm=(e,t,n)=>!n&&(t.watchAll||t.watch.has(e)||[...t.watch].some(r=>e.startsWith(r)&&/^\.\w+/.test(e.slice(r.length))));const Bs=(e,t,n,r)=>{for(const s of n||Object.keys(e)){const i=z(e,s);if(i){const{_f:a,...l}=i;if(a){if(a.refs&&a.refs[0]&&t(a.refs[0],s)&&!r)return!0;if(a.ref&&t(a.ref,a.name)&&!r)return!0;if(Bs(l,t))break}else if(be(l)&&Bs(l,t))break}}};function Ym(e,t,n){const r=z(e,n);if(r||Vo(n))return{error:r,name:n};const s=n.split(".");for(;s.length;){const i=s.join("."),a=z(t,i),l=z(e,i);if(a&&!Array.isArray(a)&&n!==i)return{name:n};if(l&&l.type)return{name:i,error:l};if(l&&l.root&&l.root.type)return{name:`${i}.root`,error:l.root};s.pop()}return{name:n}}var ik=(e,t,n,r)=>{n(e);const{name:s,...i}=e;return Qe(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find(a=>t[a]===(!r||Tt.all))},ak=(e,t,n)=>!e||!t||e===t||Us(e).some(r=>r&&(n?r===t:r.startsWith(t)||t.startsWith(r))),ok=(e,t,n,r,s)=>s.isOnAll?!1:!n&&s.isOnTouch?!(t||e):(n?r.isOnBlur:s.isOnBlur)?!e:(n?r.isOnChange:s.isOnChange)?e:!0,lk=(e,t)=>!zd(z(e,t)).length&&Ne(e,t),ck=(e,t,n)=>{const r=Us(z(e,n));return ie(r,"root",t[n]),ie(e,n,r),e},ga=e=>Ft(e);function Zm(e,t,n="validate"){if(ga(e)||Array.isArray(e)&&e.every(ga)||nt(e)&&!e)return{type:n,message:ga(e)?e:"",ref:t}}var xr=e=>be(e)&&!to(e)?e:{value:e,message:""},Jm=async(e,t,n,r,s,i)=>{const{ref:a,refs:l,required:c,maxLength:u,minLength:d,min:h,max:f,pattern:g,validate:x,name:v,valueAsNumber:S,mount:p}=e._f,m=z(n,v);if(!p||t.has(v))return{};const y=l?l[0]:a,b=A=>{s&&y.reportValidity&&(y.setCustomValidity(nt(A)?"":A||""),y.reportValidity())},j={},k=Wd(a),_=Ti(a),C=k||_,P=(S||Hd(a))&&we(a.value)&&we(m)||Ja(a)&&a.value===""||m===""||Array.isArray(m)&&!m.length,D=ZC.bind(null,v,r,j),V=(A,R,X,G=Wt.maxLength,W=Wt.minLength)=>{const U=A?R:X;j[v]={type:A?G:W,message:U,ref:a,...D(A?G:W,U)}};if(i?!Array.isArray(m)||!m.length:c&&(!C&&(P||Ue(m))||nt(m)&&!m||_&&!Mx(l).isValid||k&&!Lx(l).isValid)){const{value:A,message:R}=ga(c)?{value:!!c,message:c}:xr(c);if(A&&(j[v]={type:Wt.required,message:R,ref:y,...D(Wt.required,R)},!r))return b(R),j}if(!P&&(!Ue(h)||!Ue(f))){let A,R;const X=xr(f),G=xr(h);if(!Ue(m)&&!isNaN(m)){const W=a.valueAsNumber||m&&+m;Ue(X.value)||(A=W>X.value),Ue(G.value)||(R=W<G.value)}else{const W=a.valueAsDate||new Date(m),U=Y=>new Date(new Date().toDateString()+" "+Y),L=a.type=="time",K=a.type=="week";Ft(X.value)&&m&&(A=L?U(m)>U(X.value):K?m>X.value:W>new Date(X.value)),Ft(G.value)&&m&&(R=L?U(m)<U(G.value):K?m<G.value:W<new Date(G.value))}if((A||R)&&(V(!!A,X.message,G.message,Wt.max,Wt.min),!r))return b(j[v].message),j}if((u||d)&&!P&&(Ft(m)||i&&Array.isArray(m))){const A=xr(u),R=xr(d),X=!Ue(A.value)&&m.length>+A.value,G=!Ue(R.value)&&m.length<+R.value;if((X||G)&&(V(X,A.message,R.message),!r))return b(j[v].message),j}if(g&&!P&&Ft(m)){const{value:A,message:R}=xr(g);if(to(A)&&!m.match(A)&&(j[v]={type:Wt.pattern,message:R,ref:a,...D(Wt.pattern,R)},!r))return b(R),j}if(x){if(_t(x)){const A=await x(m,n),R=Zm(A,y);if(R&&(j[v]={...R,...D(Wt.validate,R.message)},!r))return b(R.message),j}else if(be(x)){let A={};for(const R in x){if(!Qe(A)&&!r)break;const X=Zm(await x[R](m,n),y,R);X&&(A={...X,...D(R,X.message)},b(X.message),r&&(j[v]=A))}if(!Qe(A)&&(j[v]={ref:y,...A},!r))return j}}return b(!0),j};const uk={mode:Tt.onSubmit,reValidateMode:Tt.onChange,shouldFocusError:!0};function dk(e={}){let t={...uk,...e},n={submitCount:0,isDirty:!1,isReady:!1,isLoading:_t(t.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1},r={},s=be(t.defaultValues)||be(t.values)?ke(t.defaultValues||t.values)||{}:{},i=t.shouldUnregister?{}:ke(s),a={action:!1,mount:!1,watch:!1},l={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},c,u=0;const d={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let h={...d};const f={array:Bm(),state:Bm()},g=t.criteriaMode===Tt.all,x=N=>T=>{clearTimeout(u),u=setTimeout(N,T)},v=async N=>{if(!t.disabled&&(d.isValid||h.isValid||N)){const T=t.resolver?Qe((await _()).errors):await P(r,!0);T!==n.isValid&&f.state.next({isValid:T})}},S=(N,T)=>{!t.disabled&&(d.isValidating||d.validatingFields||h.isValidating||h.validatingFields)&&((N||Array.from(l.mount)).forEach(M=>{M&&(T?ie(n.validatingFields,M,T):Ne(n.validatingFields,M))}),f.state.next({validatingFields:n.validatingFields,isValidating:!Qe(n.validatingFields)}))},p=(N,T=[],M,$,O=!0,I=!0)=>{if($&&M&&!t.disabled){if(a.action=!0,I&&Array.isArray(z(r,N))){const Q=M(z(r,N),$.argA,$.argB);O&&ie(r,N,Q)}if(I&&Array.isArray(z(n.errors,N))){const Q=M(z(n.errors,N),$.argA,$.argB);O&&ie(n.errors,N,Q),lk(n.errors,N)}if((d.touchedFields||h.touchedFields)&&I&&Array.isArray(z(n.touchedFields,N))){const Q=M(z(n.touchedFields,N),$.argA,$.argB);O&&ie(n.touchedFields,N,Q)}(d.dirtyFields||h.dirtyFields)&&(n.dirtyFields=vs(s,i)),f.state.next({name:N,isDirty:V(N,T),dirtyFields:n.dirtyFields,errors:n.errors,isValid:n.isValid})}else ie(i,N,T)},m=(N,T)=>{ie(n.errors,N,T),f.state.next({errors:n.errors})},y=N=>{n.errors=N,f.state.next({errors:n.errors,isValid:!1})},b=(N,T,M,$)=>{const O=z(r,N);if(O){const I=z(i,N,we(M)?z(s,N):M);we(I)||$&&$.defaultChecked||T?ie(i,N,T?I:Gm(O._f)):X(N,I),a.mount&&v()}},j=(N,T,M,$,O)=>{let I=!1,Q=!1;const te={name:N};if(!t.disabled){if(!M||$){(d.isDirty||h.isDirty)&&(Q=n.isDirty,n.isDirty=te.isDirty=V(),I=Q!==te.isDirty);const se=qt(z(s,N),T);Q=!!z(n.dirtyFields,N),se?Ne(n.dirtyFields,N):ie(n.dirtyFields,N,!0),te.dirtyFields=n.dirtyFields,I=I||(d.dirtyFields||h.dirtyFields)&&Q!==!se}if(M){const se=z(n.touchedFields,N);se||(ie(n.touchedFields,N,M),te.touchedFields=n.touchedFields,I=I||(d.touchedFields||h.touchedFields)&&se!==M)}I&&O&&f.state.next(te)}return I?te:{}},k=(N,T,M,$)=>{const O=z(n.errors,N),I=(d.isValid||h.isValid)&&nt(T)&&n.isValid!==T;if(t.delayError&&M?(c=x(()=>m(N,M)),c(t.delayError)):(clearTimeout(u),c=null,M?ie(n.errors,N,M):Ne(n.errors,N)),(M?!qt(O,M):O)||!Qe($)||I){const Q={...$,...I&&nt(T)?{isValid:T}:{},errors:n.errors,name:N};n={...n,...Q},f.state.next(Q)}},_=async N=>{S(N,!0);const T=await t.resolver(i,t.context,nk(N||l.mount,r,t.criteriaMode,t.shouldUseNativeValidation));return S(N),T},C=async N=>{const{errors:T}=await _(N);if(N)for(const M of N){const $=z(T,M);$?ie(n.errors,M,$):Ne(n.errors,M)}else n.errors=T;return T},P=async(N,T,M={valid:!0})=>{for(const $ in N){const O=N[$];if(O){const{_f:I,...Q}=O;if(I){const te=l.array.has(I.name),se=O._f&&rk(O._f);se&&d.validatingFields&&S([$],!0);const ft=await Jm(O,l.disabled,i,g,t.shouldUseNativeValidation&&!T,te);if(se&&d.validatingFields&&S([$]),ft[I.name]&&(M.valid=!1,T))break;!T&&(z(ft,I.name)?te?ck(n.errors,ft,I.name):ie(n.errors,I.name,ft[I.name]):Ne(n.errors,I.name))}!Qe(Q)&&await P(Q,T,M)}}return M.valid},D=()=>{for(const N of l.unMount){const T=z(r,N);T&&(T._f.refs?T._f.refs.every(M=>!Dl(M)):!Dl(T._f.ref))&&cn(N)}l.unMount=new Set},V=(N,T)=>!t.disabled&&(N&&T&&ie(i,N,T),!qt(Y(),s)),A=(N,T,M)=>Ex(N,l,{...a.mount?i:we(T)?s:Ft(N)?{[N]:T}:T},M,T),R=N=>zd(z(a.mount?i:s,N,t.shouldUnregister?z(s,N,[]):[])),X=(N,T,M={})=>{const $=z(r,N);let O=T;if($){const I=$._f;I&&(!I.disabled&&ie(i,N,Rx(T,I)),O=Ja(I.ref)&&Ue(T)?"":T,Px(I.ref)?[...I.ref.options].forEach(Q=>Q.selected=O.includes(Q.value)):I.refs?Ti(I.ref)?I.refs.forEach(Q=>{(!Q.defaultChecked||!Q.disabled)&&(Array.isArray(O)?Q.checked=!!O.find(te=>te===Q.value):Q.checked=O===Q.value||!!O)}):I.refs.forEach(Q=>Q.checked=Q.value===O):Hd(I.ref)?I.ref.value="":(I.ref.value=O,I.ref.type||f.state.next({name:N,values:ke(i)})))}(M.shouldDirty||M.shouldTouch)&&j(N,O,M.shouldTouch,M.shouldDirty,!0),M.shouldValidate&&K(N)},G=(N,T,M)=>{for(const $ in T){if(!T.hasOwnProperty($))return;const O=T[$],I=N+"."+$,Q=z(r,I);(l.array.has(N)||be(O)||Q&&!Q._f)&&!Yn(O)?G(I,O,M):X(I,O,M)}},W=(N,T,M={})=>{const $=z(r,N),O=l.array.has(N),I=ke(T);ie(i,N,I),O?(f.array.next({name:N,values:ke(i)}),(d.isDirty||d.dirtyFields||h.isDirty||h.dirtyFields)&&M.shouldDirty&&f.state.next({name:N,dirtyFields:vs(s,i),isDirty:V(N,I)})):$&&!$._f&&!Ue(I)?G(N,I,M):X(N,I,M),Xm(N,l)&&f.state.next({...n,name:N}),f.state.next({name:a.mount?N:void 0,values:ke(i)})},U=async N=>{a.mount=!0;const T=N.target;let M=T.name,$=!0;const O=z(r,M),I=se=>{$=Number.isNaN(se)||Yn(se)&&isNaN(se.getTime())||qt(se,z(i,M,se))},Q=Qm(t.mode),te=Qm(t.reValidateMode);if(O){let se,ft;const _i=T.type?Gm(O._f):Cx(N),un=N.type===Za.BLUR||N.type===Za.FOCUS_OUT,Gx=!sk(O._f)&&!t.resolver&&!z(n.errors,M)&&!O._f.deps||ok(un,z(n.touchedFields,M),n.isSubmitted,te,Q),zo=Xm(M,l,un);ie(i,M,_i),un?(!T||!T.readOnly)&&(O._f.onBlur&&O._f.onBlur(N),c&&c(0)):O._f.onChange&&O._f.onChange(N);const $o=j(M,_i,un),Qx=!Qe($o)||zo;if(!un&&f.state.next({name:M,type:N.type,values:ke(i)}),Gx)return(d.isValid||h.isValid)&&(t.mode==="onBlur"?un&&v():un||v()),Qx&&f.state.next({name:M,...zo?{}:$o});if(!un&&zo&&f.state.next({...n}),t.resolver){const{errors:Zd}=await _([M]);if(I(_i),$){const qx=Ym(n.errors,r,M),Jd=Ym(Zd,r,qx.name||M);se=Jd.error,M=Jd.name,ft=Qe(Zd)}}else S([M],!0),se=(await Jm(O,l.disabled,i,g,t.shouldUseNativeValidation))[M],S([M]),I(_i),$&&(se?ft=!1:(d.isValid||h.isValid)&&(ft=await P(r,!0)));$&&(O._f.deps&&K(O._f.deps),k(M,ft,se,$o))}},L=(N,T)=>{if(z(n.errors,T)&&N.focus)return N.focus(),1},K=async(N,T={})=>{let M,$;const O=Us(N);if(t.resolver){const I=await C(we(N)?N:O);M=Qe(I),$=N?!O.some(Q=>z(I,Q)):M}else N?($=(await Promise.all(O.map(async I=>{const Q=z(r,I);return await P(Q&&Q._f?{[I]:Q}:Q)}))).every(Boolean),!(!$&&!n.isValid)&&v()):$=M=await P(r);return f.state.next({...!Ft(N)||(d.isValid||h.isValid)&&M!==n.isValid?{}:{name:N},...t.resolver||!N?{isValid:M}:{},errors:n.errors}),T.shouldFocus&&!$&&Bs(r,L,N?O:l.mount),$},Y=N=>{const T={...a.mount?i:s};return we(N)?T:Ft(N)?z(T,N):N.map(M=>z(T,M))},ue=(N,T)=>({invalid:!!z((T||n).errors,N),isDirty:!!z((T||n).dirtyFields,N),error:z((T||n).errors,N),isValidating:!!z(n.validatingFields,N),isTouched:!!z((T||n).touchedFields,N)}),je=N=>{N&&Us(N).forEach(T=>Ne(n.errors,T)),f.state.next({errors:N?n.errors:{}})},In=(N,T,M)=>{const $=(z(r,N,{_f:{}})._f||{}).ref,O=z(n.errors,N)||{},{ref:I,message:Q,type:te,...se}=O;ie(n.errors,N,{...se,...T,ref:$}),f.state.next({name:N,errors:n.errors,isValid:!1}),M&&M.shouldFocus&&$&&$.focus&&$.focus()},Ut=(N,T)=>_t(N)?f.state.subscribe({next:M=>"values"in M&&N(A(void 0,T),M)}):A(N,T,!0),gr=N=>f.state.subscribe({next:T=>{ak(N.name,T.name,N.exact)&&ik(T,N.formState||d,Kx,N.reRenderRoot)&&N.callback({values:{...i},...n,...T,defaultValues:s})}}).unsubscribe,Bt=N=>(a.mount=!0,h={...h,...N.formState},gr({...N,formState:h})),cn=(N,T={})=>{for(const M of N?Us(N):l.mount)l.mount.delete(M),l.array.delete(M),T.keepValue||(Ne(r,M),Ne(i,M)),!T.keepError&&Ne(n.errors,M),!T.keepDirty&&Ne(n.dirtyFields,M),!T.keepTouched&&Ne(n.touchedFields,M),!T.keepIsValidating&&Ne(n.validatingFields,M),!t.shouldUnregister&&!T.keepDefaultValue&&Ne(s,M);f.state.next({values:ke(i)}),f.state.next({...n,...T.keepDirty?{isDirty:V()}:{}}),!T.keepIsValid&&v()},Gd=({disabled:N,name:T})=>{(nt(N)&&a.mount||N||l.disabled.has(T))&&(N?l.disabled.add(T):l.disabled.delete(T))},Io=(N,T={})=>{let M=z(r,N);const $=nt(T.disabled)||nt(t.disabled);return ie(r,N,{...M||{},_f:{...M&&M._f?M._f:{ref:{name:N}},name:N,mount:!0,...T}}),l.mount.add(N),M?Gd({disabled:nt(T.disabled)?T.disabled:t.disabled,name:N}):b(N,!0,T.value),{...$?{disabled:T.disabled||t.disabled}:{},...t.progressive?{required:!!T.required,min:ws(T.min),max:ws(T.max),minLength:ws(T.minLength),maxLength:ws(T.maxLength),pattern:ws(T.pattern)}:{},name:N,onChange:U,onBlur:U,ref:O=>{if(O){Io(N,T),M=z(r,N);const I=we(O.value)&&O.querySelectorAll&&O.querySelectorAll("input,select,textarea")[0]||O,Q=JC(I),te=M._f.refs||[];if(Q?te.find(se=>se===I):I===M._f.ref)return;ie(r,N,{_f:{...M._f,...Q?{refs:[...te.filter(Dl),I,...Array.isArray(z(s,N))?[{}]:[]],ref:{type:I.type,name:N}}:{ref:I}}}),b(N,!1,void 0,I)}else M=z(r,N,{}),M._f&&(M._f.mount=!1),(t.shouldUnregister||T.shouldUnregister)&&!(kx(l.array,N)&&a.action)&&l.unMount.add(N)}}},Oo=()=>t.shouldFocusError&&Bs(r,L,l.mount),Bx=N=>{nt(N)&&(f.state.next({disabled:N}),Bs(r,(T,M)=>{const $=z(r,M);$&&(T.disabled=$._f.disabled||N,Array.isArray($._f.refs)&&$._f.refs.forEach(O=>{O.disabled=$._f.disabled||N}))},0,!1))},Qd=(N,T)=>async M=>{let $;M&&(M.preventDefault&&M.preventDefault(),M.persist&&M.persist());let O=ke(i);if(f.state.next({isSubmitting:!0}),t.resolver){const{errors:I,values:Q}=await _();n.errors=I,O=ke(Q)}else await P(r);if(l.disabled.size)for(const I of l.disabled)Ne(O,I);if(Ne(n.errors,"root"),Qe(n.errors)){f.state.next({errors:{}});try{await N(O,M)}catch(I){$=I}}else T&&await T({...n.errors},M),Oo(),setTimeout(Oo);if(f.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:Qe(n.errors)&&!$,submitCount:n.submitCount+1,errors:n.errors}),$)throw $},Hx=(N,T={})=>{z(r,N)&&(we(T.defaultValue)?W(N,ke(z(s,N))):(W(N,T.defaultValue),ie(s,N,ke(T.defaultValue))),T.keepTouched||Ne(n.touchedFields,N),T.keepDirty||(Ne(n.dirtyFields,N),n.isDirty=T.defaultValue?V(N,ke(z(s,N))):V()),T.keepError||(Ne(n.errors,N),d.isValid&&v()),f.state.next({...n}))},qd=(N,T={})=>{const M=N?ke(N):s,$=ke(M),O=Qe(N),I=O?s:$;if(T.keepDefaultValues||(s=M),!T.keepValues){if(T.keepDirtyValues){const Q=new Set([...l.mount,...Object.keys(vs(s,i))]);for(const te of Array.from(Q))z(n.dirtyFields,te)?ie(I,te,z(i,te)):W(te,z(I,te))}else{if(Od&&we(N))for(const Q of l.mount){const te=z(r,Q);if(te&&te._f){const se=Array.isArray(te._f.refs)?te._f.refs[0]:te._f.ref;if(Ja(se)){const ft=se.closest("form");if(ft){ft.reset();break}}}}if(T.keepFieldsRef)for(const Q of l.mount)W(Q,z(I,Q));else r={}}i=t.shouldUnregister?T.keepDefaultValues?ke(s):{}:ke(I),f.array.next({values:{...I}}),f.state.next({values:{...I}})}l={mount:T.keepDirtyValues?l.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},a.mount=!d.isValid||!!T.keepIsValid||!!T.keepDirtyValues,a.watch=!!t.shouldUnregister,f.state.next({submitCount:T.keepSubmitCount?n.submitCount:0,isDirty:O?!1:T.keepDirty?n.isDirty:!!(T.keepDefaultValues&&!qt(N,s)),isSubmitted:T.keepIsSubmitted?n.isSubmitted:!1,dirtyFields:O?{}:T.keepDirtyValues?T.keepDefaultValues&&i?vs(s,i):n.dirtyFields:T.keepDefaultValues&&N?vs(s,N):T.keepDirty?n.dirtyFields:{},touchedFields:T.keepTouched?n.touchedFields:{},errors:T.keepErrors?n.errors:{},isSubmitSuccessful:T.keepIsSubmitSuccessful?n.isSubmitSuccessful:!1,isSubmitting:!1,defaultValues:s})},Xd=(N,T)=>qd(_t(N)?N(i):N,T),Wx=(N,T={})=>{const M=z(r,N),$=M&&M._f;if($){const O=$.refs?$.refs[0]:$.ref;O.focus&&(O.focus(),T.shouldSelect&&_t(O.select)&&O.select())}},Kx=N=>{n={...n,...N}},Yd={control:{register:Io,unregister:cn,getFieldState:ue,handleSubmit:Qd,setError:In,_subscribe:gr,_runSchema:_,_focusError:Oo,_getWatch:A,_getDirty:V,_setValid:v,_setFieldArray:p,_setDisabledField:Gd,_setErrors:y,_getFieldArray:R,_reset:qd,_resetDefaultValues:()=>_t(t.defaultValues)&&t.defaultValues().then(N=>{Xd(N,t.resetOptions),f.state.next({isLoading:!1})}),_removeUnmounted:D,_disableForm:Bx,_subjects:f,_proxyFormState:d,get _fields(){return r},get _formValues(){return i},get _state(){return a},set _state(N){a=N},get _defaultValues(){return s},get _names(){return l},set _names(N){l=N},get _formState(){return n},get _options(){return t},set _options(N){t={...t,...N}}},subscribe:Bt,trigger:K,register:Io,handleSubmit:Qd,watch:Ut,setValue:W,getValues:Y,reset:Xd,resetField:Hx,clearErrors:je,unregister:cn,setError:In,setFocus:Wx,getFieldState:ue};return{...Yd,formControl:Yd}}function Fx(e={}){const t=Z.useRef(void 0),n=Z.useRef(void 0),[r,s]=Z.useState({isDirty:!1,isValidating:!1,isLoading:_t(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:_t(e.defaultValues)?void 0:e.defaultValues});if(!t.current)if(e.formControl)t.current={...e.formControl,formState:r},e.defaultValues&&!_t(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions);else{const{formControl:a,...l}=dk(e);t.current={...l,formState:r}}const i=t.current.control;return i._options=e,Bd(()=>{const a=i._subscribe({formState:i._proxyFormState,callback:()=>s({...i._formState}),reRenderRoot:!0});return s(l=>({...l,isReady:!0})),i._formState.isReady=!0,a},[i]),Z.useEffect(()=>i._disableForm(e.disabled),[i,e.disabled]),Z.useEffect(()=>{e.mode&&(i._options.mode=e.mode),e.reValidateMode&&(i._options.reValidateMode=e.reValidateMode)},[i,e.mode,e.reValidateMode]),Z.useEffect(()=>{e.errors&&(i._setErrors(e.errors),i._focusError())},[i,e.errors]),Z.useEffect(()=>{e.shouldUnregister&&i._subjects.state.next({values:i._getWatch()})},[i,e.shouldUnregister]),Z.useEffect(()=>{if(i._proxyFormState.isDirty){const a=i._getDirty();a!==r.isDirty&&i._subjects.state.next({isDirty:a})}},[i,r.isDirty]),Z.useEffect(()=>{e.values&&!qt(e.values,n.current)?(i._reset(e.values,{keepFieldsRef:!0,...i._options.resetOptions}),n.current=e.values,s(a=>({...a}))):i._resetDefaultValues()},[i,e.values]),Z.useEffect(()=>{i._state.mount||(i._setValid(),i._state.mount=!0),i._state.watch&&(i._state.watch=!1,i._subjects.state.next({...i._formState})),i._removeUnmounted()}),t.current.formState=_x(r,i),t.current}const He={TITLE:{MIN_LENGTH:1,MAX_LENGTH:500},DESCRIPTION:{MAX_LENGTH:5e3},TAGS:{MAX_COUNT:20,MAX_TAG_LENGTH:50,MIN_TAG_LENGTH:1},ESTIMATED_DURATION:{MAX_HOURS:999,MIN_MINUTES:1}},ep=["pending","in_progress","completed","archived","cancelled"],tp=["very_low","low","medium","high","very_high"];function nr(e){return e?e.trim().replace(/\s+/g," "):""}function fk(e){const t=[],n=nr(e);return n?(n.length<He.TITLE.MIN_LENGTH&&t.push({field:"title",message:`Title must be at least ${He.TITLE.MIN_LENGTH} character long`,code:"TITLE_TOO_SHORT",value:e}),n.length>He.TITLE.MAX_LENGTH&&t.push({field:"title",message:`Title cannot exceed ${He.TITLE.MAX_LENGTH} characters`,code:"TITLE_TOO_LONG",value:e}),t):(t.push({field:"title",message:"Title is required",code:"TITLE_REQUIRED",value:e}),t)}function hk(e){const t=[],n=nr(e);return n&&n.length>He.DESCRIPTION.MAX_LENGTH&&t.push({field:"description",message:`Description cannot exceed ${He.DESCRIPTION.MAX_LENGTH} characters`,code:"DESCRIPTION_TOO_LONG",value:e}),t}function mk(e){const t=[];return e&&!ep.includes(e)&&t.push({field:"status",message:`Invalid status. Must be one of: ${ep.join(", ")}`,code:"INVALID_STATUS",value:e}),t}function pk(e){const t=[];return e&&!tp.includes(e)&&t.push({field:"priority",message:`Invalid priority. Must be one of: ${tp.join(", ")}`,code:"INVALID_PRIORITY",value:e}),t}function gk(e,t=!0){const n=[];if(!e)return n;let r;try{r=typeof e=="string"?new Date(e):e}catch{return n.push({field:"due_date",message:"Invalid date format",code:"INVALID_DATE_FORMAT",value:e}),n}if(isNaN(r.getTime()))return n.push({field:"due_date",message:"Invalid date",code:"INVALID_DATE",value:e}),n;!t&&r<new Date&&n.push({field:"due_date",message:"Due date cannot be in the past",code:"DUE_DATE_IN_PAST",value:e});const s=new Date;return s.setFullYear(s.getFullYear()+10),r>s&&n.push({field:"due_date",message:"Due date is too far in the future",code:"DUE_DATE_TOO_FAR",value:e}),n}function yk(e,t){const n=[];if(!e)return n;let r;try{r=typeof e=="string"?new Date(e):e}catch{return n.push({field:"reminder_at",message:"Invalid reminder date format",code:"INVALID_REMINDER_FORMAT",value:e}),n}if(isNaN(r.getTime()))return n.push({field:"reminder_at",message:"Invalid reminder date",code:"INVALID_REMINDER_DATE",value:e}),n;if(r<new Date&&n.push({field:"reminder_at",message:"Reminder date cannot be in the past",code:"REMINDER_IN_PAST",value:e}),t){const s=typeof t=="string"?new Date(t):t;!isNaN(s.getTime())&&r>s&&n.push({field:"reminder_at",message:"Reminder date cannot be after due date",code:"REMINDER_AFTER_DUE_DATE",value:e})}return n}function xk(e){const t=[];return e?Array.isArray(e)?(e.length>He.TAGS.MAX_COUNT&&t.push({field:"tags",message:`Cannot have more than ${He.TAGS.MAX_COUNT} tags`,code:"TOO_MANY_TAGS",value:e}),e.forEach((r,s)=>{if(typeof r!="string"){t.push({field:`tags[${s}]`,message:"Tag must be a string",code:"TAG_NOT_STRING",value:r});return}const i=nr(r);i.length<He.TAGS.MIN_TAG_LENGTH&&t.push({field:`tags[${s}]`,message:"Tag cannot be empty",code:"TAG_EMPTY",value:r}),i.length>He.TAGS.MAX_TAG_LENGTH&&t.push({field:`tags[${s}]`,message:`Tag cannot exceed ${He.TAGS.MAX_TAG_LENGTH} characters`,code:"TAG_TOO_LONG",value:r})}),new Set(e.map(r=>nr(r).toLowerCase())).size!==e.length&&t.push({field:"tags",message:"Duplicate tags are not allowed",code:"DUPLICATE_TAGS",value:e}),t):(t.push({field:"tags",message:"Tags must be an array",code:"TAGS_NOT_ARRAY",value:e}),t):t}function vk(e){const t=[];if(!e)return t;const n=/^PT(?:(\d+)H)?(?:(\d+)M)?$/,r=e.match(n);if(!r)return t.push({field:"estimated_duration",message:"Invalid duration format. Use format like PT1H30M (1 hour 30 minutes)",code:"INVALID_DURATION_FORMAT",value:e}),t;const s=parseInt(r[1]||"0",10),i=parseInt(r[2]||"0",10);return s>He.ESTIMATED_DURATION.MAX_HOURS&&t.push({field:"estimated_duration",message:`Duration cannot exceed ${He.ESTIMATED_DURATION.MAX_HOURS} hours`,code:"DURATION_TOO_LONG",value:e}),s===0&&i<He.ESTIMATED_DURATION.MIN_MINUTES&&t.push({field:"estimated_duration",message:`Duration must be at least ${He.ESTIMATED_DURATION.MIN_MINUTES} minute`,code:"DURATION_TOO_SHORT",value:e}),t}function wk(e){const t=[];return e?(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e)||t.push({field:"user_id",message:"Invalid user ID format",code:"INVALID_USER_ID_FORMAT",value:e}),t):(t.push({field:"user_id",message:"User ID is required",code:"USER_ID_REQUIRED",value:e}),t)}function Sk(e){const t=[];return e&&(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e)||t.push({field:"category_id",message:"Invalid category ID format",code:"INVALID_CATEGORY_ID_FORMAT",value:e})),t}function Ml(e){const t={...e};return t.title!==void 0&&(t.title=nr(t.title)),t.description!==void 0&&(t.description=nr(t.description)||void 0),t.tags&&(t.tags=t.tags.map(n=>nr(n)).filter(n=>n.length>0).filter((n,r,s)=>s.indexOf(n)===r)),t.metadata===void 0?t.metadata={}:(typeof t.metadata!="object"||t.metadata===null)&&(t.metadata={}),t}function np(e,t={}){const{isCreate:n=!1,allowPastDueDates:r=!0,requireUserId:s=!0}=t,i=[];return(n||e.title!==void 0)&&i.push(...fk(e.title)),s&&(n||e.user_id!==void 0)&&i.push(...wk(e.user_id)),e.description!==void 0&&i.push(...hk(e.description)),e.status!==void 0&&i.push(...mk(e.status)),e.priority!==void 0&&i.push(...pk(e.priority)),e.due_date!==void 0&&i.push(...gk(e.due_date,r)),e.reminder_at!==void 0&&i.push(...yk(e.reminder_at,e.due_date)),e.tags!==void 0&&i.push(...xk(e.tags)),e.estimated_duration!==void 0&&i.push(...vk(e.estimated_duration)),e.category_id!==void 0&&i.push(...Sk(e.category_id)),{isValid:i.length===0,errors:i}}function bk(e,t){return e.filter(n=>n.field===t||n.field.startsWith(`${t}[`))}function jk(e={}){const[t,n]=w.useState({isValid:!0,errors:[],fieldErrors:{},hasErrors:!1,isValidating:!1}),r=w.useCallback(h=>{const f={};h.errors.forEach(g=>{const x=g.field;f[x]||(f[x]=[]),f[x].push(g)}),n({isValid:h.isValid,errors:h.errors,fieldErrors:f,hasErrors:h.errors.length>0,isValidating:!1})},[]),s=w.useCallback(h=>{n(x=>({...x,isValidating:!0}));const f=Ml(h),g=np(f,e);return r(g),g},[e,r]),i=w.useCallback((h,f,g={})=>{const x={...g,[h]:f},v=Ml(x),S=np(v,{...e,isCreate:!1});return bk(S.errors,h)},[e]),a=w.useCallback(()=>{n({isValid:!0,errors:[],fieldErrors:{},hasErrors:!1,isValidating:!1})},[]),l=w.useCallback(h=>{n(f=>{const g={...f.fieldErrors};delete g[h];const x=f.errors.filter(v=>!v.field.startsWith(h)&&v.field!==h);return{...f,errors:x,fieldErrors:g,hasErrors:x.length>0,isValid:x.length===0}})},[]),c=w.useCallback(h=>{const f=t.fieldErrors[h];return!f||f.length===0?null:f.length===1?f[0].message:f.map(g=>g.message).join(", ")},[t.fieldErrors]),u=w.useCallback(h=>{var f;return((f=t.fieldErrors[h])==null?void 0:f.length)>0},[t.fieldErrors]),d=w.useCallback(h=>Ml(h),[]);return{validationState:t,validateData:s,validateField:i,clearValidation:a,clearFieldErrors:l,getFieldErrorMessage:c,hasFieldError:u,sanitizeData:d}}const Nk=({toast:e,onDismiss:t})=>{const[n,r]=w.useState(!0);w.useEffect(()=>{if(e.duration&&e.duration>0){const c=setTimeout(()=>{r(!1),setTimeout(()=>t(e.id),300)},e.duration);return()=>clearTimeout(c)}},[e.duration,e.id,t]);const s=()=>{r(!1),setTimeout(()=>t(e.id),300)},i=()=>{switch(e.type){case"success":return o.jsx(Qt,{className:"w-5 h-5 text-green-500"});case"error":return o.jsx(fi,{className:"w-5 h-5 text-red-500"});case"warning":return o.jsx(cr,{className:"w-5 h-5 text-yellow-500"});case"info":return o.jsx(Tm,{className:"w-5 h-5 text-blue-500"});default:return o.jsx(Tm,{className:"w-5 h-5 text-blue-500"})}},a=()=>{switch(e.type){case"success":return"bg-green-50 border-green-200";case"error":return"bg-red-50 border-red-200";case"warning":return"bg-yellow-50 border-yellow-200";case"info":return"bg-blue-50 border-blue-200";default:return"bg-blue-50 border-blue-200"}},l=()=>{switch(e.type){case"success":return"text-green-800";case"error":return"text-red-800";case"warning":return"text-yellow-800";case"info":return"text-blue-800";default:return"text-blue-800"}};return o.jsx(re,{children:n&&o.jsx(E.div,{initial:{opacity:0,y:-50,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.95},transition:{duration:.3,ease:"easeOut"},className:`
            relative max-w-sm w-full border rounded-lg shadow-lg backdrop-blur-sm
            ${a()}
          `,children:o.jsx("div",{className:"p-4",children:o.jsxs("div",{className:"flex items-start space-x-3",children:[o.jsx("div",{className:"flex-shrink-0",children:i()}),o.jsxs("div",{className:"flex-1 min-w-0",children:[o.jsx("h4",{className:`text-sm font-medium ${l()}`,children:e.title}),e.message&&o.jsx("p",{className:`mt-1 text-sm ${l()} opacity-80`,children:e.message}),e.action&&o.jsx("div",{className:"mt-3",children:o.jsx("button",{onClick:e.action.onClick,className:`
                        text-sm font-medium underline hover:no-underline
                        ${l()}
                      `,children:e.action.label})})]}),o.jsx("button",{onClick:s,className:`
                  flex-shrink-0 p-1 rounded-md hover:bg-black hover:bg-opacity-10
                  ${l()} opacity-60 hover:opacity-100
                `,children:o.jsx(et,{className:"w-4 h-4"})})]})})})})},Ck=({toasts:e,onDismiss:t,position:n="top-right"})=>{const r=()=>{switch(n){case"top-right":return"top-4 right-4";case"top-left":return"top-4 left-4";case"bottom-right":return"bottom-4 right-4";case"bottom-left":return"bottom-4 left-4";case"top-center":return"top-4 left-1/2 transform -translate-x-1/2";case"bottom-center":return"bottom-4 left-1/2 transform -translate-x-1/2";default:return"top-4 right-4"}};return o.jsx("div",{className:`fixed z-50 ${r()}`,children:o.jsx("div",{className:"space-y-3",children:o.jsx(re,{children:e.map(s=>o.jsx(Nk,{toast:s,onDismiss:t},s.id))})})})};function Vx(){const[e,t]=w.useState([]),n=w.useCallback(u=>{const d=Math.random().toString(36).substr(2,9),h={...u,id:d,duration:u.duration??5e3};return t(f=>[...f,h]),d},[]),r=w.useCallback(u=>{t(d=>d.filter(h=>h.id!==u))},[]),s=w.useCallback(()=>{t([])},[]),i=w.useCallback((u,d,h)=>n({...h,type:"success",title:u,message:d}),[n]),a=w.useCallback((u,d,h)=>n({...h,type:"error",title:u,message:d,duration:(h==null?void 0:h.duration)??7e3}),[n]),l=w.useCallback((u,d,h)=>n({...h,type:"warning",title:u,message:d}),[n]),c=w.useCallback((u,d,h)=>n({...h,type:"info",title:u,message:d}),[n]);return{toasts:e,addToast:n,dismissToast:r,dismissAll:s,success:i,error:a,warning:l,info:c}}const kk={folder:tn,tag:pr,star:Rd,heart:Pd,zap:To,home:Ad,briefcase:ex,"shopping-cart":ox,calendar:Fn,book:Jy,music:ix,camera:tx,coffee:nx},Tk=({categories:e,selectedCategoryId:t,onSelect:n,onCreateNew:r,placeholder:s="Select a category...",disabled:i=!1,className:a=""})=>{const[l,c]=w.useState(!1),[u,d]=w.useState(""),h=w.useRef(null),f=w.useRef(null),g=e.find(y=>y.id===t),x=e.filter(y=>y.name.toLowerCase().includes(u.toLowerCase()));w.useEffect(()=>{const y=b=>{h.current&&!h.current.contains(b.target)&&(c(!1),d(""))};return document.addEventListener("mousedown",y),()=>document.removeEventListener("mousedown",y)},[]),w.useEffect(()=>{l&&f.current&&f.current.focus()},[l]);const v=()=>{i||(c(!l),d(""))},S=y=>{n(y),c(!1),d("")},p=()=>{r&&(r(),c(!1),d(""))},m=y=>(y?kk[y]:tn)||tn;return o.jsxs("div",{className:`relative ${a}`,ref:h,children:[o.jsxs(E.button,{type:"button",whileHover:i?{}:{scale:1.01},whileTap:i?{}:{scale:.99},onClick:v,disabled:i,className:`w-full flex items-center justify-between p-3 border rounded-lg transition-all duration-200 ${i?"bg-fa-gray-100 border-fa-gray-200 text-fa-gray-400 cursor-not-allowed":l?"border-fa-blue-400 bg-fa-blue-50":"border-fa-gray-300 bg-white hover:border-fa-gray-400"}`,children:[o.jsx("div",{className:"flex items-center space-x-3",children:g?o.jsxs(o.Fragment,{children:[o.jsx("div",{className:"w-5 h-5 rounded flex items-center justify-center",style:{backgroundColor:g.color},children:Z.createElement(m(g.icon),{className:"w-3 h-3 text-white"})}),o.jsx("span",{className:"text-fa-gray-800",children:g.name})]}):o.jsxs(o.Fragment,{children:[o.jsx(tn,{className:"w-5 h-5 text-fa-gray-400"}),o.jsx("span",{className:"text-fa-gray-500",children:s})]})}),o.jsxs("div",{className:"flex items-center space-x-2",children:[g&&o.jsx(E.button,{type:"button",whileHover:{scale:1.1},whileTap:{scale:.9},onClick:y=>{y.stopPropagation(),S(void 0)},className:"p-1 rounded text-fa-gray-400 hover:text-fa-gray-600",children:o.jsx(et,{className:"w-3 h-3"})}),o.jsx(Rt,{className:`w-4 h-4 text-fa-gray-400 transition-transform duration-200 ${l?"rotate-180":""}`})]})]}),o.jsx(re,{children:l&&o.jsxs(E.div,{initial:{opacity:0,y:-10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-10,scale:.95},className:"absolute top-full left-0 right-0 mt-2 bg-white border border-fa-gray-200 rounded-lg shadow-lg z-50 max-h-64 overflow-hidden",children:[o.jsx("div",{className:"p-3 border-b border-fa-gray-100",children:o.jsxs("div",{className:"relative",children:[o.jsx(Kc,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-fa-gray-400"}),o.jsx("input",{ref:f,type:"text",value:u,onChange:y=>d(y.target.value),placeholder:"Search categories...",className:"w-full pl-10 pr-4 py-2 border border-fa-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-fa-blue-400 focus:border-transparent"})]})}),o.jsxs("div",{className:"max-h-48 overflow-y-auto",children:[o.jsxs(E.button,{type:"button",whileHover:{backgroundColor:"rgba(0, 0, 0, 0.05)"},onClick:()=>S(void 0),className:"w-full flex items-center space-x-3 p-3 text-left hover:bg-fa-gray-50 transition-colors duration-150",children:[o.jsx("div",{className:"w-5 h-5 rounded border-2 border-dashed border-fa-gray-300 flex items-center justify-center",children:o.jsx(et,{className:"w-3 h-3 text-fa-gray-400"})}),o.jsx("span",{className:"text-fa-gray-500 italic",children:"No category"})]}),x.map(y=>{const b=m(y.icon);return o.jsxs(E.button,{type:"button",whileHover:{backgroundColor:"rgba(0, 0, 0, 0.05)"},onClick:()=>S(y.id),className:`w-full flex items-center space-x-3 p-3 text-left transition-colors duration-150 ${t===y.id?"bg-fa-blue-50 text-fa-blue-800":"hover:bg-fa-gray-50"}`,children:[o.jsx("div",{className:"w-5 h-5 rounded flex items-center justify-center",style:{backgroundColor:y.color},children:o.jsx(b,{className:"w-3 h-3 text-white"})}),o.jsx("span",{className:"flex-1",children:y.name}),y.is_default&&o.jsx(Rd,{className:"w-3 h-3 text-fa-yellow-500"})]},y.id)}),u&&x.length===0&&o.jsx("div",{className:"p-3 text-center text-fa-gray-500 text-sm",children:"No categories found"}),r&&o.jsxs(E.button,{type:"button",whileHover:{backgroundColor:"rgba(59, 130, 246, 0.05)"},onClick:p,className:"w-full flex items-center space-x-3 p-3 text-left border-t border-fa-gray-100 text-fa-blue-600 hover:bg-fa-blue-50 transition-colors duration-150",children:[o.jsx("div",{className:"w-5 h-5 rounded border-2 border-dashed border-fa-blue-300 flex items-center justify-center",children:o.jsx(ko,{className:"w-3 h-3"})}),o.jsx("span",{children:"Create new category"})]})]})]})})]})},_k=({tags:e,onChange:t,suggestions:n=[],placeholder:r="Add a tag...",maxTags:s=10,disabled:i=!1,className:a="",enableSmartSuggestions:l=!0})=>{const[c,u]=w.useState(""),[d,h]=w.useState(!1),[f,g]=w.useState(-1),[x,v]=w.useState([]),[S,p]=w.useState(!1),m=w.useRef(null),y=w.useRef(null),b=w.useRef(null),j=w.useCallback(async G=>{if(l){p(!0);try{const W=await _s.getAutocompleteSuggestions(G,e,5);v(W)}catch(W){console.error("Error fetching smart suggestions:",W),v([])}finally{p(!1)}}},[e,l]),_=(l?x:n).filter(G=>G.toLowerCase().includes(c.toLowerCase())&&!e.includes(G)).slice(0,5),C=G=>{const W=G.target.value;u(W),g(-1),b.current&&clearTimeout(b.current),l&&(b.current=setTimeout(()=>{j(W)},300));const U=!l&&n.length>0,L=l&&x.length>0;h(W.length>0&&(U||L))},P=G=>{if(!i)switch(G.key){case"Enter":case",":G.preventDefault(),f>=0&&_[f]?D(_[f]):c.trim()&&D(c.trim());break;case"ArrowDown":G.preventDefault(),d&&g(W=>W<_.length-1?W+1:W);break;case"ArrowUp":G.preventDefault(),d&&g(W=>W>0?W-1:-1);break;case"Escape":h(!1),g(-1);break;case"Backspace":c===""&&e.length>0&&V(e[e.length-1]);break}},D=G=>{const W=G.trim().toLowerCase();W&&!e.includes(W)&&e.length<s&&t([...e,W]),u(""),h(!1),g(-1)},V=G=>{t(e.filter(W=>W!==G))},A=G=>{D(G)},R=()=>{setTimeout(()=>{h(!1),g(-1)},150)},X=()=>{i||(l&&c.length===0&&j(""),(c.length>0&&_.length>0||l)&&h(!0))};return w.useEffect(()=>()=>{b.current&&clearTimeout(b.current)},[]),w.useEffect(()=>{l&&j("")},[j,l]),o.jsxs("div",{className:`relative ${a}`,children:[o.jsx("div",{className:`min-h-[42px] p-2 border rounded-lg transition-all duration-200 ${i?"bg-fa-gray-100 border-fa-gray-200 cursor-not-allowed":"bg-white border-fa-gray-300 hover:border-fa-gray-400 focus-within:border-fa-blue-400 focus-within:ring-2 focus-within:ring-fa-blue-400 focus-within:ring-opacity-20"}`,children:o.jsxs("div",{className:"flex flex-wrap gap-2 items-center",children:[o.jsx(re,{children:e.map((G,W)=>o.jsxs(E.span,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},className:"inline-flex items-center px-2 py-1 rounded-full text-sm bg-fa-blue-100 text-fa-blue-800",children:[o.jsx(km,{className:"w-3 h-3 mr-1"}),G,!i&&o.jsx(E.button,{whileHover:{scale:1.1},whileTap:{scale:.9},type:"button",onClick:()=>V(G),className:"ml-1 text-fa-blue-600 hover:text-fa-blue-800",children:o.jsx(et,{className:"w-3 h-3"})})]},G))}),e.length<s&&o.jsx("input",{ref:m,type:"text",value:c,onChange:C,onKeyDown:P,onBlur:R,onFocus:X,disabled:i,placeholder:e.length===0?r:"",className:"flex-1 min-w-[120px] bg-transparent border-none outline-none text-sm placeholder-fa-gray-400 disabled:cursor-not-allowed"}),c.trim()&&!i&&o.jsx(E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},type:"button",onClick:()=>D(c.trim()),className:"p-1 rounded text-fa-blue-600 hover:bg-fa-blue-100",children:o.jsx(ko,{className:"w-4 h-4"})})]})}),o.jsx(re,{children:d&&(_.length>0||S)&&o.jsx(E.div,{ref:y,initial:{opacity:0,y:-10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-10,scale:.95},className:"absolute top-full left-0 right-0 mt-1 bg-white border border-fa-gray-200 rounded-lg shadow-lg z-50 max-h-40 overflow-y-auto",children:S?o.jsxs("div",{className:"flex items-center justify-center px-3 py-2",children:[o.jsx(oN,{className:"w-4 h-4 animate-spin text-fa-gray-400 mr-2"}),o.jsx("span",{className:"text-sm text-fa-gray-500",children:"Loading suggestions..."})]}):_.map((G,W)=>o.jsxs(E.button,{type:"button",whileHover:{backgroundColor:"rgba(0, 0, 0, 0.05)"},onClick:()=>A(G),className:`w-full flex items-center space-x-2 px-3 py-2 text-left transition-colors duration-150 ${W===f?"bg-fa-blue-50 text-fa-blue-800":"hover:bg-fa-gray-50"} ${W===0?"rounded-t-lg":""} ${W===_.length-1?"rounded-b-lg":""}`,children:[o.jsx(km,{className:"w-4 h-4 text-fa-gray-400"}),o.jsx("span",{children:G})]},G))})}),e.length>=s&&o.jsxs("p",{className:"text-xs text-fa-gray-500 mt-1",children:["Maximum ",s," tags allowed"]})]})},Ek=["#007bff","#28a745","#dc3545","#ffc107","#6f42c1","#fd7e14","#20c997","#e83e8c","#6c757d","#17a2b8","#343a40","#f8f9fa","#ff6b6b","#4ecdc4","#45b7d1","#96ceb4","#ffeaa7","#dda0dd","#98d8c8","#f7dc6f","#bb8fce","#85c1e9","#f8c471","#82e0aa"],Pk=()=>{const e=Math.floor(Math.random()*360),t=Math.floor(Math.random()*40)+60,n=Math.floor(Math.random()*30)+40;return((s,i,a)=>{a/=100;const l=i*Math.min(a,1-a)/100,c=u=>{const d=(u+s/30)%12,h=a-l*Math.max(Math.min(d-3,9-d,1),-1);return Math.round(255*h).toString(16).padStart(2,"0")};return`#${c(0)}${c(8)}${c(4)}`})(e,t,n)},Ak=({selectedColor:e,onColorSelect:t,className:n=""})=>{const[r,s]=w.useState(e),[i,a]=w.useState(!1),l=h=>{t(h),s(h)},c=h=>{const f=h.target.value;s(f),t(f)},u=()=>{const h=Pk();t(h),s(h)},d=h=>h===e;return o.jsxs("div",{className:`space-y-4 ${n}`,children:[o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-fa-gray-700 mb-2",children:"Choose a color"}),o.jsx("div",{className:"grid grid-cols-8 gap-2",children:Ek.map(h=>o.jsx(E.button,{whileHover:{scale:1.1},whileTap:{scale:.95},onClick:()=>l(h),className:`w-8 h-8 rounded-lg border-2 transition-all duration-200 ${d(h)?"border-fa-gray-800 shadow-lg":"border-fa-gray-200 hover:border-fa-gray-400"}`,style:{backgroundColor:h},title:h,children:o.jsx(re,{children:d(h)&&o.jsx(E.div,{initial:{scale:0,opacity:0},animate:{scale:1,opacity:1},exit:{scale:0,opacity:0},className:"w-full h-full flex items-center justify-center",children:o.jsx(Zr,{className:"w-4 h-4 text-white drop-shadow-sm"})})})},h))})]}),o.jsxs("div",{className:"flex items-center space-x-3",children:[o.jsxs(E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:u,className:"flex items-center space-x-2 px-3 py-2 bg-fa-blue-100 text-fa-blue-700 rounded-lg hover:bg-fa-blue-200 transition-colors",children:[o.jsx(lr,{className:"w-4 h-4"}),o.jsx("span",{children:"Random"})]}),o.jsxs(E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>a(!i),className:"flex items-center space-x-2 px-3 py-2 bg-fa-gray-100 text-fa-gray-700 rounded-lg hover:bg-fa-gray-200 transition-colors",children:[o.jsx(fN,{className:"w-4 h-4"}),o.jsx("span",{children:"Custom"})]})]}),o.jsx(re,{children:i&&o.jsxs(E.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"space-y-2",children:[o.jsx("label",{className:"block text-sm font-medium text-fa-gray-700",children:"Custom color"}),o.jsxs("div",{className:"flex items-center space-x-3",children:[o.jsx("input",{type:"color",value:r,onChange:c,className:"w-12 h-10 border border-fa-gray-300 rounded-lg cursor-pointer"}),o.jsx("input",{type:"text",value:r,onChange:h=>{const f=h.target.value;s(f),/^#[0-9A-F]{6}$/i.test(f)&&t(f)},placeholder:"#007bff",className:"flex-1 px-3 py-2 border border-fa-gray-300 rounded-lg focus:ring-2 focus:ring-fa-blue-500 focus:border-transparent"})]}),o.jsx("p",{className:"text-xs text-fa-gray-500",children:"Enter a hex color code (e.g., #007bff) or use the color picker"})]})}),o.jsxs("div",{className:"flex items-center space-x-3 p-3 bg-fa-gray-50 rounded-lg",children:[o.jsx("div",{className:"w-6 h-6 rounded border border-fa-gray-300",style:{backgroundColor:e}}),o.jsxs("div",{children:[o.jsx("p",{className:"text-sm font-medium text-fa-gray-800",children:"Selected Color"}),o.jsx("p",{className:"text-xs text-fa-gray-500",children:e})]})]})]})},rp=[{icon:tn,name:"folder"},{icon:pr,name:"tag"},{icon:Rd,name:"star"},{icon:Pd,name:"heart"},{icon:To,name:"zap"},{icon:Ad,name:"home"},{icon:ex,name:"briefcase"},{icon:ox,name:"shopping-cart"},{icon:Fn,name:"calendar"},{icon:Jy,name:"book"},{icon:ix,name:"music"},{icon:tx,name:"camera"},{icon:nx,name:"coffee"}],Dk=({isOpen:e,onClose:t,onSubmit:n,category:r,mode:s})=>{const[i,a]=w.useState(!1),[l,c]=w.useState(null),{register:u,handleSubmit:d,control:h,watch:f,setValue:g,reset:x,formState:{errors:v,isValid:S}}=Fx({defaultValues:{name:"",color:"#007bff",icon:"folder",is_default:!1},mode:"onChange"}),p=f("color"),m=f("icon");w.useEffect(()=>{e&&(x(s==="edit"&&r?{name:r.name,color:r.color,icon:r.icon||"folder",is_default:r.is_default}:{name:"",color:"#007bff",icon:"folder",is_default:!1}),c(null))},[e,s,r,x]);const y=async k=>{a(!0),c(null);try{await n({name:k.name.trim(),color:k.color,icon:k.icon,is_default:k.is_default}),t()}catch(_){const C=_ instanceof Error?_.message:"An error occurred";c(C)}finally{a(!1)}},b=()=>{const k=rp.find(_=>_.name===m);return k?k.icon:tn};if(!e)return null;const j=b();return o.jsx(re,{children:o.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:o.jsxs(E.div,{initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:20},className:"fa-glass-panel w-full max-w-md max-h-[90vh] overflow-y-auto",children:[o.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-fa-white-glass",children:[o.jsxs("div",{className:"flex items-center space-x-3",children:[o.jsx("div",{className:"w-8 h-8 rounded-lg flex items-center justify-center",style:{backgroundColor:p},children:o.jsx(j,{className:"w-4 h-4 text-white"})}),o.jsx("h2",{className:"fa-h3 text-fa-gray-800",children:s==="create"?"Create Category":"Edit Category"})]}),o.jsx(E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:t,className:"p-2 rounded-lg text-fa-gray-500 hover:bg-fa-white-glass",children:o.jsx(et,{className:"w-5 h-5"})})]}),o.jsxs("form",{onSubmit:d(y),className:"p-6 space-y-6",children:[l&&o.jsx(E.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"p-4 bg-fa-error bg-opacity-10 border border-fa-error rounded-lg",children:o.jsx("p",{className:"text-fa-error text-sm",children:l})}),o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-fa-gray-700 mb-2",children:"Category Name"}),o.jsx("input",{...u("name",{required:"Category name is required",minLength:{value:1,message:"Name must be at least 1 character"},maxLength:{value:50,message:"Name must be less than 50 characters"}}),className:"fa-input w-full",placeholder:"Enter category name...",autoFocus:!0}),v.name&&o.jsx("p",{className:"text-fa-error text-sm mt-1",children:v.name.message})]}),o.jsx(Ak,{selectedColor:p,onColorSelect:k=>g("color",k)}),o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-fa-gray-700 mb-2",children:"Icon"}),o.jsx("div",{className:"grid grid-cols-6 gap-2",children:rp.map(({icon:k,name:_})=>o.jsx(E.button,{type:"button",whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>g("icon",_),className:`p-3 rounded-lg border transition-all duration-200 flex items-center justify-center ${m===_?"border-fa-blue-400 bg-fa-blue-50":"border-fa-gray-200 hover:border-fa-gray-400 hover:bg-fa-gray-50"}`,children:o.jsx(k,{className:"w-5 h-5 text-fa-gray-600"})},_))})]}),o.jsxs("div",{className:"flex items-center space-x-3",children:[o.jsx("input",{...u("is_default"),type:"checkbox",id:"is_default",className:"w-4 h-4 text-fa-blue-600 bg-fa-gray-100 border-fa-gray-300 rounded focus:ring-fa-blue-500"}),o.jsx("label",{htmlFor:"is_default",className:"text-sm text-fa-gray-700",children:"Set as default category"})]}),o.jsxs("div",{className:"flex space-x-3 pt-4",children:[o.jsx(E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"button",onClick:t,disabled:i,className:"flex-1 fa-button-secondary",children:"Cancel"}),o.jsx(E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:!S||i,className:"flex-1 fa-button-primary disabled:opacity-50 disabled:cursor-not-allowed",children:i?o.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[o.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),o.jsx("span",{children:"Saving..."})]}):o.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[o.jsx(Md,{className:"w-4 h-4"}),o.jsx("span",{children:s==="create"?"Create":"Save"})]})})]})]})]})})})},Mk=$C(),Rk=[{value:"pending",label:"Pending",color:"text-fa-gray-500"},{value:"in_progress",label:"In Progress",color:"text-fa-blue-500"},{value:"completed",label:"Completed",color:"text-fa-green-500"},{value:"archived",label:"Archived",color:"text-fa-gray-400"},{value:"cancelled",label:"Cancelled",color:"text-fa-red-500"}],Ix=({isOpen:e,onClose:t,onSubmit:n,todo:r,mode:s})=>{var D,V;const[i,a]=w.useState(!1),[l,c]=w.useState(null),[u,d]=w.useState(!1),h=jk({isCreate:s==="create",allowPastDueDates:!0,requireUserId:!1}),f=Vx(),{categories:g,getAllTags:x,createCategory:v,loadCategories:S}=Fo(),{register:p,handleSubmit:m,control:y,reset:b,formState:{errors:j,isValid:k}}=Fx({defaultValues:{title:"",description:"",priority:"medium",status:"pending",category_id:void 0,due_date:"",reminder_at:"",tags:[],estimated_duration_hours:0,estimated_duration_minutes:0},mode:"onChange"});w.useEffect(()=>{if(e){if(s==="edit"&&r){const A=r.due_date?new Date(r.due_date).toISOString().split("T")[0]:"",R=r.reminder_at?new Date(r.reminder_at).toISOString().slice(0,16):"";let X=0,G=0;if(r.estimated_duration){const W=r.estimated_duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?/);W&&(X=parseInt(W[1]||"0",10),G=parseInt(W[2]||"0",10))}b({title:r.title,description:r.description||"",priority:r.priority,status:r.status,category_id:r.category_id,due_date:A,reminder_at:R,tags:r.tags,estimated_duration_hours:X,estimated_duration_minutes:G})}else b({title:"",description:"",priority:"medium",status:"pending",category_id:void 0,due_date:"",reminder_at:"",tags:[],estimated_duration_hours:0,estimated_duration_minutes:0});c(null)}},[e,s,r,b]);const _=(A,R)=>A===0&&R===0?"":`PT${A>0?`${A}H`:""}${R>0?`${R}M`:""}`,C=async A=>{a(!0),c(null);try{const R=_(A.estimated_duration_hours,A.estimated_duration_minutes),X={title:A.title.trim(),description:A.description.trim()||void 0,priority:A.priority,status:A.status,category_id:A.category_id||void 0,due_date:A.due_date?new Date(A.due_date):void 0,tags:A.tags,estimated_duration:R||void 0},G=h.validateData(X);if(!G.isValid){const W=G.errors.length===1?G.errors[0].message:`Please fix ${G.errors.length} validation errors`;c(W),f.error("Validation Error",W);return}if(s==="create"){const W=X,U=await Be.createTodo(W);f.success("Todo Created","Your todo has been created successfully"),n(U)}else{if(!r)throw new Error("Todo is required for edit mode");const W=X,U=await Be.updateTodo(r.id,W);f.success("Todo Updated","Your todo has been updated successfully"),n(U)}t()}catch(R){const X=R instanceof Error?R.message:"An error occurred";c(X),X.includes("validation")?f.error("Validation Error",X):X.includes("not found")?f.error("Todo Not Found","The todo you are trying to edit no longer exists"):f.error("Operation Failed",X)}finally{a(!1)}},P=async A=>{try{await v(A),await S(),f.success("Category Created",`"${A.name}" has been created successfully`)}catch(R){const X=R instanceof Error?R.message:"Failed to create category";throw f.error("Create Category Failed",X),R}};return e?o.jsxs(o.Fragment,{children:[o.jsx(re,{children:e&&o.jsx(E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-40 backdrop-blur-sm flex items-center justify-center z-50 p-4",onClick:t,children:o.jsxs(E.div,{initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:20},transition:{duration:.2},className:"fa-glass-panel-frosted w-full max-w-2xl max-h-[90vh] overflow-y-auto",onClick:A=>A.stopPropagation(),children:[o.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-fa-gray-200",children:[o.jsx("h2",{className:"fa-heading-2",children:s==="create"?"Create New Todo":"Edit Todo"}),o.jsx(E.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:t,className:"p-2 text-fa-gray-500 hover:text-fa-gray-700 rounded-lg hover:bg-fa-gray-100",children:o.jsx(et,{className:"w-5 h-5"})})]}),l&&o.jsxs(E.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mx-6 mt-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm flex items-center space-x-2",children:[o.jsx(fi,{className:"w-4 h-4"}),o.jsx("span",{children:l})]}),o.jsxs("form",{onSubmit:m(C),className:"p-6 space-y-6",children:[o.jsxs("div",{children:[o.jsx("label",{htmlFor:"title",className:"block text-sm font-medium text-fa-gray-700 mb-2",children:"Title *"}),o.jsx("input",{id:"title",type:"text",...p("title",{required:"Title is required",minLength:{value:1,message:"Title cannot be empty"},maxLength:{value:200,message:"Title is too long"}}),className:`fa-input ${j.title?"border-red-500":""}`,placeholder:"What needs to be done?",autoFocus:!0}),j.title&&o.jsx("p",{className:"mt-1 text-sm text-red-600",children:j.title.message})]}),o.jsxs("div",{children:[o.jsx("label",{htmlFor:"description",className:"block text-sm font-medium text-fa-gray-700 mb-2",children:"Description"}),o.jsx("textarea",{id:"description",...p("description"),className:"fa-input min-h-[100px] resize-y",placeholder:"Add more details...",rows:4})]}),o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[o.jsxs("div",{children:[o.jsx("label",{htmlFor:"priority",className:"block text-sm font-medium text-fa-gray-700 mb-2",children:"Priority"}),o.jsx(Ji,{name:"priority",control:y,render:({field:A})=>o.jsxs("div",{className:"space-y-2",children:[o.jsx("select",{...A,className:"fa-input",children:Mk.map(R=>o.jsx("option",{value:R.value,children:R.label},R.value))}),o.jsxs("div",{className:"flex items-center space-x-2 p-2 bg-fa-gray-50 rounded-lg",children:[o.jsx("span",{className:"text-sm text-fa-gray-600",children:"Preview:"}),o.jsx(Ya,{priority:A.value,variant:"chip",size:"sm",showLabel:!0,animated:!0})]})]})})]}),s==="edit"&&o.jsxs("div",{children:[o.jsx("label",{htmlFor:"status",className:"block text-sm font-medium text-fa-gray-700 mb-2",children:"Status"}),o.jsx(Ji,{name:"status",control:y,render:({field:A})=>o.jsx("select",{...A,className:"fa-input",children:Rk.map(R=>o.jsx("option",{value:R.value,children:R.label},R.value))})})]})]}),o.jsxs("div",{children:[o.jsxs("label",{className:"block text-sm font-medium text-fa-gray-700 mb-2",children:[o.jsx(tn,{className:"w-4 h-4 inline mr-1"}),"Category"]}),o.jsx(Ji,{name:"category_id",control:y,render:({field:A})=>o.jsx(Tk,{categories:g,selectedCategoryId:A.value,onSelect:A.onChange,onCreateNew:()=>d(!0),placeholder:"Select a category..."})})]}),o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[o.jsxs("div",{children:[o.jsxs("label",{htmlFor:"due_date",className:"block text-sm font-medium text-fa-gray-700 mb-2",children:[o.jsx(Fn,{className:"w-4 h-4 inline mr-1"}),"Due Date"]}),o.jsx("input",{id:"due_date",type:"date",...p("due_date"),className:"fa-input"})]}),o.jsxs("div",{children:[o.jsxs("label",{htmlFor:"reminder_at",className:"block text-sm font-medium text-fa-gray-700 mb-2",children:[o.jsx(Jr,{className:"w-4 h-4 inline mr-1"}),"Reminder"]}),o.jsx("input",{id:"reminder_at",type:"datetime-local",...p("reminder_at"),className:"fa-input"})]})]}),o.jsxs("div",{children:[o.jsxs("label",{className:"block text-sm font-medium text-fa-gray-700 mb-2",children:[o.jsx(pr,{className:"w-4 h-4 inline mr-1"}),"Tags"]}),o.jsx(Ji,{name:"tags",control:y,render:({field:A})=>o.jsx(_k,{tags:A.value,onChange:A.onChange,suggestions:x(),placeholder:"Add tags...",maxTags:10,enableSmartSuggestions:!0})})]}),o.jsxs("div",{children:[o.jsxs("label",{className:"block text-sm font-medium text-fa-gray-700 mb-2",children:[o.jsx(Jr,{className:"w-4 h-4 inline mr-1"}),"Estimated Duration"]}),o.jsxs("div",{className:"flex space-x-4 items-center",children:[o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("input",{type:"number",...p("estimated_duration_hours",{min:{value:0,message:"Hours must be positive"},max:{value:999,message:"Hours must be less than 1000"}}),className:"fa-input w-20",placeholder:"0",min:"0",max:"999"}),o.jsx("span",{className:"text-sm text-fa-gray-600",children:"hours"})]}),o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("input",{type:"number",...p("estimated_duration_minutes",{min:{value:0,message:"Minutes must be positive"},max:{value:59,message:"Minutes must be less than 60"}}),className:"fa-input w-20",placeholder:"0",min:"0",max:"59"}),o.jsx("span",{className:"text-sm text-fa-gray-600",children:"minutes"})]})]}),(j.estimated_duration_hours||j.estimated_duration_minutes)&&o.jsx("p",{className:"mt-1 text-sm text-red-600",children:((D=j.estimated_duration_hours)==null?void 0:D.message)||((V=j.estimated_duration_minutes)==null?void 0:V.message)})]}),o.jsxs("div",{className:"flex justify-end space-x-3 pt-4 border-t border-fa-gray-200",children:[o.jsx(E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"button",onClick:t,disabled:i,className:"px-6 py-3 fa-button-glass disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancel"}),o.jsx(E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:i||!k,className:"px-6 py-3 fa-button-primary text-white disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:i?o.jsxs(o.Fragment,{children:[o.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),o.jsx("span",{children:s==="create"?"Creating...":"Saving..."})]}):o.jsxs(o.Fragment,{children:[o.jsx(Md,{className:"w-4 h-4"}),o.jsx("span",{children:s==="create"?"Create Todo":"Save Changes"})]})})]})]})]})},"todo-form-modal")}),o.jsx(Dk,{isOpen:u,onClose:()=>d(!1),onSubmit:P,mode:"create"})]}):null};function Lk(e){if(!e.due_date||e.status==="completed"||e.status==="cancelled")return{isOverdue:!1,severity:"none",daysOverdue:0,hoursOverdue:0,formattedDuration:""};const t=new Date,n=new Date(e.due_date),r=t.getTime()-n.getTime();if(r<=0)return{isOverdue:!1,severity:"none",daysOverdue:0,hoursOverdue:0,formattedDuration:""};const s=Math.floor(r/(1e3*60*60)),i=Math.floor(r/(1e3*60*60*24));let a="slight";i>=7?a="severe":i>=1&&(a="moderate");const l=Fk(s,i);return{isOverdue:!0,severity:a,daysOverdue:i,hoursOverdue:s,formattedDuration:l}}function Fk(e,t){return t>=1?t===1?"1 day overdue":`${t} days overdue`:e>=1?e===1?"1 hour overdue":`${e} hours overdue`:"Just overdue"}function Vk(e){switch(e){case"slight":return{borderClass:"border-l-4 border-orange-400",bgClass:"bg-orange-50",textClass:"text-orange-600",iconClass:"text-orange-500",pulseClass:""};case"moderate":return{borderClass:"border-l-4 border-red-500",bgClass:"bg-red-50",textClass:"text-red-700",iconClass:"text-red-600",pulseClass:""};case"severe":return{borderClass:"border-l-4 border-red-600",bgClass:"bg-red-100",textClass:"text-red-800",iconClass:"text-red-700",pulseClass:"animate-pulse"};default:return{borderClass:"",bgClass:"",textClass:"",iconClass:"",pulseClass:""}}}function uT(e,t){if(!e.isOverdue)return!1;const s=(t?new Date().getTime()-t.getTime():1/0)/(1e3*60*60);switch(e.severity){case"slight":return!t||s>=4;case"moderate":return!t||s>=12;case"severe":return!t||s>=6;default:return!1}}function dT(e){switch(e){case"slight":return"low";case"moderate":return"normal";case"severe":return"high";default:return"low"}}function fT(e,t){const n=`Task Overdue: ${e.title}`,r=`This task is ${t.formattedDuration}. ${t.severity==="severe"?"Please address it urgently!":"Please review when possible."}`;return{title:n,message:r}}const Ox=({todo:e,onUpdate:t,onDelete:n})=>{var A;const[r,s]=w.useState(!1),[i,a]=w.useState(!1),[l,c]=w.useState(e.title),[u,d]=w.useState(!1),[h,f]=w.useState(!1),[g,x]=w.useState(!1),{categories:v}=Fo(),S=v.find(R=>R.id===e.category_id),p=e.status==="completed",m=Lk(e),y=m.isOverdue,b=Vk(m.severity),j=w.useCallback(async()=>{if(!u){d(!0);try{const R=await Be.toggleTodoCompletion(e.id,e.status);t==null||t(R)}catch(R){console.error("Failed to toggle todo completion:",R)}finally{d(!1)}}},[e.id,e.status,u,t]),k=w.useCallback(async()=>{if(!u){d(!0);try{await Be.deleteTodo(e.id),n==null||n(e.id),f(!1)}catch(R){console.error("Failed to delete todo:",R)}finally{d(!1)}}},[e.id,u,n]),_=()=>{x(!0)},C=R=>{t==null||t(R),x(!1)},P=w.useCallback(async()=>{if(u||l.trim()===e.title){a(!1);return}d(!0);try{const R=await Be.updateTodo(e.id,{title:l.trim()});t==null||t(R),a(!1)}catch(R){console.error("Failed to update todo:",R),c(e.title)}finally{d(!1)}},[e.id,e.title,l,u,t]),D=()=>{c(e.title),a(!1)},V=()=>{switch(e.status){case"completed":return"text-fa-success";case"in_progress":return"text-fa-info";case"pending":return"text-fa-gray-500";case"cancelled":return"text-fa-error";case"archived":return"text-fa-gray-400";default:return"text-fa-gray-500"}};return o.jsxs(o.Fragment,{children:[o.jsxs("div",{className:`fa-todo-card relative transition-all duration-300 ${p?"opacity-70":""} ${y?`${b.borderClass} ${b.bgClass} ${b.pulseClass}`:""} ${pa(e.priority)&&!y?"ring-2 ring-red-500/30 shadow-lg shadow-red-500/20":""} ${zm(e.priority)&&!pa(e.priority)&&!y?"ring-1 ring-orange-500/20":""}`,onMouseEnter:()=>s(!0),onMouseLeave:()=>s(!1),children:[o.jsxs("div",{className:"flex items-start",children:[o.jsx(E.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:j,disabled:u,className:`flex-shrink-0 w-6 h-6 rounded-full border-2 flex items-center justify-center mr-4 mt-1 transition-all duration-200 ${p?"bg-fa-success border-fa-success text-white":"border-fa-gray-300 hover:border-fa-blue-400"} ${u?"opacity-50 cursor-not-allowed":""}`,children:u?o.jsx("div",{className:"w-3 h-3 border border-current border-t-transparent rounded-full animate-spin"}):p&&o.jsx(Zr,{className:"w-4 h-4"})}),o.jsx("div",{className:"flex-1 min-w-0",children:i?o.jsxs("div",{className:"space-y-2",children:[o.jsx("input",{type:"text",value:l,onChange:R=>c(R.target.value),onKeyDown:R=>{R.key==="Enter"&&P(),R.key==="Escape"&&D()},className:"w-full bg-transparent text-fa-gray-800 focus:outline-none border-b border-fa-blue-300 pb-1",autoFocus:!0,disabled:u}),o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx(E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:P,disabled:u,className:"fa-button-glass text-xs px-2 py-1",children:"Save"}),o.jsx(E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:D,disabled:u,className:"fa-button-glass text-xs px-2 py-1",children:"Cancel"})]})]}):o.jsxs(o.Fragment,{children:[o.jsxs("div",{className:"flex items-start justify-between",children:[o.jsx("h3",{className:`text-lg font-medium flex-1 ${p?"line-through text-fa-gray-500":"text-fa-gray-800"}`,children:e.title}),y&&o.jsxs("div",{className:`flex items-center ml-2 ${b.textClass}`,children:[m.severity==="slight"&&o.jsx(fi,{className:`w-4 h-4 mr-1 ${b.iconClass}`}),m.severity==="moderate"&&o.jsx(cr,{className:`w-4 h-4 mr-1 ${b.iconClass}`}),m.severity==="severe"&&o.jsx(dN,{className:`w-4 h-4 mr-1 ${b.iconClass} ${b.pulseClass}`}),o.jsx("span",{className:"fa-caption font-medium",children:m.formattedDuration})]})]}),e.description&&o.jsx("p",{className:"fa-body text-fa-gray-600 mt-1 line-clamp-2",children:e.description}),o.jsxs("div",{className:"flex items-center space-x-4 mt-2 flex-wrap gap-2",children:[o.jsx("span",{className:`fa-caption px-2 py-1 rounded-full bg-fa-white-glass ${V()}`,children:((A=e.status)==null?void 0:A.replace("_"," "))||"pending"}),S&&o.jsxs("div",{className:"flex items-center space-x-1",children:[o.jsx("div",{className:"w-3 h-3 rounded",style:{backgroundColor:S.color}}),o.jsx("span",{className:"fa-caption text-fa-gray-600",children:S.name})]}),e.tags&&e.tags.length>0&&o.jsxs("div",{className:"flex items-center space-x-1",children:[o.jsx(pr,{className:"w-3 h-3 text-fa-gray-400"}),e.tags.slice(0,3).map((R,X)=>o.jsx("span",{className:"fa-caption bg-fa-blue-100 text-fa-blue-700 px-2 py-1 rounded-full",children:R},X)),e.tags.length>3&&o.jsxs("span",{className:"fa-caption text-fa-gray-400",children:["+",e.tags.length-3]})]}),e.due_date&&o.jsxs("div",{className:`flex items-center ${y?b.textClass:"text-fa-gray-500"}`,children:[o.jsx(Fn,{className:"w-4 h-4 mr-1"}),o.jsx("span",{className:"fa-caption",children:Be.formatDueDate(e.due_date)})]}),o.jsx(Ya,{priority:e.priority,variant:"badge",size:"sm",showLabel:!1,animated:!0})]})]})}),!i&&o.jsxs(E.div,{className:"flex items-center space-x-1 ml-2",initial:{opacity:0,width:0},animate:{opacity:r?1:0,width:r?"auto":0},transition:{duration:.2},children:[o.jsx(E.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:_,disabled:u,className:"p-1 text-fa-gray-400 hover:text-fa-blue-500 disabled:opacity-50",title:"Edit todo",children:o.jsx(hN,{className:"w-4 h-4"})}),o.jsx(E.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:()=>f(!0),disabled:u,className:"p-1 text-fa-gray-400 hover:text-fa-error disabled:opacity-50",title:"Delete todo",children:o.jsx(lx,{className:"w-4 h-4"})})]})]}),zm(e.priority)&&o.jsx("div",{className:"absolute top-0 left-0 w-full rounded-t-2xl",children:o.jsx(Ya,{priority:e.priority,variant:"bar",animated:!0,className:"rounded-t-2xl"})}),pa(e.priority)&&o.jsx("div",{className:"absolute -top-1 -left-1 -right-1 -bottom-1 rounded-2xl bg-gradient-to-r from-red-500/20 to-red-600/20 blur-sm -z-10 animate-pulse"})]}),o.jsx(jx,{isOpen:h,onClose:()=>f(!1),onConfirm:k,title:"Delete Todo",message:`Are you sure you want to delete "${e.title}"? This action cannot be undone.`,confirmText:"Delete",cancelText:"Cancel",variant:"danger",isLoading:u}),o.jsx(Ix,{isOpen:g,onClose:()=>x(!1),onSubmit:C,todo:e,mode:"edit"})]})};function Ik(e,t){const{itemHeight:n,containerHeight:r,overscan:s=5,threshold:i=100}=t,[a,l]=w.useState(0),c=w.useRef(null),u=w.useMemo(()=>e.length*n,[e.length,n]),d=w.useMemo(()=>{const m=Math.max(0,Math.floor(a/n)-s),y=Math.ceil(r/n),b=Math.min(e.length-1,m+y+s*2);return{start:m,end:b}},[a,n,r,s,e.length]),h=w.useMemo(()=>{const m=[];for(let y=d.start;y<=d.end;y++)y>=0&&y<e.length&&m.push({index:y,item:e[y],offsetY:y*n,isVisible:!0});return m},[e,d,n]),f=w.useCallback(m=>{const y=m.currentTarget.scrollTop;l(y)},[]),g=w.useCallback((m,y="start")=>{if(!c.current||m<0||m>=e.length)return;let b;switch(y){case"start":b=m*n;break;case"center":b=m*n-(r-n)/2;break;case"end":b=m*n-r+n;break}b=Math.max(0,Math.min(b,u-r)),c.current.scrollTo({top:b,behavior:"smooth"})},[e.length,n,r,u]),x=w.useCallback(()=>{c.current&&c.current.scrollTo({top:0,behavior:"smooth"})},[]),v=w.useCallback(()=>{c.current&&c.current.scrollTo({top:u-r,behavior:"smooth"})},[u,r]);w.useEffect(()=>{if(!c.current)return;const m=a/Math.max(1,u-r),y=Math.max(0,u-r),b=m*y;Math.abs(b-a)>i&&(c.current.scrollTop=b,l(b))},[e.length,u,r,i]);const S=w.useMemo(()=>({style:{height:r,overflowY:"auto",overflowX:"hidden"},onScroll:f,ref:c}),[r,f]),p=w.useMemo(()=>({style:{height:u,position:"relative"}}),[u]);return{virtualItems:h,totalHeight:u,scrollToIndex:g,scrollToTop:x,scrollToBottom:v,containerProps:S,contentProps:p}}const Kd=120,Ok=5,zx=w.memo(({todo:e,index:t,offsetY:n,onUpdate:r,onDelete:s})=>o.jsx(E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:t*.02,duration:.3},style:{position:"absolute",top:n,left:0,right:0,height:Kd,paddingBottom:12},children:o.jsx(Ox,{todo:e,onUpdate:r,onDelete:s})}));zx.displayName="VirtualizedTodoItem";const $x=({todos:e,onTodoUpdate:t,onTodoDelete:n,containerHeight:r,itemHeight:s=Kd,overscan:i=Ok,className:a="",emptyState:l,loadingState:c,isLoading:u=!1})=>{const d=e.length>50,h=Ik(e,{itemHeight:s,containerHeight:r,overscan:i}),f=w.useCallback(x=>{t(x)},[t]),g=w.useCallback(x=>{n(x)},[n]);return u&&c?o.jsx("div",{className:`flex items-center justify-center ${a}`,style:{height:r},children:c}):e.length===0&&l?o.jsx("div",{className:`flex items-center justify-center ${a}`,style:{height:r},children:l}):e.length===0?o.jsx("div",{className:`flex items-center justify-center ${a}`,style:{height:r},children:o.jsxs("div",{className:"text-center",children:[o.jsx("div",{className:"text-5xl mb-4",children:"🍃"}),o.jsx("h3",{className:"fa-heading-3 mb-2",children:"No tasks found"}),o.jsx("p",{className:"fa-body text-fa-gray-500",children:"Try adjusting your filters or add a new task"})]})}):d?o.jsx("div",{className:a,children:o.jsx("div",{...h.containerProps,children:o.jsx("div",{...h.contentProps,children:h.virtualItems.map(({item:x,index:v,offsetY:S})=>o.jsx(zx,{todo:x,index:v,offsetY:S,onUpdate:f,onDelete:g},x.id))})})}):o.jsx("div",{className:`overflow-y-auto ${a}`,style:{height:r},children:o.jsx("div",{className:"space-y-3 p-1",children:e.map((x,v)=>o.jsx(E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:v*.02,duration:.3},children:o.jsx(Ox,{todo:x,onUpdate:f,onDelete:g})},x.id))})})},zk=w.memo(({onPerformanceMetrics:e,...t})=>{const n=performance.now();return Z.useEffect(()=>{const s=performance.now()-n;e==null||e({renderTime:s,itemCount:t.todos.length,visibleItems:Math.min(t.todos.length,Math.ceil(t.containerHeight/(t.itemHeight||Kd)))})}),o.jsx($x,{...t})});zk.displayName="VirtualizedTodoListWithMetrics";const $k=({isVisible:e,selectedCount:t,totalCount:n,isAllSelected:r,isPartiallySelected:s,onSelectAll:i,onSelectNone:a,operations:l,isProcessing:c,onClose:u,className:d=""})=>{var b,j,k,_,C;const[h,f]=w.useState(!1),[g,x]=w.useState({isOpen:!1}),v=async P=>{f(!1),P.requiresConfirmation?x({isOpen:!0,operation:P}):await S(P)},S=async P=>{try{const D=await P.action([]);console.log("Bulk operation result:",D),D.success&&u()}catch(D){console.error("Bulk operation failed:",D)}},p=async()=>{g.operation&&await S(g.operation),x({isOpen:!1})},m=()=>r?o.jsx(yN,{className:"w-5 h-5 text-fa-blue-600"}):s?o.jsx(_m,{className:"w-5 h-5 text-fa-blue-600 fill-current opacity-50"}):o.jsx(_m,{className:"w-5 h-5 text-fa-gray-400"}),y=()=>{r?a():i()};return e?o.jsxs(o.Fragment,{children:[o.jsx(re,{children:o.jsxs(E.div,{initial:{opacity:0,y:-50,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.95},transition:{type:"spring",damping:25,stiffness:300},className:`fa-glass-panel-frosted border border-fa-blue-200 shadow-lg ${d}`,children:[o.jsxs("div",{className:"flex items-center justify-between p-4",children:[o.jsxs("div",{className:"flex items-center space-x-4",children:[o.jsxs(E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:y,className:"flex items-center space-x-2 text-fa-gray-700 hover:text-fa-blue-600",disabled:c,children:[m(),o.jsx("span",{className:"text-sm font-medium",children:r?"Deselect All":"Select All"})]}),o.jsxs("div",{className:"text-sm text-fa-gray-600",children:[o.jsx("span",{className:"font-medium text-fa-blue-600",children:t})," ","of"," ",o.jsx("span",{className:"font-medium",children:n})," ","selected"]})]}),o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("div",{className:"flex items-center space-x-1",children:l.slice(0,3).map(P=>o.jsxs(E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>v(P),disabled:c||t===0,className:`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-2 ${P.variant==="danger"?"bg-fa-error text-white hover:bg-red-600 disabled:bg-fa-gray-300":P.variant==="warning"?"bg-fa-warning text-white hover:bg-yellow-600 disabled:bg-fa-gray-300":"fa-button-glass disabled:opacity-50"} disabled:cursor-not-allowed`,title:P.description,children:[o.jsx("span",{children:P.icon}),o.jsx("span",{className:"hidden sm:inline",children:P.name})]},P.id))}),l.length>3&&o.jsxs("div",{className:"relative",children:[o.jsx(E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>f(!h),disabled:c||t===0,className:"fa-button-glass p-2 disabled:opacity-50 disabled:cursor-not-allowed",children:o.jsx(sN,{className:"w-4 h-4"})}),o.jsx(re,{children:h&&o.jsx(E.div,{initial:{opacity:0,y:-10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-10,scale:.95},transition:{duration:.2},className:"absolute top-full right-0 mt-2 w-64 fa-glass-panel-frosted border border-fa-gray-200 rounded-xl shadow-lg z-50",children:o.jsx("div",{className:"p-2",children:l.slice(3).map(P=>o.jsxs(E.button,{whileHover:{backgroundColor:"rgba(59, 130, 246, 0.1)"},onClick:()=>v(P),disabled:c,className:`w-full text-left px-3 py-2 rounded-lg transition-colors duration-150 flex items-center space-x-3 disabled:opacity-50 disabled:cursor-not-allowed ${P.variant==="danger"?"text-fa-error hover:bg-red-50":P.variant==="warning"?"text-fa-warning hover:bg-yellow-50":"text-fa-gray-700 hover:bg-fa-gray-50"}`,children:[o.jsx("span",{className:"text-lg",children:P.icon}),o.jsxs("div",{children:[o.jsx("div",{className:"font-medium",children:P.name}),o.jsx("div",{className:"text-xs text-fa-gray-500",children:P.description})]})]},P.id))})})})]}),o.jsx(E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:u,className:"p-2 text-fa-gray-400 hover:text-fa-gray-600 rounded-lg hover:bg-fa-white-glass",disabled:c,children:o.jsx(et,{className:"w-4 h-4"})})]})]}),c&&o.jsx(E.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"border-t border-fa-gray-200 px-4 py-3",children:o.jsxs("div",{className:"flex items-center space-x-3",children:[o.jsx("div",{className:"w-4 h-4 border-2 border-fa-blue-200 border-t-fa-blue-500 rounded-full animate-spin"}),o.jsx("span",{className:"text-sm text-fa-gray-600",children:"Processing bulk operation..."})]})})]})}),o.jsx(jx,{isOpen:g.isOpen,onClose:()=>x({isOpen:!1}),onConfirm:p,title:`${(b=g.operation)==null?void 0:b.name}`,message:((j=g.operation)==null?void 0:j.confirmationMessage)||`Are you sure you want to ${(k=g.operation)==null?void 0:k.name.toLowerCase()} ${t} selected todo${t!==1?"s":""}?`,confirmText:((_=g.operation)==null?void 0:_.name)||"Confirm",cancelText:"Cancel",variant:((C=g.operation)==null?void 0:C.variant)||"info",isLoading:c})]}):null};function sp(e){try{const t=e.match(/^PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?$/);if(!t)return 0;const n=parseInt(t[1]||"0",10),r=parseInt(t[2]||"0",10),s=parseInt(t[3]||"0",10);return n*60+r+Math.round(s/60)}catch{return 0}}function Uk(e,t){const n=new Date,r=24*60*60*1e3,s=h=>{if(!h.due_date)return 0;const x=(new Date(h.due_date).getTime()-n.getTime())/r;return x<0?10:x<1?8:x<3?6:x<7?4:x<30?2:1},i=no[e.priority],a=no[t.priority],l=s(e),c=s(t),u=i*2+l;return a*2+c-u}function Bk(e,t){const n=new Date,r=a=>a.due_date?new Date(a.due_date)<n:!1,s=r(e),i=r(t);return s&&!i?-1:!s&&i?1:!e.due_date&&!t.due_date?0:e.due_date?t.due_date?new Date(e.due_date).getTime()-new Date(t.due_date).getTime():-1:1}const Rl=[{id:"default",name:"Default",config:{field:"position",order:"asc"},icon:"📋",description:"Custom order"},{id:"priority_high",name:"Priority (High to Low)",config:{field:"priority",order:"desc"},icon:"🔥",description:"Most important first"},{id:"due_date_soon",name:"Due Date (Soonest)",config:{field:"due_date",order:"asc"},icon:"⏰",description:"Upcoming deadlines first"},{id:"alphabetical",name:"Alphabetical",config:{field:"title",order:"asc"},icon:"🔤",description:"A to Z"},{id:"recently_created",name:"Recently Created",config:{field:"created_at",order:"desc"},icon:"🆕",description:"Newest first"},{id:"recently_updated",name:"Recently Updated",config:{field:"updated_at",order:"desc"},icon:"📝",description:"Last modified first"},{id:"smart_priority",name:"Smart Priority",config:{field:"priority",order:"desc"},icon:"🧠",description:"Priority with urgency consideration"},{id:"overdue_first",name:"Overdue First",config:{field:"due_date",order:"asc"},icon:"🚨",description:"Overdue and urgent tasks first"}],no={very_high:5,high:4,medium:3,low:2,very_low:1},ip={in_progress:5,pending:4,completed:3,archived:2,cancelled:1};function Hk(e){const[t,n]=w.useState({field:"position",order:"asc"}),[r,s]=w.useState("default"),i=w.useMemo(()=>[...e].sort((x,v)=>{let S=0;if(r==="smart_priority")return Uk(x,v);if(r==="overdue_first")return Bk(x,v);switch(t.field){case"title":S=x.title.localeCompare(v.title);break;case"status":S=ip[x.status]-ip[v.status];break;case"priority":S=no[x.priority]-no[v.priority];break;case"due_date":!x.due_date&&!v.due_date?S=0:x.due_date?v.due_date?S=new Date(x.due_date).getTime()-new Date(v.due_date).getTime():S=-1:S=1;break;case"created_at":S=new Date(x.created_at).getTime()-new Date(v.created_at).getTime();break;case"updated_at":S=new Date(x.updated_at).getTime()-new Date(v.updated_at).getTime();break;case"position":S=x.position-v.position;break;case"estimated_duration":const p=x.estimated_duration?sp(x.estimated_duration):0,m=v.estimated_duration?sp(v.estimated_duration):0;S=p-m;break;default:S=0}return t.order==="desc"?-S:S}),[e,t]),a=w.useCallback((g,x)=>{n(v=>({field:g,order:x||(v.field===g&&v.order==="asc"?"desc":"asc")})),s("")},[]),l=w.useCallback(g=>{const x=Rl.find(v=>v.id===g);x&&(n(x.config),s(g))},[]),c=w.useCallback(()=>{n(g=>({...g,order:g.order==="asc"?"desc":"asc"})),s("")},[]),u=w.useCallback(g=>t.field!==g?null:t.order==="asc"?"↑":"↓",[t]),d=w.useCallback(g=>t.field===g,[t.field]),h=w.useMemo(()=>{const g=Rl.find(S=>S.id===r);if(g)return g.description||g.name;const x=t.field.replace("_"," "),v=t.order==="asc"?"ascending":"descending";return`${x} (${v})`},[t,r]),f=w.useCallback((g,x="asc")=>{console.log("Secondary sort not yet implemented:",g,x)},[]);return{sortConfig:t,sortedTodos:i,updateSort:a,applyPreset:l,toggleSortOrder:c,getSortIndicator:u,isSortedBy:d,sortDescription:h,presets:Rl,activePreset:r,addSecondarySort:f}}const Ll={search:"",status:[],priority:[],tags:[],dueDate:"all",category:[],isCompleted:void 0},ap=[{id:"all",name:"All Tasks",filters:{},icon:"📋"},{id:"active",name:"Active",filters:{status:["pending","in_progress"]},icon:"⚡"},{id:"completed",name:"Completed",filters:{status:["completed"]},icon:"✅"},{id:"overdue",name:"Overdue",filters:{dueDate:"overdue"},icon:"🚨"},{id:"high_priority",name:"High Priority",filters:{priority:["high","very_high"]},icon:"🔥"},{id:"today",name:"Due Today",filters:{dueDate:"today"},icon:"📅"}];function Wk(e,t=[]){const[n,r]=w.useState(Ll),[s,i]=w.useState("all"),a=w.useMemo(()=>e.filter(f=>{var g;if(n.search){const x=n.search.toLowerCase();if(!(f.title.toLowerCase().includes(x)||((g=f.description)==null?void 0:g.toLowerCase().includes(x))||f.tags.some(S=>S.toLowerCase().includes(x))))return!1}if(n.status.length>0&&!n.status.includes(f.status)||n.priority.length>0&&!n.priority.includes(f.priority)||n.tags.length>0&&!n.tags.some(v=>f.tags.some(S=>S.toLowerCase().includes(v.toLowerCase())))||n.category.length>0&&(!f.category_id||!n.category.includes(f.category_id)))return!1;if(n.dueDate!=="all"){const x=new Date,v=new Date(x.getFullYear(),x.getMonth(),x.getDate()),S=new Date(v);S.setDate(S.getDate()+1);const p=new Date(v);p.setDate(p.getDate()+7);const m=new Date(v);switch(m.setMonth(m.getMonth()+1),n.dueDate){case"overdue":if(!f.due_date||new Date(f.due_date)>=v||f.status==="completed")return!1;break;case"today":if(!f.due_date)return!1;const y=new Date(f.due_date);if(y<v||y>=S)return!1;break;case"tomorrow":if(!f.due_date)return!1;const b=new Date(f.due_date);if(b<S||b>=new Date(S.getTime()+24*60*60*1e3))return!1;break;case"this_week":if(!f.due_date||new Date(f.due_date)>p)return!1;break;case"this_month":if(!f.due_date||new Date(f.due_date)>m)return!1;break;case"no_due_date":if(f.due_date)return!1;break}}if(n.isCompleted!==void 0){const x=f.status==="completed";if(n.isCompleted!==x)return!1}return!0}),[e,n]),l=w.useCallback((f,g)=>{r(x=>({...x,[f]:g})),i("")},[]),c=w.useCallback(f=>{const g=ap.find(x=>x.id===f);g&&(r(x=>({...Ll,...x,...g.filters})),i(f))},[]),u=w.useCallback(()=>{r(Ll),i("all")},[]),d=w.useMemo(()=>{const f=[];return n.search&&f.push(`Search: "${n.search}"`),n.status.length>0&&f.push(`Status: ${n.status.join(", ")}`),n.priority.length>0&&f.push(`Priority: ${n.priority.join(", ")}`),n.tags.length>0&&f.push(`Tags: ${n.tags.join(", ")}`),n.dueDate!=="all"&&f.push(`Due: ${n.dueDate.replace("_"," ")}`),n.category.length>0&&f.push(`Category: ${n.category.join(", ")}`),{activeFilters:f,hasActiveFilters:f.length>0,totalFiltered:a.length,totalOriginal:e.length}},[n,a.length,e.length]),h=w.useMemo(()=>{const f=[...new Set(e.map(v=>v.status))],g=[...new Set(e.map(v=>v.priority))],x=[...new Set(e.flatMap(v=>v.tags))];return{statuses:f,priorities:g,tags:x,categories:t}},[e,t]);return{filters:n,filteredTodos:a,updateFilter:l,applyPreset:c,clearFilters:u,filterSummary:d,filterOptions:h,presets:ap,activePreset:s}}function Kk(e,t,n){const[r,s]=w.useState(new Set),[i,a]=w.useState(!1),[l,c]=w.useState(null),u=w.useMemo(()=>e.filter(k=>r.has(k.id)),[e,r]),d=w.useCallback(k=>{s(_=>new Set([..._,k]))},[]),h=w.useCallback(k=>{s(_=>{const C=new Set(_);return C.delete(k),C})},[]),f=w.useCallback(k=>{s(_=>{const C=new Set(_);return C.has(k)?C.delete(k):C.add(k),C})},[]),g=w.useCallback(()=>{s(new Set(e.map(k=>k.id)))},[e]),x=w.useCallback(()=>{s(new Set)},[]),v=w.useCallback(k=>{const _=e.filter(C=>C.status===k).map(C=>C.id);s(new Set(_))},[e]),S=w.useCallback(k=>{const _=e.filter(C=>C.priority===k).map(C=>C.id);s(new Set(_))},[e]),p=w.useCallback(async k=>{const _=Array.from(r),C={success:!0,processedCount:0,failedCount:0,errors:[]};a(!0),c(`update_status_${k}`);try{const P=_.map(async A=>{try{return{success:!0,todo:await Be.updateTodoStatus(A,k)}}catch(R){return{success:!1,error:R instanceof Error?R.message:"Unknown error",todoId:A}}}),D=await Promise.all(P),V=[];D.forEach(A=>{A.success&&"todo"in A?(V.push(A.todo),A.processedCount++):!A.success&&"error"in A&&(A.failedCount++,A.errors.push(`Failed to update todo ${A.todoId}: ${A.error}`))}),t(V),s(new Set),C.processedCount=V.length,C.failedCount=D.length-V.length,C.success=C.failedCount===0}catch(P){C.success=!1,C.errors.push(P instanceof Error?P.message:"Unknown error")}finally{a(!1)}return C},[r,t]),m=w.useCallback(async k=>{const _=Array.from(r),C={success:!0,processedCount:0,failedCount:0,errors:[]};a(!0),c(`update_priority_${k}`);try{const P=_.map(async A=>{try{return{success:!0,todo:await Be.updateTodo(A,{priority:k})}}catch(R){return{success:!1,error:R instanceof Error?R.message:"Unknown error",todoId:A}}}),D=await Promise.all(P),V=[];D.forEach(A=>{A.success&&"todo"in A?(V.push(A.todo),A.processedCount++):!A.success&&"error"in A&&(A.failedCount++,A.errors.push(`Failed to update todo ${A.todoId}: ${A.error}`))}),t(V),s(new Set),C.processedCount=V.length,C.failedCount=D.length-V.length,C.success=C.failedCount===0}catch(P){C.success=!1,C.errors.push(P instanceof Error?P.message:"Unknown error")}finally{a(!1)}return C},[r,t]),y=w.useCallback(async()=>{const k=Array.from(r),_={success:!0,processedCount:0,failedCount:0,errors:[]};a(!0),c("delete");try{const C=k.map(async D=>{try{return await Be.deleteTodo(D),{success:!0,todoId:D}}catch(V){return{success:!1,error:V instanceof Error?V.message:"Unknown error",todoId:D}}}),P=await Promise.all(C);P.forEach(D=>{D.success?(n(D.todoId),D.processedCount++):(D.failedCount++,D.errors.push(`Failed to delete todo ${D.todoId}: ${D.error}`))}),s(new Set),_.processedCount=P.filter(D=>D.success).length,_.failedCount=P.filter(D=>!D.success).length,_.success=_.failedCount===0}catch(C){_.success=!1,_.errors.push(C instanceof Error?C.message:"Unknown error")}finally{a(!1)}return _},[r,n]),b=w.useMemo(()=>[{id:"mark_completed",name:"Mark as Completed",icon:"✅",description:"Mark selected todos as completed",action:()=>p("completed")},{id:"mark_pending",name:"Mark as Pending",icon:"⏳",description:"Mark selected todos as pending",action:()=>p("pending")},{id:"mark_in_progress",name:"Mark as In Progress",icon:"🔄",description:"Mark selected todos as in progress",action:()=>p("in_progress")},{id:"set_high_priority",name:"Set High Priority",icon:"🔥",description:"Set selected todos to high priority",action:()=>m("high")},{id:"set_medium_priority",name:"Set Medium Priority",icon:"⚡",description:"Set selected todos to medium priority",action:()=>m("medium")},{id:"set_low_priority",name:"Set Low Priority",icon:"📝",description:"Set selected todos to low priority",action:()=>m("low")},{id:"delete",name:"Delete",icon:"🗑️",description:"Delete selected todos permanently",action:y,requiresConfirmation:!0,confirmationMessage:"Are you sure you want to delete the selected todos? This action cannot be undone.",variant:"danger"}],[p,m,y]),j=w.useMemo(()=>({selectedCount:r.size,totalCount:e.length,hasSelection:r.size>0,isAllSelected:r.size===e.length&&e.length>0,isPartiallySelected:r.size>0&&r.size<e.length}),[r.size,e.length]);return{selectedTodoIds:r,selectedTodos:u,selectionState:j,selectTodo:d,deselectTodo:h,toggleTodoSelection:f,selectAll:g,selectNone:x,selectByStatus:v,selectByPriority:S,availableOperations:b,bulkUpdateStatus:p,bulkUpdatePriority:m,bulkDelete:y,isProcessing:i,lastOperation:l}}const Gk=({containerHeight:e=600,className:t=""})=>{const{todos:n,categories:r,isLoading:s,error:i,filters:a,selectedTodos:l,loadTodos:c,loadCategories:u,updateTodo:d,deleteTodo:h,setSearchQuery:f,setFilters:g,selectTodo:x,deselectTodo:v,clearSelection:S}=Fo(),{todoViewMode:p,setTodoViewMode:m}=RC(),[y,b]=w.useState(!1),[j,k]=w.useState(new Date),_=VC(a.searchQuery,300),C=w.useCallback(async U=>{try{await d(U.id,U)}catch(L){console.error("Failed to update todo:",L)}},[d]),P=w.useCallback(async U=>{try{await h(U)}catch(L){console.error("Failed to delete todo:",L)}},[h]),D=w.useCallback(async U=>{try{for(const L of U)await d(L.id,L)}catch(L){console.error("Failed to bulk update todos:",L)}},[d]),V=Wk(n,r),A=Hk(V.filteredTodos),R=Kk(n,D,P);w.useEffect(()=>{c(),u()},[c,u]),w.useEffect(()=>{f(_)},[_,f]);const X=w.useCallback(async()=>{try{await c(),k(new Date)}catch(U){console.error("Failed to refresh todos:",U)}},[c]),G=w.useMemo(()=>{const U=new Set;return n.forEach(L=>{L.title.split(" ").forEach(K=>{K.length>2&&U.add(K)}),L.tags.forEach(K=>U.add(K))}),Array.from(U).slice(0,10)},[n]),W=w.useCallback(U=>{m(U)},[m]);return s?o.jsx("div",{className:`w-full flex items-center justify-center ${t}`,style:{height:e},children:o.jsxs("div",{className:"text-center",children:[o.jsx("div",{className:"w-8 h-8 border-4 border-fa-blue-200 border-t-fa-blue-500 rounded-full animate-spin mx-auto mb-4"}),o.jsx("p",{className:"fa-body text-fa-gray-500",children:"Loading todos..."})]})}):i?o.jsx("div",{className:`w-full flex items-center justify-center ${t}`,style:{height:e},children:o.jsxs("div",{className:"text-center",children:[o.jsx("div",{className:"text-5xl mb-4",children:"⚠️"}),o.jsx("h3",{className:"fa-heading-3 mb-2 text-fa-error",children:"Error Loading Todos"}),o.jsx("p",{className:"fa-body text-fa-gray-500 mb-4",children:i}),o.jsx(E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:c,className:"fa-button-primary px-4 py-2",children:"Try Again"})]})}):o.jsxs("div",{className:`w-full h-full flex flex-col ${t}`,children:[o.jsxs("div",{className:"flex-shrink-0 space-y-4 mb-6",children:[o.jsxs("div",{className:"flex items-center space-x-4",children:[o.jsx("div",{className:"flex-1",children:o.jsx(OC,{value:a.searchQuery,onChange:f,onFilterToggle:()=>b(!y),suggestions:G,isFilterActive:Object.values(a).some(U=>Array.isArray(U)?U.length>0:typeof U=="string"?U!=="":typeof U=="object"&&U!==null?Object.keys(U).length>0:!1)})}),o.jsx("div",{className:"flex items-center space-x-1 fa-glass-panel px-2 py-1 rounded-lg",children:["list","grid","kanban"].map(U=>o.jsxs(E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>W(U),className:`p-2 rounded-lg transition-all duration-200 ${p===U?"bg-fa-blue-500 text-white":"text-fa-gray-400 hover:text-fa-gray-600 hover:bg-fa-white-glass"}`,title:`${U.charAt(0).toUpperCase()+U.slice(1)} view`,children:[U==="list"&&o.jsx(sx,{className:"w-4 h-4"}),U==="grid"&&o.jsx(aN,{className:"w-4 h-4"}),U==="kanban"&&o.jsx(tN,{className:"w-4 h-4"})]},U))}),o.jsx(E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:X,disabled:s,className:"fa-button-glass p-2 disabled:opacity-50",title:"Refresh todos",children:o.jsx(lr,{className:`w-4 h-4 ${s?"animate-spin":""}`})})]}),o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsx(KC,{sortConfig:A.sortConfig,onUpdateSort:A.updateSort,onApplyPreset:A.applyPreset,onToggleSortOrder:A.toggleSortOrder,presets:A.presets,activePreset:A.activePreset,sortDescription:A.sortDescription}),o.jsx("div",{className:"text-sm text-fa-gray-500",children:V.filterSummary.hasActiveFilters?o.jsxs("span",{children:[V.filterSummary.totalFiltered," of ",V.filterSummary.totalOriginal," tasks"]}):o.jsxs("span",{children:[n.length," tasks"]})})]})]}),o.jsx(re,{children:R.selectionState.hasSelection&&o.jsx("div",{className:"flex-shrink-0 mb-4",children:o.jsx($k,{isVisible:R.selectionState.hasSelection,selectedCount:R.selectionState.selectedCount,totalCount:R.selectionState.totalCount,isAllSelected:R.selectionState.isAllSelected,isPartiallySelected:R.selectionState.isPartiallySelected,onSelectAll:R.selectAll,onSelectNone:R.selectNone,operations:R.availableOperations,isProcessing:R.isProcessing,onClose:R.selectNone})})}),o.jsx("div",{className:"flex-1 min-h-0",children:o.jsx($x,{todos:A.sortedTodos,onTodoUpdate:C,onTodoDelete:P,containerHeight:e-200,isLoading:s,emptyState:V.filterSummary.hasActiveFilters?o.jsxs("div",{className:"text-center",children:[o.jsx("div",{className:"text-5xl mb-4",children:"🔍"}),o.jsx("h3",{className:"fa-heading-3 mb-2",children:"No matching tasks"}),o.jsx("p",{className:"fa-body text-fa-gray-500 mb-4",children:"Try adjusting your filters or search terms"}),o.jsx(E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:V.clearFilters,className:"fa-button-glass px-4 py-2",children:"Clear Filters"})]}):o.jsxs("div",{className:"text-center",children:[o.jsx("div",{className:"text-5xl mb-4",children:"🍃"}),o.jsx("h3",{className:"fa-heading-3 mb-2",children:"No tasks yet"}),o.jsx("p",{className:"fa-body text-fa-gray-500",children:"Add your first task to get started!"})]})})}),o.jsx(WC,{isOpen:y,onClose:()=>b(!1),filters:V.filters,onUpdateFilter:V.updateFilter,onApplyPreset:V.applyPreset,onClearFilters:V.clearFilters,presets:V.presets,activePreset:V.activePreset,filterOptions:V.filterOptions,filterSummary:V.filterSummary})]})},Qk=({onTodoCreate:e})=>{const[t,n]=w.useState(""),[r,s]=w.useState(!1),[i,a]=w.useState(!1),l=d=>{d.preventDefault(),t.trim()&&a(!0)},c=d=>{e==null||e(d),n(""),a(!1)},u=()=>{a(!0)};return o.jsxs(o.Fragment,{children:[o.jsx("form",{onSubmit:l,className:"w-full",children:o.jsx("div",{className:`fa-glass-panel transition-all duration-300 ${r?"ring-2 ring-fa-blue-400 shadow-lg":"shadow-md"}`,children:o.jsxs("div",{className:"flex items-center",children:[o.jsx(E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},type:"button",onClick:u,className:"p-4 text-fa-blue-500 hover:text-fa-blue-600",children:o.jsx(ko,{className:"w-5 h-5"})}),o.jsx("input",{type:"text",value:t,onChange:d=>n(d.target.value),onFocus:()=>s(!0),onBlur:()=>s(!1),onClick:u,placeholder:"What needs to be done?",className:"flex-1 bg-transparent py-4 px-2 text-fa-gray-800 placeholder-fa-gray-400 focus:outline-none cursor-pointer",readOnly:!0}),o.jsxs("div",{className:"flex items-center space-x-2 pr-4",children:[o.jsx(E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},type:"button",onClick:u,className:"p-2 text-fa-gray-400 hover:text-fa-gray-600",children:o.jsx(Fn,{className:"w-4 h-4"})}),o.jsx(E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},type:"button",onClick:u,className:"p-2 text-fa-gray-400 hover:text-fa-gray-600",children:o.jsx(pr,{className:"w-4 h-4"})})]})]})})}),o.jsx(Ix,{isOpen:i,onClose:()=>a(!1),onSubmit:c,mode:"create"})]})},qk=()=>{const[e,t]=w.useState(!1),{syncStatus:n,queueStatus:r,networkStatus:s,isLoadingStatus:i,toggleSyncPanel:a,performSync:l}=LC(),c=()=>{if(!(s!=null&&s.isOnline))return o.jsx(Gc,{className:"h-4 w-4 text-fa-error"});switch(n.status){case"syncing":return o.jsx(lr,{className:"h-4 w-4 text-fa-info animate-spin"});case"idle":return r.pendingOperations>0?o.jsx(Jr,{className:"h-4 w-4 text-fa-warning"}):o.jsx(Zr,{className:"h-4 w-4 text-fa-success"});case"error":return o.jsx(fi,{className:"h-4 w-4 text-fa-error"});case"offline":return o.jsx(Gc,{className:"h-4 w-4 text-fa-gray-400"});default:return o.jsx(Jr,{className:"h-4 w-4 text-fa-gray-400"})}},u=()=>{if(!(s!=null&&s.isOnline))return"Offline";switch(n.status){case"syncing":return n.syncProgress?`Syncing ${n.syncProgress.current}/${n.syncProgress.total}`:"Syncing...";case"idle":return r.pendingOperations>0?`${r.pendingOperations} pending`:"Up to date";case"error":return"Sync error";case"offline":return"Offline";default:return"Unknown"}},d=()=>{if(!(s!=null&&s.isOnline))return"text-fa-error";switch(n.status){case"syncing":return"text-fa-info";case"idle":return r.pendingOperations>0?"text-fa-warning":"text-fa-success";case"error":return"text-fa-error";case"offline":return"text-fa-gray-400";default:return"text-fa-gray-400"}},h=async()=>{if(n.status!=="syncing"&&(s!=null&&s.isOnline))try{await l()}catch(f){console.error("Manual sync failed:",f)}};return o.jsxs("div",{className:"relative",children:[o.jsxs(E.div,{className:"flex items-center space-x-2 px-3 py-2 rounded-lg fa-glass-panel-subtle hover:fa-glass-panel-frosted transition-all duration-200 cursor-pointer",onClick:()=>t(!e),whileHover:{scale:1.02},whileTap:{scale:.98},children:[o.jsxs("div",{className:"flex items-center space-x-2",children:[c(),o.jsx("span",{className:`fa-text-sm font-medium ${d()}`,children:u()})]}),o.jsx(Rt,{className:`h-3 w-3 text-fa-gray-500 transition-transform duration-200 ${e?"rotate-180":""}`})]}),o.jsx(re,{children:e&&o.jsx(E.div,{initial:{opacity:0,y:-10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-10,scale:.95},transition:{duration:.2},className:"absolute top-full right-0 mt-2 w-80 fa-glass-panel-frosted border border-fa-border rounded-lg shadow-lg z-50",children:o.jsxs("div",{className:"p-4",children:[o.jsxs("div",{className:"flex items-center justify-between mb-4",children:[o.jsx("h3",{className:"fa-heading-4 text-fa-gray-800",children:"Sync Status"}),o.jsx("button",{onClick:h,disabled:n.status==="syncing"||!(s!=null&&s.isOnline),className:"p-2 rounded-lg fa-glass-panel-subtle hover:fa-glass-panel-frosted transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:o.jsx(lr,{className:`h-4 w-4 text-fa-info ${n.status==="syncing"?"animate-spin":""}`})})]}),o.jsxs("div",{className:"mb-4",children:[o.jsxs("div",{className:"flex items-center justify-between mb-2",children:[o.jsx("span",{className:"fa-text-sm font-medium text-fa-gray-700",children:"Network"}),o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("div",{className:`w-2 h-2 rounded-full ${s!=null&&s.isOnline?"bg-fa-success":"bg-fa-error"}`}),o.jsx("span",{className:"fa-text-xs text-fa-gray-600",children:(s==null?void 0:s.connectionQuality)||"Unknown"})]})]}),(s==null?void 0:s.latency)&&o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx(Xy,{className:"h-3 w-3 text-fa-gray-500"}),o.jsxs("span",{className:"fa-text-xs text-fa-gray-600",children:[s.latency,"ms latency"]})]})]}),o.jsxs("div",{className:"mb-4",children:[o.jsxs("div",{className:"flex items-center justify-between mb-2",children:[o.jsx("span",{className:"fa-text-sm font-medium text-fa-gray-700",children:"Queue"}),o.jsxs("span",{className:"fa-text-xs text-fa-gray-600",children:[r.totalOperations," total"]})]}),o.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[o.jsxs("div",{className:"fa-glass-panel-subtle rounded-lg p-2",children:[o.jsx("div",{className:"fa-text-xs text-fa-gray-600",children:"Pending"}),o.jsx("div",{className:"fa-text-sm font-semibold text-fa-warning",children:r.pendingOperations})]}),o.jsxs("div",{className:"fa-glass-panel-subtle rounded-lg p-2",children:[o.jsx("div",{className:"fa-text-xs text-fa-gray-600",children:"Failed"}),o.jsx("div",{className:"fa-text-sm font-semibold text-fa-error",children:r.failedOperations})]})]})]}),n.lastSyncAt&&o.jsx("div",{className:"mb-4",children:o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsx("span",{className:"fa-text-sm font-medium text-fa-gray-700",children:"Last Sync"}),o.jsx("span",{className:"fa-text-xs text-fa-gray-600",children:new Date(n.lastSyncAt).toLocaleTimeString()})]})}),n.errorMessage&&o.jsx("div",{className:"mb-4 p-3 rounded-lg bg-fa-error-bg border border-fa-error-border",children:o.jsxs("div",{className:"flex items-start space-x-2",children:[o.jsx(fi,{className:"h-4 w-4 text-fa-error mt-0.5 flex-shrink-0"}),o.jsxs("div",{children:[o.jsx("div",{className:"fa-text-sm font-medium text-fa-error mb-1",children:"Sync Error"}),o.jsx("div",{className:"fa-text-xs text-fa-error-text",children:n.errorMessage})]})]})}),n.syncProgress&&o.jsxs("div",{className:"mb-4",children:[o.jsxs("div",{className:"flex items-center justify-between mb-2",children:[o.jsx("span",{className:"fa-text-sm font-medium text-fa-gray-700",children:n.syncProgress.operation}),o.jsxs("span",{className:"fa-text-xs text-fa-gray-600",children:[n.syncProgress.current,"/",n.syncProgress.total]})]}),o.jsx("div",{className:"w-full bg-fa-gray-200 rounded-full h-2",children:o.jsx(E.div,{className:"bg-fa-info h-2 rounded-full",initial:{width:0},animate:{width:`${n.syncProgress.current/n.syncProgress.total*100}%`},transition:{duration:.3}})})]}),o.jsxs("div",{className:"flex space-x-2",children:[o.jsx("button",{onClick:a,className:"flex-1 px-3 py-2 fa-glass-panel-subtle hover:fa-glass-panel-frosted rounded-lg fa-text-sm font-medium text-fa-gray-700 transition-all duration-200",children:"View Details"}),r.conflictOperations>0&&o.jsxs("button",{onClick:a,className:"flex-1 px-3 py-2 bg-fa-warning hover:bg-fa-warning-hover rounded-lg fa-text-sm font-medium text-white transition-all duration-200 flex items-center justify-center space-x-1",children:[o.jsx(To,{className:"h-3 w-3"}),o.jsx("span",{children:"Resolve Conflicts"})]})]})]})})})]})},Xk=()=>{const{activeConflict:e,showConflictDialog:t,isResolvingConflict:n,hideConflictResolution:r,resolveConflict:s,resolveConflictManually:i}=bx(),[a,l]=w.useState("merge"),[c,u]=w.useState([]),[d,h]=w.useState(!1);Z.useEffect(()=>{if(e){const p=e.localData||{},m=e.remoteData||{},y=new Set([...Object.keys(p),...Object.keys(m)]),b=Array.from(y).map(j=>{const k=p[j],_=m[j],C=JSON.stringify(k)!==JSON.stringify(_);return{field:j,localValue:k,remoteValue:_,isDifferent:C,selectedValue:"local"}});u(b)}},[e]);const f=w.useMemo(()=>{if(!e)return{};const p={};return c.forEach(m=>{switch(m.selectedValue){case"local":p[m.field]=m.localValue;break;case"remote":p[m.field]=m.remoteValue;break;case"custom":p[m.field]=m.customValue;break}}),p},[c,e]),g=(p,m,y)=>{u(b=>b.map((j,k)=>k===p?{...j,selectedValue:m,customValue:y}:j))},x=async()=>{if(e)try{switch(a){case"local":await s(e.id,"local");break;case"remote":await s(e.id,"remote");break;case"merge":await i(e.id,f);break}}catch(p){console.error("Failed to resolve conflict:",p)}},v=p=>p==null?"null":typeof p=="string"?p:typeof p=="boolean"||typeof p=="number"?p.toString():Array.isArray(p)?p.join(", "):typeof p=="object"?JSON.stringify(p,null,2):String(p),S=p=>{switch(p){case"update-update":return"text-fa-warning";case"update-delete":return"text-fa-error";case"create-create":return"text-fa-info";default:return"text-fa-gray-600"}};return!t||!e?null:o.jsx(re,{children:o.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4",children:o.jsxs(E.div,{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:20},transition:{type:"spring",damping:25,stiffness:300},className:"fa-glass-panel-frosted border border-fa-border rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",onClick:p=>p.stopPropagation(),children:[o.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-fa-border",children:[o.jsxs("div",{className:"flex items-center space-x-3",children:[o.jsx("div",{className:"p-2 rounded-full bg-fa-warning-bg",children:o.jsx(cr,{className:"w-5 h-5 text-fa-warning"})}),o.jsxs("div",{children:[o.jsx("h2",{className:"fa-heading-3 text-fa-gray-800",children:"Resolve Sync Conflict"}),o.jsxs("p",{className:"fa-text-sm text-fa-gray-600",children:[e.tableName," • ",e.recordId]})]})]}),o.jsx("button",{onClick:r,className:"p-2 rounded-lg fa-glass-panel-subtle hover:fa-glass-panel-frosted transition-all duration-200",children:o.jsx(et,{className:"w-5 h-5 text-fa-gray-600"})})]}),o.jsxs("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-200px)]",children:[o.jsxs("div",{className:"mb-6 p-4 rounded-lg fa-glass-panel-subtle",children:[o.jsxs("div",{className:"flex items-center justify-between mb-3",children:[o.jsx("span",{className:"fa-text-sm font-medium text-fa-gray-700",children:"Conflict Type"}),o.jsx("span",{className:`fa-text-sm font-semibold ${S(e.conflictType)}`,children:e.conflictType.replace("-"," → ")})]}),e.recommendations.length>0&&o.jsxs("div",{children:[o.jsx("span",{className:"fa-text-sm font-medium text-fa-gray-700 block mb-2",children:"Recommendations"}),o.jsx("ul",{className:"space-y-1",children:e.recommendations.map((p,m)=>o.jsxs("li",{className:"fa-text-xs text-fa-gray-600 flex items-start space-x-2",children:[o.jsx("span",{className:"text-fa-info",children:"•"}),o.jsx("span",{children:p})]},m))})]})]}),o.jsxs("div",{className:"mb-6",children:[o.jsx("h3",{className:"fa-heading-4 text-fa-gray-800 mb-3",children:"Resolution Strategy"}),o.jsxs("div",{className:"grid grid-cols-3 gap-3",children:[o.jsxs("button",{onClick:()=>l("local"),className:`p-4 rounded-lg border-2 transition-all duration-200 ${a==="local"?"border-fa-info bg-fa-info-bg":"border-fa-border fa-glass-panel-subtle hover:fa-glass-panel-frosted"}`,children:[o.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[o.jsx($t,{className:"w-4 h-4 text-fa-info"}),o.jsx("span",{className:"fa-text-sm font-medium text-fa-gray-800",children:"Keep Local"})]}),o.jsx("p",{className:"fa-text-xs text-fa-gray-600",children:"Use your local changes"})]}),o.jsxs("button",{onClick:()=>l("remote"),className:`p-4 rounded-lg border-2 transition-all duration-200 ${a==="remote"?"border-fa-info bg-fa-info-bg":"border-fa-border fa-glass-panel-subtle hover:fa-glass-panel-frosted"}`,children:[o.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[o.jsx(Cm,{className:"w-4 h-4 text-fa-info"}),o.jsx("span",{className:"fa-text-sm font-medium text-fa-gray-800",children:"Keep Remote"})]}),o.jsx("p",{className:"fa-text-xs text-fa-gray-600",children:"Use remote changes"})]}),o.jsxs("button",{onClick:()=>l("merge"),className:`p-4 rounded-lg border-2 transition-all duration-200 ${a==="merge"?"border-fa-info bg-fa-info-bg":"border-fa-border fa-glass-panel-subtle hover:fa-glass-panel-frosted"}`,children:[o.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[o.jsx(lN,{className:"w-4 h-4 text-fa-info"}),o.jsx("span",{className:"fa-text-sm font-medium text-fa-gray-800",children:"Merge"})]}),o.jsx("p",{className:"fa-text-xs text-fa-gray-600",children:"Combine both changes"})]})]})]}),a==="merge"&&o.jsxs("div",{className:"mb-6",children:[o.jsxs("div",{className:"flex items-center justify-between mb-3",children:[o.jsx("h3",{className:"fa-heading-4 text-fa-gray-800",children:"Field Comparison"}),o.jsxs("button",{onClick:()=>h(!d),className:"fa-text-sm text-fa-info hover:text-fa-info-hover transition-colors duration-200",children:[d?"Hide":"Show"," Advanced"]})]}),o.jsx("div",{className:"space-y-3",children:c.filter(p=>d||p.isDifferent).map((p,m)=>o.jsxs("div",{className:"fa-glass-panel-subtle rounded-lg p-4",children:[o.jsxs("div",{className:"flex items-center justify-between mb-3",children:[o.jsx("span",{className:"fa-text-sm font-medium text-fa-gray-800",children:p.field}),p.isDifferent&&o.jsx("span",{className:"px-2 py-1 rounded-full bg-fa-warning-bg text-fa-warning fa-text-xs font-medium",children:"Conflict"})]}),o.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[o.jsxs("div",{className:`p-3 rounded-lg border-2 transition-all duration-200 cursor-pointer ${p.selectedValue==="local"?"border-fa-info bg-fa-info-bg":"border-fa-border hover:border-fa-info-border"}`,onClick:()=>g(m,"local"),children:[o.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[o.jsx($t,{className:"w-3 h-3 text-fa-info"}),o.jsx("span",{className:"fa-text-xs font-medium text-fa-gray-700",children:"Local"})]}),o.jsx("div",{className:"fa-text-sm text-fa-gray-800 font-mono bg-fa-white-glass rounded p-2",children:v(p.localValue)})]}),o.jsxs("div",{className:`p-3 rounded-lg border-2 transition-all duration-200 cursor-pointer ${p.selectedValue==="remote"?"border-fa-info bg-fa-info-bg":"border-fa-border hover:border-fa-info-border"}`,onClick:()=>g(m,"remote"),children:[o.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[o.jsx(Cm,{className:"w-3 h-3 text-fa-info"}),o.jsx("span",{className:"fa-text-xs font-medium text-fa-gray-700",children:"Remote"})]}),o.jsx("div",{className:"fa-text-sm text-fa-gray-800 font-mono bg-fa-white-glass rounded p-2",children:v(p.remoteValue)})]})]})]},p.field))})]})]}),o.jsxs("div",{className:"flex items-center justify-between p-6 border-t border-fa-border",children:[o.jsxs("div",{className:"flex items-center space-x-2 text-fa-gray-600",children:[o.jsx(Jr,{className:"w-4 h-4"}),o.jsxs("span",{className:"fa-text-sm",children:["Created ",new Date(e.createdAt).toLocaleString()]})]}),o.jsxs("div",{className:"flex space-x-3",children:[o.jsx("button",{onClick:r,className:"px-4 py-2 fa-glass-panel-subtle hover:fa-glass-panel-frosted rounded-lg fa-text-sm font-medium text-fa-gray-700 transition-all duration-200",children:"Cancel"}),o.jsx("button",{onClick:x,disabled:n,className:"px-4 py-2 bg-fa-info hover:bg-fa-info-hover disabled:opacity-50 disabled:cursor-not-allowed rounded-lg fa-text-sm font-medium text-white transition-all duration-200 flex items-center space-x-2",children:n?o.jsxs(o.Fragment,{children:[o.jsx(E.div,{animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"},children:o.jsx(lr,{className:"w-4 h-4"})}),o.jsx("span",{children:"Resolving..."})]}):o.jsxs(o.Fragment,{children:[o.jsx(Zr,{className:"w-4 h-4"}),o.jsx("span",{children:"Resolve Conflict"})]})})]})]})]})})})},Yk=()=>{const[e,t]=w.useState("status"),{syncStatus:n,queueStatus:r,networkStatus:s,showSyncPanel:i,toggleSyncPanel:a,performSync:l,forcePushChanges:c,forcePullChanges:u,clearSyncQueue:d,autoSyncEnabled:h,syncInterval:f,setAutoSync:g,setSyncInterval:x}=Id(),{conflicts:v,showConflictResolution:S}=bx(),p=async()=>{try{await l()}catch(j){console.error("Sync failed:",j)}},m=async()=>{try{await c()}catch(j){console.error("Force push failed:",j)}},y=async()=>{try{await u()}catch(j){console.error("Force pull failed:",j)}},b=async()=>{try{await d()}catch(j){console.error("Clear queue failed:",j)}};return i?o.jsx(re,{children:o.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-40 p-4",children:o.jsxs(E.div,{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:20},transition:{type:"spring",damping:25,stiffness:300},className:"fa-glass-panel-frosted border border-fa-border rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",onClick:j=>j.stopPropagation(),children:[o.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-fa-border",children:[o.jsx("h2",{className:"fa-heading-3 text-fa-gray-800",children:"Sync Management"}),o.jsx("button",{onClick:a,className:"p-2 rounded-lg fa-glass-panel-subtle hover:fa-glass-panel-frosted transition-all duration-200",children:o.jsx(et,{className:"w-5 h-5 text-fa-gray-600"})})]}),o.jsx("div",{className:"flex border-b border-fa-border",children:[{id:"status",label:"Status",icon:Xy},{id:"queue",label:"Queue",icon:Jr},{id:"conflicts",label:"Conflicts",icon:cr,badge:v.length},{id:"settings",label:"Settings",icon:ax}].map(j=>o.jsxs("button",{onClick:()=>t(j.id),className:`flex items-center space-x-2 px-6 py-3 border-b-2 transition-all duration-200 ${e===j.id?"border-fa-info text-fa-info":"border-transparent text-fa-gray-600 hover:text-fa-gray-800"}`,children:[o.jsx(j.icon,{className:"w-4 h-4"}),o.jsx("span",{className:"fa-text-sm font-medium",children:j.label}),j.badge!==void 0&&j.badge>0&&o.jsx("span",{className:"px-2 py-1 rounded-full bg-fa-error text-white fa-text-xs font-medium",children:j.badge})]},j.id))}),o.jsxs("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-200px)]",children:[e==="status"&&o.jsxs("div",{className:"space-y-6",children:[o.jsxs("div",{className:"fa-glass-panel-subtle rounded-lg p-4",children:[o.jsxs("div",{className:"flex items-center justify-between mb-4",children:[o.jsx("h3",{className:"fa-heading-4 text-fa-gray-800",children:"Network Status"}),o.jsxs("div",{className:"flex items-center space-x-2",children:[s!=null&&s.isOnline?o.jsx(wN,{className:"w-4 h-4 text-fa-success"}):o.jsx(Gc,{className:"w-4 h-4 text-fa-error"}),o.jsx("span",{className:`fa-text-sm font-medium ${s!=null&&s.isOnline?"text-fa-success":"text-fa-error"}`,children:s!=null&&s.isOnline?"Online":"Offline"})]})]}),s&&o.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[o.jsxs("div",{children:[o.jsx("span",{className:"fa-text-xs text-fa-gray-600",children:"Connection Quality"}),o.jsx("div",{className:`fa-text-sm font-semibold ${s.connectionQuality==="excellent"?"text-fa-success":s.connectionQuality==="good"?"text-fa-info":s.connectionQuality==="poor"?"text-fa-warning":"text-fa-error"}`,children:s.connectionQuality})]}),o.jsxs("div",{children:[o.jsx("span",{className:"fa-text-xs text-fa-gray-600",children:"Latency"}),o.jsxs("div",{className:"fa-text-sm font-semibold text-fa-gray-800",children:[s.latency,"ms"]})]})]})]}),o.jsxs("div",{className:"fa-glass-panel-subtle rounded-lg p-4",children:[o.jsxs("div",{className:"flex items-center justify-between mb-4",children:[o.jsx("h3",{className:"fa-heading-4 text-fa-gray-800",children:"Sync Status"}),o.jsxs("div",{className:"flex items-center space-x-2",children:[n.status==="syncing"?o.jsx(lr,{className:"w-4 h-4 text-fa-info animate-spin"}):n.status==="idle"?o.jsx(Qt,{className:"w-4 h-4 text-fa-success"}):o.jsx(cr,{className:"w-4 h-4 text-fa-error"}),o.jsx("span",{className:`fa-text-sm font-medium ${n.status==="syncing"?"text-fa-info":n.status==="idle"?"text-fa-success":"text-fa-error"}`,children:n.status})]})]}),n.lastSyncAt&&o.jsxs("div",{className:"mb-4",children:[o.jsx("span",{className:"fa-text-xs text-fa-gray-600",children:"Last Sync"}),o.jsx("div",{className:"fa-text-sm text-fa-gray-800",children:new Date(n.lastSyncAt).toLocaleString()})]}),n.errorMessage&&o.jsx("div",{className:"p-3 rounded-lg bg-fa-error-bg border border-fa-error-border",children:o.jsx("div",{className:"fa-text-sm text-fa-error",children:n.errorMessage})}),o.jsxs("div",{className:"flex space-x-3 mt-4",children:[o.jsxs("button",{onClick:p,disabled:n.status==="syncing"||!(s!=null&&s.isOnline),className:"flex items-center space-x-2 px-4 py-2 bg-fa-info hover:bg-fa-info-hover disabled:opacity-50 disabled:cursor-not-allowed rounded-lg fa-text-sm font-medium text-white transition-all duration-200",children:[o.jsx(lr,{className:`w-4 h-4 ${n.status==="syncing"?"animate-spin":""}`}),o.jsx("span",{children:"Sync Now"})]}),o.jsxs("button",{onClick:m,disabled:n.status==="syncing"||!(s!=null&&s.isOnline),className:"flex items-center space-x-2 px-4 py-2 fa-glass-panel-subtle hover:fa-glass-panel-frosted disabled:opacity-50 disabled:cursor-not-allowed rounded-lg fa-text-sm font-medium text-fa-gray-700 transition-all duration-200",children:[o.jsx(vN,{className:"w-4 h-4"}),o.jsx("span",{children:"Force Push"})]}),o.jsxs("button",{onClick:y,disabled:n.status==="syncing"||!(s!=null&&s.isOnline),className:"flex items-center space-x-2 px-4 py-2 fa-glass-panel-subtle hover:fa-glass-panel-frosted disabled:opacity-50 disabled:cursor-not-allowed rounded-lg fa-text-sm font-medium text-fa-gray-700 transition-all duration-200",children:[o.jsx(rN,{className:"w-4 h-4"}),o.jsx("span",{children:"Force Pull"})]})]})]})]}),e==="queue"&&o.jsx("div",{className:"space-y-6",children:o.jsxs("div",{className:"fa-glass-panel-subtle rounded-lg p-4",children:[o.jsxs("div",{className:"flex items-center justify-between mb-4",children:[o.jsx("h3",{className:"fa-heading-4 text-fa-gray-800",children:"Operation Queue"}),o.jsxs("button",{onClick:b,disabled:r.totalOperations===0,className:"flex items-center space-x-2 px-3 py-2 text-fa-error hover:bg-fa-error-bg disabled:opacity-50 disabled:cursor-not-allowed rounded-lg fa-text-sm font-medium transition-all duration-200",children:[o.jsx(lx,{className:"w-4 h-4"}),o.jsx("span",{children:"Clear Queue"})]})]}),o.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[o.jsxs("div",{className:"text-center p-3 rounded-lg fa-glass-panel-subtle",children:[o.jsx("div",{className:"fa-text-2xl font-bold text-fa-info",children:r.totalOperations}),o.jsx("div",{className:"fa-text-xs text-fa-gray-600",children:"Total"})]}),o.jsxs("div",{className:"text-center p-3 rounded-lg fa-glass-panel-subtle",children:[o.jsx("div",{className:"fa-text-2xl font-bold text-fa-warning",children:r.pendingOperations}),o.jsx("div",{className:"fa-text-xs text-fa-gray-600",children:"Pending"})]}),o.jsxs("div",{className:"text-center p-3 rounded-lg fa-glass-panel-subtle",children:[o.jsx("div",{className:"fa-text-2xl font-bold text-fa-error",children:r.failedOperations}),o.jsx("div",{className:"fa-text-xs text-fa-gray-600",children:"Failed"})]}),o.jsxs("div",{className:"text-center p-3 rounded-lg fa-glass-panel-subtle",children:[o.jsx("div",{className:"fa-text-2xl font-bold text-fa-warning",children:r.conflictOperations}),o.jsx("div",{className:"fa-text-xs text-fa-gray-600",children:"Conflicts"})]})]})]})}),e==="conflicts"&&o.jsx("div",{className:"space-y-4",children:v.length===0?o.jsxs("div",{className:"text-center py-8",children:[o.jsx(Qt,{className:"w-12 h-12 text-fa-success mx-auto mb-4"}),o.jsx("h3",{className:"fa-heading-4 text-fa-gray-800 mb-2",children:"No Conflicts"}),o.jsx("p",{className:"fa-text-sm text-fa-gray-600",children:"All changes are synchronized successfully."})]}):v.map(j=>o.jsxs("div",{className:"fa-glass-panel-subtle rounded-lg p-4",children:[o.jsxs("div",{className:"flex items-center justify-between mb-3",children:[o.jsxs("div",{children:[o.jsxs("h4",{className:"fa-text-sm font-medium text-fa-gray-800",children:[j.tableName," • ",j.recordId]}),o.jsxs("p",{className:"fa-text-xs text-fa-gray-600",children:[j.conflictType," • ",new Date(j.createdAt).toLocaleString()]})]}),o.jsx("button",{onClick:()=>S(j),className:"px-3 py-2 bg-fa-warning hover:bg-fa-warning-hover rounded-lg fa-text-sm font-medium text-white transition-all duration-200",children:"Resolve"})]}),j.recommendations.length>0&&o.jsxs("div",{className:"mt-3",children:[o.jsx("p",{className:"fa-text-xs text-fa-gray-600 mb-1",children:"Recommendations:"}),o.jsx("ul",{className:"space-y-1",children:j.recommendations.slice(0,2).map((k,_)=>o.jsxs("li",{className:"fa-text-xs text-fa-gray-600 flex items-start space-x-2",children:[o.jsx("span",{className:"text-fa-info",children:"•"}),o.jsx("span",{children:k})]},_))})]})]},j.id))}),e==="settings"&&o.jsx("div",{className:"space-y-6",children:o.jsxs("div",{className:"fa-glass-panel-subtle rounded-lg p-4",children:[o.jsx("h3",{className:"fa-heading-4 text-fa-gray-800 mb-4",children:"Sync Settings"}),o.jsxs("div",{className:"space-y-4",children:[o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsxs("div",{children:[o.jsx("span",{className:"fa-text-sm font-medium text-fa-gray-800",children:"Auto Sync"}),o.jsx("p",{className:"fa-text-xs text-fa-gray-600",children:"Automatically sync changes when online"})]}),o.jsx("button",{onClick:()=>g(!h),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ${h?"bg-fa-info":"bg-fa-gray-300"}`,children:o.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ${h?"translate-x-6":"translate-x-1"}`})})]}),o.jsxs("div",{children:[o.jsx("label",{className:"fa-text-sm font-medium text-fa-gray-800 block mb-2",children:"Sync Interval (seconds)"}),o.jsx("input",{type:"number",min:"5",max:"300",value:f/1e3,onChange:j=>x(parseInt(j.target.value)*1e3),className:"w-full px-3 py-2 fa-glass-panel-subtle border border-fa-border rounded-lg fa-text-sm focus:outline-none focus:ring-2 focus:ring-fa-info focus:border-transparent"})]})]})]})})]})]})})}):null},Zk=({systemInfo:e})=>{const{theme:t,toggleTheme:n}=kN(),[r,s]=w.useState(!0);return o.jsxs("div",{className:"w-full h-full flex flex-col bg-transparent",children:[o.jsx(EN,{systemInfo:e,onToggleTheme:n,theme:t}),o.jsxs("div",{className:"flex flex-1 overflow-hidden",children:[o.jsx(E.div,{className:`h-full ${r?"w-64":"w-0"}`,animate:{width:r?256:0},transition:{duration:.3,ease:[.4,0,.2,1]},children:o.jsx(PN,{})}),o.jsx("div",{className:"flex-1 flex flex-col overflow-hidden p-6",children:o.jsxs("div",{className:"fa-glass-panel-frosted flex-1 flex flex-col rounded-2xl p-6",children:[o.jsxs("div",{className:"mb-6 flex items-center justify-between",children:[o.jsxs("div",{children:[o.jsx("h1",{className:"fa-heading-1 mb-2",children:"My Tasks"}),o.jsx("p",{className:"fa-body text-fa-gray-600",children:"Stay organized and productive"})]}),o.jsx(qk,{})]}),o.jsx("div",{className:"mb-6",children:o.jsx(Qk,{})}),o.jsx("div",{className:"flex-1 overflow-hidden",children:o.jsx(Gk,{})})]})})]}),o.jsx(Yk,{}),o.jsx(Xk,{})]})},Jk=()=>o.jsx("div",{className:"w-full h-full flex items-center justify-center bg-gradient-to-br from-fa-blue-50 to-fa-aqua-50",children:o.jsxs("div",{className:"text-center",children:[o.jsx(E.div,{className:"w-16 h-16 mx-auto mb-6 rounded-full border-4 border-fa-blue-500 border-t-transparent",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}}),o.jsx(E.h1,{className:"fa-heading-1 mb-2",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:"Modern Todo"}),o.jsx(E.p,{className:"fa-caption text-fa-gray-600",initial:{opacity:0},animate:{opacity:1},transition:{delay:.4},children:"Loading your productivity experience..."})]})});class eT extends w.Component{constructor(){super(...arguments);Ht(this,"state",{hasError:!1,error:void 0})}static getDerivedStateFromError(n){return{hasError:!0,error:n}}componentDidCatch(n,r){console.error("Uncaught error:",n,r)}render(){var n;return this.state.hasError?o.jsx("div",{className:"w-full h-full flex items-center justify-center p-6",children:o.jsxs(E.div,{className:"fa-glass-panel-frosted p-8 max-w-md w-full text-center",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:[o.jsx("div",{className:"text-fa-error text-5xl mb-4",children:"⚠️"}),o.jsx("h2",{className:"fa-heading-2 mb-4",children:"Something went wrong"}),o.jsx("p",{className:"fa-body text-fa-gray-600 mb-6",children:((n=this.state.error)==null?void 0:n.message)||"An unexpected error occurred."}),o.jsx("button",{className:"fa-button-primary px-6 py-3 rounded-lg font-medium",onClick:()=>window.location.reload(),children:"Reload Application"})]})}):this.props.children}}const tT=({onSwitchToRegister:e,onSwitchToReset:t})=>{const[n,r]=w.useState(""),[s,i]=w.useState(""),[a,l]=w.useState(!1),[c,u]=w.useState(!1),[d,h]=w.useState(null),{login:f}=ls(),g=async x=>{x.preventDefault(),u(!0),h(null);try{await f(n,s)}catch(v){h(v instanceof Error?v.message:"Login failed")}finally{u(!1)}};return o.jsx(E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"w-full max-w-md",children:o.jsxs("div",{className:"fa-glass-panel-frosted rounded-2xl p-8 shadow-xl",children:[o.jsxs("div",{className:"text-center mb-8",children:[o.jsx(E.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"w-16 h-16 bg-gradient-to-br from-blue-400 to-aqua-400 rounded-full flex items-center justify-center mx-auto mb-4",children:o.jsx($t,{className:"w-8 h-8 text-white"})}),o.jsx("h2",{className:"fa-heading-2 text-gray-800 mb-2",children:"Welcome Back"}),o.jsx("p",{className:"fa-body text-gray-600",children:"Sign in to your account"})]}),d&&o.jsx(E.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-6 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm",children:d}),o.jsxs(E.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{delay:.3},className:"mb-6 p-3 bg-blue-50 border border-blue-200 rounded-lg text-blue-700 text-sm",children:[o.jsxs("div",{className:"flex items-center justify-between mb-2",children:[o.jsx("div",{className:"font-medium",children:"🧪 Development Testing Account"}),o.jsx("button",{type:"button",onClick:()=>{r("GOD"),i("123456")},className:"text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700 transition-colors",children:"Quick Fill"})]}),o.jsxs("div",{className:"text-xs",children:["Username: ",o.jsx("span",{className:"font-mono font-semibold",children:"GOD"}),o.jsx("br",{}),"Password: ",o.jsx("span",{className:"font-mono font-semibold",children:"123456"})]})]}),o.jsxs("form",{onSubmit:g,className:"space-y-6",children:[o.jsxs("div",{children:[o.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 mb-2",children:"Username"}),o.jsxs("div",{className:"relative",children:[o.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:o.jsx($t,{className:"h-5 w-5 text-gray-400"})}),o.jsx("input",{id:"username",type:"text",value:n,onChange:x=>r(x.target.value),className:"fa-input pl-10 w-full",placeholder:"Enter your username",required:!0})]})]}),o.jsxs("div",{children:[o.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),o.jsxs("div",{className:"relative",children:[o.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:o.jsx(tr,{className:"h-5 w-5 text-gray-400"})}),o.jsx("input",{id:"password",type:a?"text":"password",value:s,onChange:x=>i(x.target.value),className:"fa-input pl-10 w-full pr-12",placeholder:"Enter your password",required:!0}),o.jsx("button",{type:"button",onClick:()=>l(!a),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600",children:a?o.jsx(hi,{className:"h-5 w-5"}):o.jsx(mi,{className:"h-5 w-5"})})]})]}),o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsxs("div",{className:"flex items-center",children:[o.jsx("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),o.jsx("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-700",children:"Remember me"})]}),o.jsx("div",{className:"text-sm",children:o.jsx("button",{type:"button",onClick:t,className:"font-medium text-blue-600 hover:text-blue-500",children:"Forgot password?"})})]}),o.jsx(E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:c,className:"w-full fa-button-primary py-3 px-4 rounded-lg font-medium text-white shadow-lg disabled:opacity-50 disabled:cursor-not-allowed",children:c?o.jsxs("div",{className:"flex items-center justify-center",children:[o.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Signing in..."]}):"Sign in"})]}),o.jsx("div",{className:"mt-6 text-center",children:o.jsxs("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",o.jsx("button",{onClick:e,className:"font-medium text-blue-600 hover:text-blue-500",children:"Sign up"})]})})]})})},nT=({onSwitchToLogin:e})=>{const[t,n]=w.useState(""),[r,s]=w.useState(""),[i,a]=w.useState(""),[l,c]=w.useState(""),[u,d]=w.useState(!1),[h,f]=w.useState(!1),[g,x]=w.useState(!1),[v,S]=w.useState(null),{register:p}=ls(),m=async y=>{if(y.preventDefault(),i!==l){S("Passwords do not match");return}x(!0),S(null);try{await p(t,i,r)}catch(b){S(b instanceof Error?b.message:"Registration failed")}finally{x(!1)}};return o.jsx(E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"w-full max-w-md",children:o.jsxs("div",{className:"fa-glass-panel-frosted rounded-2xl p-8 shadow-xl",children:[o.jsxs("div",{className:"text-center mb-8",children:[o.jsx(E.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"w-16 h-16 bg-gradient-to-br from-blue-400 to-aqua-400 rounded-full flex items-center justify-center mx-auto mb-4",children:o.jsx($t,{className:"w-8 h-8 text-white"})}),o.jsx("h2",{className:"fa-heading-2 text-gray-800 mb-2",children:"Create Account"}),o.jsx("p",{className:"fa-body text-gray-600",children:"Join our todo application"})]}),v&&o.jsx(E.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-6 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm",children:v}),o.jsxs("form",{onSubmit:m,className:"space-y-6",children:[o.jsxs("div",{children:[o.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 mb-2",children:"Username"}),o.jsxs("div",{className:"relative",children:[o.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:o.jsx($t,{className:"h-5 w-5 text-gray-400"})}),o.jsx("input",{id:"username",type:"text",value:t,onChange:y=>n(y.target.value),className:"fa-input pl-10 w-full",placeholder:"Choose a username",required:!0})]})]}),o.jsxs("div",{children:[o.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),o.jsxs("div",{className:"relative",children:[o.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:o.jsx(Dd,{className:"h-5 w-5 text-gray-400"})}),o.jsx("input",{id:"email",type:"email",value:r,onChange:y=>s(y.target.value),className:"fa-input pl-10 w-full",placeholder:"Enter your email"})]})]}),o.jsxs("div",{children:[o.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),o.jsxs("div",{className:"relative",children:[o.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:o.jsx(tr,{className:"h-5 w-5 text-gray-400"})}),o.jsx("input",{id:"password",type:u?"text":"password",value:i,onChange:y=>a(y.target.value),className:"fa-input pl-10 w-full pr-12",placeholder:"Create a strong password",required:!0}),o.jsx("button",{type:"button",onClick:()=>d(!u),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600",children:u?o.jsx(hi,{className:"h-5 w-5"}):o.jsx(mi,{className:"h-5 w-5"})})]})]}),o.jsxs("div",{children:[o.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm Password"}),o.jsxs("div",{className:"relative",children:[o.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:o.jsx(tr,{className:"h-5 w-5 text-gray-400"})}),o.jsx("input",{id:"confirmPassword",type:h?"text":"password",value:l,onChange:y=>c(y.target.value),className:"fa-input pl-10 w-full pr-12",placeholder:"Confirm your password",required:!0}),o.jsx("button",{type:"button",onClick:()=>f(!h),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600",children:h?o.jsx(hi,{className:"h-5 w-5"}):o.jsx(mi,{className:"h-5 w-5"})})]})]}),o.jsx(E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:g,className:"w-full fa-button-primary py-3 px-4 rounded-lg font-medium text-white shadow-lg disabled:opacity-50 disabled:cursor-not-allowed",children:g?o.jsxs("div",{className:"flex items-center justify-center",children:[o.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Creating account..."]}):"Create Account"})]}),o.jsx("div",{className:"mt-6 text-center",children:o.jsxs("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",o.jsx("button",{onClick:e,className:"font-medium text-blue-600 hover:text-blue-500",children:"Sign in"})]})})]})})},rT=({onBackToLogin:e})=>{const[t,n]=w.useState(""),[r,s]=w.useState(""),[i,a]=w.useState(""),[l,c]=w.useState(!1),[u,d]=w.useState(!1),[h,f]=w.useState("request"),[g,x]=w.useState(!1),[v,S]=w.useState(null),[p,m]=w.useState(null),y=async j=>{j.preventDefault(),x(!0),S(null),m(null);try{await new Promise(k=>setTimeout(k,1e3)),f("reset"),m("Reset instructions sent to your email")}catch{S("Failed to send reset instructions")}finally{x(!1)}},b=async j=>{if(j.preventDefault(),r!==i){S("Passwords do not match");return}if(r.length<12){S("Password must be at least 12 characters long");return}x(!0),S(null),m(null);try{await new Promise(k=>setTimeout(k,1e3)),m("Password reset successfully")}catch{S("Failed to reset password")}finally{x(!1)}};return o.jsx(E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"w-full max-w-md",children:o.jsxs("div",{className:"fa-glass-panel-frosted rounded-2xl p-8 shadow-xl",children:[o.jsxs("div",{className:"text-center mb-8",children:[o.jsx(E.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"w-16 h-16 bg-gradient-to-br from-blue-400 to-aqua-400 rounded-full flex items-center justify-center mx-auto mb-4",children:o.jsx(tr,{className:"w-8 h-8 text-white"})}),o.jsx("h2",{className:"fa-heading-2 text-gray-800 mb-2",children:h==="request"?"Reset Password":"Set New Password"}),o.jsx("p",{className:"fa-body text-gray-600",children:h==="request"?"Enter your username to receive reset instructions":"Enter your new password"})]}),v&&o.jsx(E.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-6 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm",children:v}),p&&o.jsx(E.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-6 p-3 bg-green-50 border border-green-200 rounded-lg text-green-700 text-sm",children:p}),h==="request"?o.jsxs("form",{onSubmit:y,className:"space-y-6",children:[o.jsxs("div",{children:[o.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 mb-2",children:"Username"}),o.jsxs("div",{className:"relative",children:[o.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:o.jsx($t,{className:"h-5 w-5 text-gray-400"})}),o.jsx("input",{id:"username",type:"text",value:t,onChange:j=>n(j.target.value),className:"fa-input pl-10 w-full",placeholder:"Enter your username",required:!0})]})]}),o.jsx(E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:g,className:"w-full fa-button-primary py-3 px-4 rounded-lg font-medium text-white shadow-lg disabled:opacity-50 disabled:cursor-not-allowed",children:g?o.jsxs("div",{className:"flex items-center justify-center",children:[o.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Sending instructions..."]}):"Send Reset Instructions"})]}):o.jsxs("form",{onSubmit:b,className:"space-y-6",children:[o.jsxs("div",{children:[o.jsx("label",{htmlFor:"newPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"New Password"}),o.jsxs("div",{className:"relative",children:[o.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:o.jsx(tr,{className:"h-5 w-5 text-gray-400"})}),o.jsx("input",{id:"newPassword",type:l?"text":"password",value:r,onChange:j=>s(j.target.value),className:"fa-input pl-10 w-full pr-12",placeholder:"Enter new password",required:!0}),o.jsx("button",{type:"button",onClick:()=>c(!l),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600",children:l?o.jsx(hi,{className:"h-5 w-5"}):o.jsx(mi,{className:"h-5 w-5"})})]})]}),o.jsxs("div",{children:[o.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm New Password"}),o.jsxs("div",{className:"relative",children:[o.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:o.jsx(tr,{className:"h-5 w-5 text-gray-400"})}),o.jsx("input",{id:"confirmPassword",type:u?"text":"password",value:i,onChange:j=>a(j.target.value),className:"fa-input pl-10 w-full pr-12",placeholder:"Confirm new password",required:!0}),o.jsx("button",{type:"button",onClick:()=>d(!u),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600",children:u?o.jsx(hi,{className:"h-5 w-5"}):o.jsx(mi,{className:"h-5 w-5"})})]})]}),o.jsx(E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:g,className:"w-full fa-button-primary py-3 px-4 rounded-lg font-medium text-white shadow-lg disabled:opacity-50 disabled:cursor-not-allowed",children:g?o.jsxs("div",{className:"flex items-center justify-center",children:[o.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Resetting password..."]}):"Reset Password"})]}),o.jsx("div",{className:"mt-6 text-center",children:o.jsxs("button",{onClick:e,className:"flex items-center justify-center mx-auto text-sm text-blue-600 hover:text-blue-500",children:[o.jsx(Jj,{className:"w-4 h-4 mr-1"}),"Back to login"]})})]})})},sT=()=>{const[e,t]=w.useState("login"),n=()=>{t("register")},r=()=>{t("login")},s=()=>{t("reset")},i=()=>{t("login")};return o.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-aqua-50 to-blue-100 p-4 sm:p-6 lg:p-8",children:o.jsx("div",{className:"w-full max-w-md mx-auto",children:o.jsx(re,{mode:"wait",children:o.jsxs(E.div,{initial:{opacity:0,x:e==="login"?-20:e==="register"?0:20},animate:{opacity:1,x:0},exit:{opacity:0,x:e==="login"?20:e==="register"?0:-20},transition:{duration:.3},className:"w-full",children:[e==="login"&&o.jsx(tT,{onSwitchToRegister:n,onSwitchToReset:s}),e==="register"&&o.jsx(nT,{onSwitchToLogin:r}),e==="reset"&&o.jsx(rT,{onBackToLogin:i})]},e)})})})},iT=({children:e})=>{const{isAuthenticated:t,loading:n}=ls();return n?o.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-aqua-50 to-blue-100",children:o.jsxs(E.div,{initial:{opacity:0},animate:{opacity:1},className:"text-center",children:[o.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"}),o.jsx("p",{className:"text-gray-600",children:"Loading..."})]})}):t?o.jsx(o.Fragment,{children:e}):o.jsx(sT,{})},aT=()=>{const[e,t]=w.useState(!0),[n,r]=w.useState(null),s=Vx();return w.useEffect(()=>{(async()=>{try{if(FC(),window.electronAPI){const a=await window.electronAPI.system.getInfo();r(a)}await new Promise(a=>setTimeout(a,1500)),t(!1)}catch(a){console.error("Failed to initialize app:",a),s.error("Initialization Failed","Failed to initialize the application. Please try restarting."),t(!1)}})()},[]),e?o.jsx(Jk,{}):o.jsxs(eT,{children:[o.jsx(bN,{children:o.jsx(CN,{children:o.jsx(TN,{children:o.jsx(iT,{children:o.jsx(re,{mode:"wait",children:o.jsx(E.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.6,ease:[.4,0,.2,1]},className:"w-full h-full",children:o.jsx(Zk,{systemInfo:n})},"main-app")})})})})}),o.jsx(Ck,{toasts:s.toasts,onDismiss:s.dismissToast,position:"top-right"})]})},Ux=document.getElementById("root");if(!Ux)throw new Error("Root element not found");Fl.createRoot(Ux).render(o.jsx(Z.StrictMode,{children:o.jsx(aT,{})}));export{fT as a,dT as b,Lk as g,uT as s};
